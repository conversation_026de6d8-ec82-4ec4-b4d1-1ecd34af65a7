// 物业端-报修工单管理页面
import {handlePropertyPageShow} from '../../../utils/pageUtils.js'

const app = getApp()

Page({
  data: {
    activeTab: 0,
    tabs: [
      { key: 'pending', title: '待办' },
      { key: 'completed', title: '已办' }
    ],
    repairList: [],
    loading: false,
    refreshing: false,
    hasMore: true,
    pageNum: 1,
    pageSize: 10
  },

  onLoad(options) {
    console.log('报修工单管理页面加载')
  },

  onShow() {
    handlePropertyPageShow(this, this.loadCurrentTabData)
  },

  // 加载当前Tab的数据
  loadCurrentTabData() {
    this.loadRepairList(true)
  },

  // Tab切换
  onTabChange(e) {
    const index = e.detail.index
    this.setData({
      activeTab: index,
      pageNum: 1,
      hasMore: true,
      repairList: []
    })
    this.loadCurrentTabData()
  },

  // 加载报修列表
  async loadRepairList(refresh = false) {
    if (this.data.loading) return

    this.setData({ loading: true })
    if (refresh) {
      this.setData({ pageNum: 1, hasMore: true })
    }

    try {
      const currentTab = this.data.tabs[this.data.activeTab]
      let statusFilter = null
      
      // 已办状态过滤
      if (currentTab.key === 'completed') {
        statusFilter = 2 // 已完成
      }

      const requestData = {
        pageNum: this.data.pageNum,
        pageSize: this.data.pageSize
      }

      // 只有已办时才传status参数
      if (statusFilter) {
        requestData.status = statusFilter
      }

      const res = await app.request({
        url: '/api/wx/property/repair/list',
        method: 'POST',
        data: requestData
      })

      if (res.code === 0) {
        let newList = res.data.list || []

        // 如果是待办，需要前端过滤出status=0或status=1的数据
        if (currentTab.key === 'pending') {
          newList = newList.filter(item => item.status == 0 || item.status == 1)
        }

        this.setData({
          repairList: refresh ? newList : [...this.data.repairList, ...newList],
          hasMore: newList.length === this.data.pageSize,
          pageNum: this.data.pageNum + 1
        })
      } else {
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载报修列表失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false, refreshing: false })
    }
  },

  // 查看详情
  viewDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/property/repair/detail?id=${id}`
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ refreshing: true })
    this.loadCurrentTabData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadCurrentTabData()
    }
  }
})
