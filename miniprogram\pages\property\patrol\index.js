// 巡更任务页面
import {handlePropertyPageShow} from '../../../utils/pageUtils.js'

const app = getApp()

Page({
  data: {
    title: '巡更任务',
    loading: false,
    refreshing: false,
    monthlyStats: {
      patrolDays: 0,
      abnormalCount: 0
    },
    todayStats: {
      totalTasks: 0,
      completedTasks: 0,
      pendingTasks: 0,
      completionRate: 0
    },
    historyData: []
  },

  onLoad() {
    console.log('巡更任务页面加载')
    wx.setNavigationBarTitle({
      title: this.data.title
    })
  },

  onShow() {
    handlePropertyPageShow(this, this.loadAllData)
  },

  // 加载所有数据
  async loadAllData() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      // 并行加载三个接口的数据
      const [monthlyRes, todayRes, historyRes] = await Promise.all([
        app.request({
          url: '/api/wx/patrol/getMonthlyStats',
          method: 'POST'
        }),
        app.request({
          url: '/api/wx/patrol/getTodayTasks',
          method: 'POST'
        }),
        app.request({
          url: '/api/wx/patrol/getHistoryByDate',
          method: 'POST'
        })
      ])

      // 处理月度统计数据
      if (monthlyRes.code === 0) {
        this.setData({
          monthlyStats: monthlyRes.data
        })
      }

      // 处理今日任务数据
      if (todayRes.code === 0) {
        this.setData({
          todayStats: {
            totalTasks: todayRes.data.totalTasks || 0,
            completedTasks: todayRes.data.completedTasks || 0,
            pendingTasks: todayRes.data.pendingTasks || 0,
            completionRate: todayRes.data.completionRate || 0
          }
        })
      }

      // 处理历史数据
      if (historyRes.code === 0) {
        this.setData({
          historyData: historyRes.data || []
        })
      }

      // 如果有任何接口失败，显示错误信息
      if (monthlyRes.code !== 0 || todayRes.code !== 0 || historyRes.code !== 0) {
        const errorMsg = monthlyRes.msg || todayRes.msg || historyRes.msg || '加载失败'
        wx.showToast({
          title: errorMsg,
          icon: 'none'
        })
      }

    } catch (error) {
      console.error('加载巡更数据失败:', error)
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      })
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      })
    }
  },

  // 查看本月巡更详情
  viewMonthlyPatrol() {
    wx.navigateTo({
      url: '/pages/property/patrol/monthly?type=patrol'
    })
  },

  // 查看本月异常详情
  viewMonthlyAbnormal() {
    wx.navigateTo({
      url: '/pages/property/patrol/monthly?type=abnormal'
    })
  },

  // 查看今日任务详情
  viewTodayTasks() {
    wx.navigateTo({
      url: '/pages/property/patrol/tasks'
    })
  },

  // 查看历史详情
  viewHistoryDetail(e) {
    const date = e.currentTarget.dataset.date
    wx.navigateTo({
      url: `/pages/property/patrol/tasks?date=${date}`
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ refreshing: true })
    this.loadAllData().then(() => {
      wx.stopPullDownRefresh()
    })
  }
})
