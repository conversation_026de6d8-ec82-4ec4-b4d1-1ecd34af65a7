# 车牌识别功能测试清单

## 部署前检查

### 1. 数据库准备
- [ ] 执行 `sql/vehicle_recognition_optimization.sql` 
- [ ] 执行 `sql/plate_recognition_log.sql`
- [ ] 确认 `eh_vehicle` 表有测试数据
- [ ] 确认 `eh_plate_recognition_log` 表创建成功

### 2. 配置文件
- [ ] 百度AI配置已添加到 `application.yml`
- [ ] 百度AI密钥已正确配置
- [ ] 菜单配置已添加（车牌识别挪车）

### 3. 代码部署
- [ ] 后端代码已编译部署
- [ ] 小程序代码已上传
- [ ] van-uploader组件配置正确

## 功能测试

### 1. 基础功能测试

#### 文件上传测试
- [ ] 点击"选择图片"能正常打开相册
- [ ] 拍照功能正常工作
- [ ] 图片上传成功，返回fileId
- [ ] 上传的图片能正常预览
- [ ] 删除图片功能正常

#### 车牌识别测试
- [ ] 上传车牌图片后自动开始识别
- [ ] 识别过程显示"识别中..."状态
- [ ] 识别成功显示车牌号和置信度
- [ ] 识别失败显示错误信息

#### 车主查询测试
- [ ] 识别成功后自动查询车主信息
- [ ] 找到车主时显示完整信息
- [ ] 未找到车主时显示提示信息

#### 拨号功能测试
- [ ] 点击车主电话能正常拨号
- [ ] "联系车主挪车"按钮正常工作

### 2. 后台管理测试

#### 记录查看测试
- [ ] 后台能正常访问识别记录页面
- [ ] 记录列表正常显示
- [ ] 搜索和筛选功能正常
- [ ] 记录详情页面正常显示

#### 统计功能测试
- [ ] 统计数据正常显示
- [ ] 今日/本月统计准确
- [ ] 成功率统计正确

### 3. 异常情况测试

#### 错误处理测试
- [ ] 网络异常时的错误提示
- [ ] 文件格式错误时的提示
- [ ] 文件大小超限时的提示
- [ ] 百度AI服务异常时的处理

#### 边界条件测试
- [ ] 上传非图片文件的处理
- [ ] 上传损坏图片的处理
- [ ] 识别模糊车牌的处理
- [ ] 识别非车牌图片的处理

## 性能测试

### 1. 响应时间测试
- [ ] 文件上传时间 < 5秒
- [ ] 车牌识别时间 < 10秒
- [ ] 车主查询时间 < 2秒

### 2. 并发测试
- [ ] 多用户同时使用功能正常
- [ ] 高并发时系统稳定

## 数据验证

### 1. 记录完整性
- [ ] 每次识别都有记录保存
- [ ] 记录信息完整准确
- [ ] 文件ID正确关联

### 2. 数据一致性
- [ ] 文件信息与记录一致
- [ ] 车主信息查询准确
- [ ] 统计数据计算正确

## 用户体验测试

### 1. 界面测试
- [ ] 页面布局美观
- [ ] 操作流程清晰
- [ ] 提示信息友好

### 2. 交互测试
- [ ] 按钮响应及时
- [ ] 加载状态明确
- [ ] 错误提示清楚

## 安全测试

### 1. 权限测试
- [ ] 未登录用户无法访问
- [ ] 用户只能查看自己社区的数据
- [ ] 文件访问权限正确

### 2. 数据安全测试
- [ ] 敏感信息不在日志中显示
- [ ] 文件上传安全验证
- [ ] API接口安全防护

## 兼容性测试

### 1. 设备兼容性
- [ ] iOS设备正常工作
- [ ] Android设备正常工作
- [ ] 不同屏幕尺寸适配

### 2. 网络兼容性
- [ ] WiFi环境正常
- [ ] 4G/5G环境正常
- [ ] 弱网环境处理

## 测试数据准备

### 测试车牌图片
准备以下类型的测试图片：
- [ ] 清晰的蓝牌车牌
- [ ] 清晰的绿牌车牌
- [ ] 清晰的黄牌车牌
- [ ] 模糊的车牌图片
- [ ] 倾斜角度的车牌
- [ ] 非车牌图片

### 测试车辆数据
确保数据库中有以下测试数据：
- [ ] 有车主信息的车辆
- [ ] 无车主信息的车辆
- [ ] 不同车牌类型的车辆

## 测试结果记录

### 功能测试结果
- 测试时间：_______
- 测试人员：_______
- 通过项目：___ / ___
- 失败项目：_______

### 性能测试结果
- 平均识别时间：_______
- 平均上传时间：_______
- 识别成功率：_______

### 问题记录
1. _______
2. _______
3. _______

## 上线准备

### 最终检查
- [ ] 所有测试项目通过
- [ ] 性能指标达标
- [ ] 安全检查通过
- [ ] 用户手册准备完成

### 监控配置
- [ ] 错误日志监控
- [ ] 性能指标监控
- [ ] 使用量统计监控

功能测试完成后，即可正式上线使用！
