<view class="patrol-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <van-loading type="spinner" size="24px" />
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else>
    <!-- 统计卡片区域 -->
    <view class="stats-cards">
      <view class="stats-card orange" bindtap="viewMonthlyPatrol">
        <view class="stats-content">
          <view class="stats-title">本月已巡查</view>
          <view class="stats-value">{{monthlyStats.patrolDays}}天</view>
        </view>
        <view class="stats-action">查看</view>
      </view>

      <view class="stats-card blue" bindtap="viewMonthlyAbnormal">
        <view class="stats-content">
          <view class="stats-title">本月异常次数</view>
          <view class="stats-value">{{monthlyStats.abnormalCount}}次</view>
        </view>
        <view class="stats-action">查看</view>
      </view>
    </view>

    <!-- 今日任务区域 -->
    <view class="today-section">
      <view class="section-title">今日任务</view>

      <view class="today-task-card" bindtap="viewTodayTasks">
        <view class="task-progress">
          <text class="progress-text">共{{todayStats.totalTasks}}项需巡查</text>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{todayStats.completionRate}}%"></view>
          </view>
          <text class="progress-ratio">{{todayStats.completedTasks}}/{{todayStats.totalTasks}}</text>
        </view>

        <view class="start-patrol-btn">
          开始巡查
        </view>
      </view>
    </view>

    <!-- 往日任务区域 -->
    <view class="history-section">
      <view class="section-title">往日任务</view>

      <view wx:for="{{historyData}}" wx:key="date" class="history-item">
        <view class="history-date">
          <van-icon name="clock-o" size="16px" color="#666" />
          <text class="date-text">{{item.date}}</text>
        </view>

        <view class="history-stats">
          <view class="stat-item">
            <text class="stat-label">完成</text>
            <text class="stat-value completed">{{item.completedTasks}} 项</text>
          </view>

          <view class="stat-item">
            <text class="stat-label">未完成</text>
            <text class="stat-value pending">{{item.pendingTasks}} 项</text>
          </view>

          <view class="history-action" bindtap="viewHistoryDetail" data-date="{{item.date}}">
            查看详情
          </view>
        </view>
      </view>

      <!-- 历史数据为空 -->
      <view wx:if="{{historyData.length === 0}}" class="empty-state">
        <van-icon name="smile-o" size="80rpx" color="#ddd" />
        <text class="empty-text">暂无历史数据</text>
      </view>
    </view>
  </view>
</view>
