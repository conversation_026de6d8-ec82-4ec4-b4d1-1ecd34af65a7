// 巡更记录详情页面
const app = getApp()

Page({
  data: {
    recordId: '',
    recordInfo: null,
    loading: false,
    photoList: []
  },

  onLoad(options) {
    const recordId = options.recordId
    if (!recordId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ recordId })
    this.loadRecordDetail()
  },

  // 加载巡更记录详情
  async loadRecordDetail() {
    this.setData({ loading: true })
    
    try {
      const res = await app.request({
        url: `/api/wx/patrol/getTaskDetail/${this.data.recordId}`,
        method: 'GET'
      })

      if (res.code === 0) {
        const recordInfo = res.data
        this.setData({ recordInfo })
        
        wx.setNavigationBarTitle({
          title: `巡更详情 - ${recordInfo.location_name}`
        })
        
        // 解析照片列表
        this.parsePhotoList(recordInfo.photo_file_ids)
      } else {
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('加载巡更记录详情失败:', error)
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 解析照片列表
  async parsePhotoList(photoFileIds) {
    if (!photoFileIds) {
      return
    }
    
    try {
      const fileIds = JSON.parse(photoFileIds)
      if (Array.isArray(fileIds) && fileIds.length > 0) {
        // 获取照片URL
        const photoPromises = fileIds.map(fileId => this.getPhotoUrl(fileId))
        const photoList = await Promise.all(photoPromises)
        this.setData({ photoList: photoList.filter(photo => photo) })
      }
    } catch (error) {
      console.error('解析照片列表失败:', error)
    }
  },

  // 获取照片URL
  async getPhotoUrl(fileId) {
    try {
      const res = await app.request({
        url: `/api/wx/file/getUrl/${fileId}`,
        method: 'GET'
      })
      
      if (res.code === 0) {
        return {
          fileId: fileId,
          url: res.data.url || res.data
        }
      }
    } catch (error) {
      console.error('获取照片URL失败:', error)
    }
    return null
  },

  // 预览照片
  previewPhoto(e) {
    const index = e.currentTarget.dataset.index
    const urls = this.data.photoList.map(photo => photo.url)
    
    wx.previewImage({
      current: urls[index],
      urls: urls
    })
  },

  // 查看位置
  viewLocation() {
    const { recordInfo } = this.data
    
    if (!recordInfo || !recordInfo.latitude || !recordInfo.longitude) {
      wx.showToast({
        title: '暂无位置信息',
        icon: 'none'
      })
      return
    }
    
    wx.openLocation({
      latitude: parseFloat(recordInfo.latitude),
      longitude: parseFloat(recordInfo.longitude),
      name: recordInfo.location_name,
      address: recordInfo.location_address || '',
      scale: 18
    })
  },

  // 格式化时间显示
  formatTime(timeStr) {
    if (!timeStr) return '未记录'
    return timeStr.substring(0, 16) // 显示到分钟
  },

  // 获取状态文本
  getStatusText(status) {
    switch (status) {
      case 0: return '待巡更'
      case 1: return '已完成'
      case 2: return '已过期'
      default: return '未知'
    }
  },

  // 获取状态样式
  getStatusClass(status) {
    switch (status) {
      case 0: return 'status-pending'
      case 1: return 'status-completed'
      case 2: return 'status-overdue'
      default: return 'status-unknown'
    }
  },

  // 计算距离偏差
  getDistanceText(distance) {
    if (!distance || distance === 0) return '位置正常'
    if (distance <= 50) return `偏差${Math.round(distance)}米`
    return `偏差${Math.round(distance)}米（超出范围）`
  },

  // 获取距离样式
  getDistanceClass(distance, allowedRange) {
    if (!distance || distance === 0) return 'distance-normal'
    if (distance <= (allowedRange || 100)) return 'distance-normal'
    return 'distance-warning'
  }
})
