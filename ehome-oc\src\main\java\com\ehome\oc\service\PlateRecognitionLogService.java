package com.ehome.oc.service;

import com.ehome.oc.domain.PlateRecognitionLogRecord;

/**
 * 车牌识别记录服务接口
 * 
 * <AUTHOR>
 */
public interface PlateRecognitionLogService {
    
    /**
     * 保存识别记录
     * 
     * @param logRecord 识别记录
     * @return 是否保存成功
     */
    boolean saveRecognitionLog(PlateRecognitionLogRecord logRecord);
    
    /**
     * 创建识别记录对象
     * 
     * @param communityId 社区ID
     * @param userId 用户ID
     * @param userName 用户姓名
     * @param userPhone 用户手机号
     * @return 识别记录对象
     */
    PlateRecognitionLogRecord createLogRecord(String communityId, String userId, String userName, String userPhone);
}
