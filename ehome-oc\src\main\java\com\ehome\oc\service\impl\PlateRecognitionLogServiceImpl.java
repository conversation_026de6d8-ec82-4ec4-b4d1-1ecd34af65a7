package com.ehome.oc.service.impl;

import com.ehome.common.utils.uuid.Seq;
import com.ehome.oc.domain.PlateRecognitionLogRecord;
import com.ehome.oc.service.PlateRecognitionLogService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 车牌识别记录服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class PlateRecognitionLogServiceImpl implements PlateRecognitionLogService {
    
    private static final Logger logger = LoggerFactory.getLogger(PlateRecognitionLogServiceImpl.class);
    
    @Override
    public boolean saveRecognitionLog(PlateRecognitionLogRecord logRecord) {
        try {
            Record record = new Record();
            record.set("log_id", logRecord.getLogId());
            record.set("community_id", logRecord.getCommunityId());
            record.set("user_id", logRecord.getUserId());
            record.set("user_name", logRecord.getUserName());
            record.set("user_phone", logRecord.getUserPhone());
            record.set("file_id", logRecord.getFileId());
            record.set("plate_number", logRecord.getPlateNumber());
            record.set("recognition_status", logRecord.getRecognitionStatus());
            record.set("confidence", logRecord.getConfidence());
            record.set("plate_color", logRecord.getPlateColor());
            record.set("owner_found", logRecord.getOwnerFound());
            record.set("owner_name", logRecord.getOwnerName());
            record.set("owner_phone", logRecord.getOwnerPhone());
            record.set("image_size", logRecord.getImageSize());
            record.set("recognition_time", logRecord.getRecognitionTime());
            record.set("error_message", logRecord.getErrorMessage());
            record.set("create_time", logRecord.getCreateTime());
            record.set("ip_address", logRecord.getIpAddress());
            
            boolean success = Db.save("eh_plate_recognition_log", "log_id", record);
            
            if (success) {
                logger.info("保存车牌识别记录成功: 用户={}, 车牌号={}, 状态={}", 
                    logRecord.getUserName(), logRecord.getPlateNumber(), 
                    logRecord.getRecognitionStatus() == 1 ? "成功" : "失败");
            } else {
                logger.error("保存车牌识别记录失败: 用户={}, 车牌号={}", 
                    logRecord.getUserName(), logRecord.getPlateNumber());
            }
            
            return success;
            
        } catch (Exception e) {
            logger.error("保存车牌识别记录异常", e);
            return false;
        }
    }
    
    @Override
    public PlateRecognitionLogRecord createLogRecord(String communityId, String userId, String userName, String userPhone) {
        PlateRecognitionLogRecord logRecord = new PlateRecognitionLogRecord();
        logRecord.setLogId(Seq.getId());
        logRecord.setCommunityId(communityId);
        logRecord.setUserId(userId);
        logRecord.setUserName(userName);
        logRecord.setUserPhone(userPhone);
        
        // 设置默认值
        logRecord.setRecognitionStatus(0); // 默认失败
        logRecord.setOwnerFound(0); // 默认未找到车主
        
        return logRecord;
    }
}
