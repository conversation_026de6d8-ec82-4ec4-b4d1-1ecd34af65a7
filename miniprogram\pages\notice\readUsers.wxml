<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">{{title}} - 已读房屋</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <van-loading size="24px" vertical>加载中...</van-loading>
  </view>

  <!-- 已读用户列表 -->
  <view class="read-users-container" wx:else>
    <view class="read-users-list">
      <view wx:for="{{readUsersList}}" wx:key="index" class="read-user-item">
        <view class="read-user-house">{{item.houseName}}</view>
        <view class="read-user-time">{{item.readTimeFormatted}}</view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{readUsersList.length === 0}}">
      <van-icon name="eye-o" size="80rpx" color="#ccc" />
      <text class="empty-text">暂无已读记录</text>
    </view>
  </view>
</view>
