package com.ehome.oc.service.impl;

import com.baidu.aip.ocr.AipOcr;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.StringUtils;
import com.ehome.oc.config.BaiduAiConfig;
import com.ehome.oc.service.PlateRecognitionService;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;

/**
 * 车牌识别服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class PlateRecognitionServiceImpl implements PlateRecognitionService {
    
    private static final Logger logger = LoggerFactory.getLogger(PlateRecognitionServiceImpl.class);
    
    @Autowired
    private BaiduAiConfig baiduAiConfig;
    
    /**
     * 获取百度OCR客户端
     */
    private AipOcr getAipOcr() {
        return new AipOcr(baiduAiConfig.getAppId(), baiduAiConfig.getApiKey(), baiduAiConfig.getSecretKey());
    }
    
    @Override
    public AjaxResult recognizePlate(byte[] imageBytes) {
        if (!baiduAiConfig.isEnabled()) {
            return AjaxResult.error("百度AI服务未启用");
        }
        
        if (imageBytes == null || imageBytes.length == 0) {
            return AjaxResult.error("图片数据为空");
        }
        
        try {
            AipOcr client = getAipOcr();
            
            // 调用车牌识别接口
            HashMap<String, String> options = new HashMap<>();
            options.put("multi_detect", "false"); // 是否检测多张车牌，默认为false
            
            JSONObject response = client.plateLicense(imageBytes, options);

            logger.info("百度AI车牌识别响应: {}", response.toString());

            return parseRecognitionResult(response);
            
        } catch (Exception e) {
            logger.error("车牌识别失败", e);
            return AjaxResult.error("车牌识别失败：" + e.getMessage());
        }
    }
    
    @Override
    public AjaxResult recognizePlateByPath(String imagePath) {
        if (StringUtils.isEmpty(imagePath)) {
            return AjaxResult.error("图片路径为空");
        }
        
        try {
            byte[] imageBytes = Files.readAllBytes(Paths.get(imagePath));
            return recognizePlate(imageBytes);
        } catch (IOException e) {
            logger.error("读取图片文件失败：" + imagePath, e);
            return AjaxResult.error("读取图片文件失败");
        }
    }
    
    /**
     * 解析识别结果
     */
    private AjaxResult parseRecognitionResult(JSONObject response) {
        try {
            // 检查是否有错误
            if (response.has("error_code")) {
                String errorMsg = response.optString("error_msg", "未知错误");
                logger.error("百度API返回错误：{}", errorMsg);
                return AjaxResult.error("识别失败：" + errorMsg);
            }

            // 获取识别结果 - 百度车牌识别返回的是对象而不是数组
            JSONObject wordsResult = response.optJSONObject("words_result");
            if (wordsResult == null) {
                return AjaxResult.error("未识别到车牌信息");
            }

            // 获取车牌号
            String plateNumber = wordsResult.optString("number", "");
            if (StringUtils.isEmpty(plateNumber)) {
                return AjaxResult.error("车牌号识别为空");
            }

            // 获取置信度 - 车牌识别返回的是数组，取平均值
            org.json.JSONArray probabilityArray = wordsResult.optJSONArray("probability");
            double probability = 0.0;
            if (probabilityArray != null && probabilityArray.length() > 0) {
                double sum = 0.0;
                for (int i = 0; i < probabilityArray.length(); i++) {
                    sum += probabilityArray.optDouble(i, 0.0);
                }
                probability = sum / probabilityArray.length();
            }

            // 获取车牌颜色
            String color = wordsResult.optString("color", "");

            // 构建返回结果
            HashMap<String, Object> result = new HashMap<>();
            result.put("plateNumber", plateNumber);
            result.put("probability", probability);
            result.put("confidence", Math.round(probability * 100) + "%");
            result.put("color", color);

            logger.info("车牌识别成功：{}, 颜色：{}, 置信度：{}", plateNumber, color, probability);

            return AjaxResult.success("识别成功", result);

        } catch (Exception e) {
            logger.error("解析识别结果失败", e);
            return AjaxResult.error("解析识别结果失败");
        }
    }
}
