package com.ehome.oc.service.charge;

import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.domain.entity.SysUser;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 收费模块公共服务类
 * 用于抽取各个Controller中的重复代码
 */
@Service
public class ChargeCommonService {

    // ==================== 数据设置方法 ====================

    /**
     * 设置创建和更新信息
     */
    public void setCreateAndUpdateInfo(Record record, SysUser user) {
        String now = DateUtils.getTime();
        String loginName = user.getLoginName();
        record.set("community_id", user.getCommunityId());
        record.set("created_at", now);
        record.set("updated_at", now);
        record.set("created_by", loginName);
        record.set("updated_by", loginName);
        record.set("is_deleted", false);
        record.set("is_active", 1);
    }

    /**
     * 设置更新信息
     */
    public void setUpdateInfo(Record record, SysUser user) {
        record.set("updated_at", DateUtils.getTime());
        record.set("updated_by", user.getLoginName());
    }

    /**
     * 设置创建和更新信息（兼容旧版本方法名）
     */
    public void setCreateAndUpdateInfo(Record record, String loginName, String communityId) {
        String now = DateUtils.getTime();
        record.set("community_id", communityId);
        record.set("create_time", now);
        record.set("update_time", now);
        record.set("create_by", loginName);
        record.set("update_by", loginName);
        record.set("is_active", 1);
    }

    /**
     * 设置更新信息（兼容旧版本方法名）
     */
    public void setUpdateInfo(Record record, String loginName) {
        record.set("update_time", DateUtils.getTime());
        record.set("update_by", loginName);
    }

    // ==================== 参数验证方法 ====================

    /**
     * 验证字符串ID参数
     */
    public AjaxResult validateId(String id, String fieldName) {
        if (StringUtils.isEmpty(id)) {
            return AjaxResult.error(fieldName + "不能为空");
        }
        return null; // 验证通过返回null
    }

    /**
     * 验证Long类型ID参数
     */
    public AjaxResult validateId(Long id, String fieldName) {
        if (id == null || id <= 0) {
            return AjaxResult.error(fieldName + "不能为空");
        }
        return null; // 验证通过返回null
    }

    /**
     * 验证时间范围
     */
    public AjaxResult validateTimeRange(String startTime, String endTime) {
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            return AjaxResult.error("请选择时间范围");
        }
        return null; // 验证通过返回null
    }

    /**
     * 验证支付方式
     */
    public AjaxResult validatePayType(Integer payType) {
        if (payType == null || payType <= 0) {
            return AjaxResult.error("请选择支付方式");
        }
        return null; // 验证通过返回null
    }

    /**
     * 验证账单ID列表字符串
     */
    public AjaxResult validateBillIds(String billIdsStr) {
        if (StringUtils.isEmpty(billIdsStr)) {
            return AjaxResult.error("请选择要操作的账单");
        }
        return null; // 验证通过返回null
    }

    // ==================== 记录查询和验证方法 ====================

    /**
     * 通用记录查询方法
     */
    public Record getRecordById(String tableName, Long id) {
        if (id == null || id <= 0) {
            return null;
        }
        return Db.findFirst("select * from " + tableName + " where id = ?", id);
    }

    /**
     * 检查记录是否存在
     */
    public boolean checkRecordExists(String tableName, Long id) {
        if (id == null || id <= 0) {
            return false;
        }
        Long count = Db.queryLong("select count(*) from " + tableName + " where id = ?", id);
        return count != null && count > 0;
    }

    /**
     * 获取收费标准详情
     */
    public Record getChargeStandardDetail(Long id) {
        if (id == null || id <= 0) {
            return null;
        }
        return Db.findFirst("select * from eh_charge_standard where id = ? and is_deleted = 0", id);
    }

    /**
     * 获取收费绑定详情
     */
    public Record getBindingDetail(Long id) {
        if (id == null || id <= 0) {
            return null;
        }
        return Db.findFirst(
                "select cb.*, cs.name as charge_standard_name from eh_charge_binding cb " +
                "left join eh_charge_standard cs on cb.charge_standard_id = cs.id " +
                "where cb.id = ?", id);
    }

    /**
     * 获取账单详情
     */
    public Record getBillDetail(Long id) {
        if (id == null || id <= 0) {
            return null;
        }
        return Db.findFirst(
                "SELECT cb.*, cs.name as charge_standard_name, h.combina_name " +
                "FROM eh_charge_bill cb " +
                "LEFT JOIN eh_charge_standard cs ON cb.charge_standard_id = cs.id " +
                "LEFT JOIN eh_house_info h ON cb.asset_id = h.house_id AND cb.asset_type = 1 " +
                "WHERE cb.id = ?", id);
    }

    /**
     * 获取收费标准列表
     */
    public List<Record> getChargeStandards(String communityId) {
        return Db.find(
                "select id, name, charge_type, period_type, count_type " +
                "from eh_charge_standard where is_deleted = 0 and is_active = 1 and is_current = 1 and community_id = ? " +
                "order by created_at desc", communityId);
    }

    /**
     * 更新收费标准的关联资产数
     */
    public void updateChargeStandardAssetCount(Long chargeStandardId) {
        if (chargeStandardId == null || chargeStandardId <= 0) {
            return;
        }

        // 统计该收费标准关联的资产数量
        Long count = Db.queryLong(
                "select count(*) from eh_charge_binding where charge_standard_id = ? and is_active = 1",
                chargeStandardId);

        // 更新收费标准表的关联资产数
        Db.update(
                "update eh_charge_standard set related_asset_count = ? where id = ?",
                count, chargeStandardId);
    }

    /**
     * 将Record列表转换为Map列表
     */
    public java.util.List<java.util.Map<String, Object>> recordToMap(List<Record> records) {
        java.util.List<java.util.Map<String, Object>> result = new java.util.ArrayList<>();
        if (records != null) {
            for (Record record : records) {
                result.add(record.toMap());
            }
        }
        return result;
    }

    // ==================== 业务逻辑验证方法 ====================

    /**
     * 检查重复绑定
     */
    public boolean checkDuplicateBinding(Record binding, SysUser currentUser) {
        Long assetId = binding.getLong("asset_id");
        Integer assetType = binding.getInt("asset_type");

        // 查询是否已存在绑定
        Record existing = Db.findFirst(
                "select id, charge_standard_id from eh_charge_binding " +
                "where asset_id = ? and asset_type = ? and is_active = 1",
                assetId, assetType);

        if (existing != null) {
            Long existingStandardId = existing.getLong("charge_standard_id");
            Long newStandardId = binding.getLong("charge_standard_id");

            // 如果是相同的收费标准，则认为是重复绑定
            if (existingStandardId.equals(newStandardId)) {
                return true;
            }

            // 如果是不同的收费标准，先解绑原来的
            Db.update("update eh_charge_binding set is_active = 0, update_time = now(), update_by = ? where id = ?",
                    currentUser.getUserName(), existing.getLong("id"));

            // 更新原收费标准的关联资产数
            updateChargeStandardAssetCount(existingStandardId);
        }

        return false;
    }

    /**
     * 验证收费绑定是否可以生成账单
     */
    public AjaxResult validateBindingForBillGeneration(Long bindingId) {
        if (bindingId == null || bindingId <= 0) {
            return AjaxResult.error("收费绑定ID不能为空");
        }

        Record binding = Db.findFirst(
                "select cb.*, cs.name as charge_standard_name from eh_charge_binding cb " +
                "left join eh_charge_standard cs on cb.charge_standard_id = cs.id " +
                "where cb.id = ? and cb.is_active = 1 and cs.is_active = 1", bindingId);

        if (binding == null) {
            return AjaxResult.error("收费绑定不存在或已禁用");
        }

        return null; // 验证通过返回null
    }

    /**
     * 验证账单是否可以收款
     */
    public AjaxResult validateBillForPayment(Long billId) {
        if (billId == null || billId <= 0) {
            return AjaxResult.error("账单ID不能为空");
        }

        Record bill = Db.findFirst("select pay_status from eh_charge_bill where id = ?", billId);
        if (bill == null) {
            return AjaxResult.error("账单不存在");
        }

        if (bill.getInt("pay_status") != 0) {
            return AjaxResult.error("账单已收款或已作废");
        }

        return null; // 验证通过返回null
    }

    // ==================== 批量操作方法 ====================

    /**
     * 批量删除记录（软删除）
     */
    public int batchDelete(String tableName, String ids, SysUser user) {
        if (StringUtils.isEmpty(ids)) {
            return 0;
        }

        String[] idArr = ids.split(",");
        int deleteCount = 0;

        for (String id : idArr) {
            Record record = new Record();
            record.set("id", id);
            record.set("is_deleted", 1);
            setUpdateInfo(record, user);

            boolean result = Db.update(tableName, "id", record);
            if (result) {
                deleteCount++;
            }
        }

        return deleteCount;
    }

    /**
     * 批量更新状态
     */
    public int batchUpdateStatus(String tableName, List<Long> ids, int status, SysUser user) {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }

        int updateCount = 0;
        for (Long id : ids) {
            Record record = new Record();
            record.set("id", id);
            record.set("is_active", status);
            setUpdateInfo(record, user);

            boolean result = Db.update(tableName, "id", record);
            if (result) {
                updateCount++;
            }
        }

        return updateCount;
    }

    // ==================== 响应构建方法 ====================

    /**
     * 构建成功响应
     */
    public AjaxResult buildSuccessResponse(String message) {
        return AjaxResult.success(message);
    }

    /**
     * 构建成功响应带数据
     */
    public AjaxResult buildSuccessResponse(String message, Object data) {
        return AjaxResult.success(message, data);
    }

    /**
     * 构建错误响应
     */
    public AjaxResult buildErrorResponse(String message) {
        return AjaxResult.error(message);
    }

    /**
     * 构建错误响应带异常信息
     */
    public AjaxResult buildErrorResponse(String message, Exception e) {
        return AjaxResult.error(message + "：" + e.getMessage());
    }

    // ==================== 工具方法 ====================

    /**
     * 安全执行数据库操作
     */
    public AjaxResult safeExecute(String operation, Runnable action) {
        try {
            action.run();
            return AjaxResult.success(operation + "成功");
        } catch (Exception e) {
            return AjaxResult.error(operation + "失败：" + e.getMessage());
        }
    }

    /**
     * 解析账单ID列表
     */
    public List<Long> parseBillIds(String billIdsStr) {
        List<Long> billIds = new java.util.ArrayList<>();
        if (StringUtils.isNotEmpty(billIdsStr)) {
            String[] idArray = billIdsStr.split(",");
            for (String id : idArray) {
                try {
                    billIds.add(Long.parseLong(id.trim()));
                } catch (NumberFormatException e) {
                    // 忽略格式错误的ID
                }
            }
        }
        return billIds;
    }
}
