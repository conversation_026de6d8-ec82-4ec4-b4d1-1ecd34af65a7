# 车牌识别功能验证清单

## 快速验证步骤

### 1. 基础功能验证（5分钟）

#### ✅ 文件上传验证
- [ ] 打开小程序车牌识别页面
- [ ] 点击"选择图片"上传车牌照片
- [ ] 确认上传成功，返回fileId
- [ ] 检查上传日志：`/api/wx/file/upload` 返回 code:0

#### ✅ 车牌识别验证  
- [ ] 上传成功后自动开始识别
- [ ] 确认识别接口调用成功：`/api/wx/vehicle/recognizePlate`
- [ ] 检查识别结果显示车牌号和置信度
- [ ] 验证百度AI响应正常

#### ✅ 车主查询验证
- [ ] 识别成功后查询车主信息
- [ ] 如果找到车主，显示姓名、电话、房屋信息
- [ ] 如果未找到，显示"未找到车主信息"

#### ✅ 拨号功能验证
- [ ] 点击车主电话号码
- [ ] 确认能正常拨打电话
- [ ] 测试"联系车主挪车"按钮

### 2. 后台管理验证（3分钟）

#### ✅ 记录查看验证
- [ ] 登录后台管理系统
- [ ] 访问车牌识别记录页面
- [ ] 确认能看到刚才的识别记录
- [ ] 检查记录信息完整性

#### ✅ 统计功能验证
- [ ] 点击"统计信息"按钮
- [ ] 确认今日统计数据正确
- [ ] 验证成功率计算准确

### 3. 错误处理验证（2分钟）

#### ✅ 异常情况验证
- [ ] 上传非图片文件，确认错误提示
- [ ] 上传过大文件，确认大小限制
- [ ] 识别非车牌图片，确认处理正常
- [ ] 网络异常时的错误提示

## 详细验证日志

### 成功案例日志示例

#### 文件上传成功
```
POST /api/wx/file/upload
Response: {
  "code": 0,
  "fileId": "20250819110138A002",
  "url": "https://domain/fv/download/20250819110138A002"
}
```

#### 车牌识别成功
```
POST /api/wx/vehicle/recognizePlate
Request: fileId=20250819110138A002
Response: {
  "code": 200,
  "data": {
    "plateNumber": "湘LZ0170",
    "confidence": "99%",
    "color": "blue",
    "ownerInfo": {
      "ownerName": "张三",
      "ownerPhone": "13800138000"
    }
  }
}
```

### 常见问题排查

#### 问题1：识别接口报错 "Current request is not a multipart request"
**解决方案**: 已修复，使用 `@RequestParam` 接收参数

#### 问题2：van-uploader组件不显示
**解决方案**: 在页面json中添加组件配置
```json
{
  "usingComponents": {
    "van-uploader": "/miniprogram_npm/@vant/weapp/uploader/index"
  }
}
```

#### 问题3：导入模块错误
**解决方案**: 使用正确的导入路径
```javascript
import { createFeedbackManager } from '../../utils/feedbackManager.js'
import { handleError, getLoadingManager } from '../../utils/errorHandler.js'
```

## 数据库验证

### 验证识别记录保存
```sql
-- 查看最新的识别记录
SELECT * FROM eh_plate_recognition_log 
ORDER BY create_time DESC 
LIMIT 5;

-- 验证记录完整性
SELECT 
  log_id,
  file_id,
  plate_number,
  recognition_status,
  owner_found,
  create_time
FROM eh_plate_recognition_log 
WHERE DATE(create_time) = CURDATE();
```

### 验证文件信息关联
```sql
-- 验证文件ID关联
SELECT 
  l.log_id,
  l.file_id,
  l.plate_number,
  f.original_filename,
  f.file_size
FROM eh_plate_recognition_log l
LEFT JOIN eh_file_info f ON l.file_id = f.file_id
WHERE l.file_id IS NOT NULL
ORDER BY l.create_time DESC
LIMIT 5;
```

## 性能验证

### 响应时间测试
- [ ] 文件上传时间 < 5秒
- [ ] 车牌识别时间 < 10秒  
- [ ] 车主查询时间 < 2秒
- [ ] 页面加载时间 < 3秒

### 准确率测试
- [ ] 清晰车牌识别准确率 > 95%
- [ ] 车主信息匹配准确率 = 100%

## 安全验证

### 权限验证
- [ ] 未登录用户无法访问识别功能
- [ ] 用户只能查看自己社区的车主信息
- [ ] 文件上传有大小和类型限制

### 数据安全验证
- [ ] 敏感信息不在前端日志中显示
- [ ] API接口有正确的认证头
- [ ] 文件访问权限正确

## 验证结果记录

### 基础功能测试
- 测试时间: ___________
- 测试人员: ___________
- 文件上传: ✅ / ❌
- 车牌识别: ✅ / ❌  
- 车主查询: ✅ / ❌
- 拨号功能: ✅ / ❌

### 后台管理测试
- 记录查看: ✅ / ❌
- 统计功能: ✅ / ❌
- 详情页面: ✅ / ❌

### 错误处理测试
- 文件类型验证: ✅ / ❌
- 大小限制验证: ✅ / ❌
- 网络异常处理: ✅ / ❌

### 性能测试
- 平均上传时间: _____ 秒
- 平均识别时间: _____ 秒
- 识别成功率: _____ %

### 问题记录
1. ________________
2. ________________
3. ________________

## 上线确认

### 最终检查清单
- [ ] 所有基础功能正常
- [ ] 后台管理功能正常
- [ ] 错误处理完善
- [ ] 性能指标达标
- [ ] 安全验证通过
- [ ] 数据库记录正常

### 上线准备
- [ ] 生产环境配置确认
- [ ] 百度AI密钥配置
- [ ] 监控告警配置
- [ ] 用户使用说明准备

**验证完成签字**: ___________  
**验证日期**: ___________  
**可以上线**: ✅ / ❌
