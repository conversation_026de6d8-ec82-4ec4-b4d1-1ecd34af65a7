# 巡更配置项目名称字段测试指南

## 测试前准备

1. **执行数据库迁移脚本**
   ```sql
   -- 执行文件：sql/patrol_config_add_project_name.sql
   source sql/patrol_config_add_project_name.sql;
   ```

2. **重启应用服务**
   - 重启后端服务以加载新的控制器代码

## 功能测试

### 1. 新增巡更配置测试
- 访问：`/oc/patrol/config/add`
- 验证项目名称字段：
  - [ ] 字段显示为必填项（红色星号）
  - [ ] 输入框支持下拉选择常用项目名称
  - [ ] 可以自定义输入项目名称
  - [ ] 提交时验证项目名称不能为空

### 2. 编辑巡更配置测试
- 访问：`/oc/patrol/config/edit/{configId}`
- 验证项目名称字段：
  - [ ] 正确回填已保存的项目名称
  - [ ] 支持修改项目名称
  - [ ] 保存时更新项目名称

### 3. 列表页面测试
- 访问：`/oc/patrol/config`
- 验证项目名称显示：
  - [ ] 列表中显示项目名称列
  - [ ] 支持按项目名称搜索筛选
  - [ ] 项目名称列支持排序

### 4. API接口测试
- 测试获取项目名称选项：
  ```
  GET /oc/patrol/config/projectNameOptions
  ```
  - [ ] 返回预定义的项目名称数组

### 5. 小程序端测试
- 访问小程序巡更功能
- 验证项目名称显示：
  - [ ] 任务列表中显示项目名称标签
  - [ ] 任务详情页面显示项目名称
  - [ ] 项目名称样式正确（蓝色标签）
  - [ ] 历史记录中包含项目名称

### 6. 巡更记录页面测试
- 访问：`/oc/patrol/record`
- 验证项目名称显示：
  - [ ] 记录列表中显示项目名称列
  - [ ] 记录详情包含项目名称信息

## 常用项目名称列表
- 安全巡查
- 设施检查
- 环境巡视
- 消防检查
- 设备维护
- 绿化养护
- 清洁检查
- 夜间巡逻

## 数据验证
```sql
-- 验证字段是否正确添加
DESCRIBE eh_patrol_config;

-- 查看现有数据的项目名称
SELECT config_id, location_name, project_name, planned_time 
FROM eh_patrol_config 
ORDER BY create_time DESC;
```
