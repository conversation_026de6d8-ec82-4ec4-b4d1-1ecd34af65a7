/* 物业端报修详情页面样式 */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏样式 */
.search-container {
  background-color: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

/* 空状态 */
.empty-container {
  padding: 100rpx 0;
}

/* 报修列表 */
.repair-list {
  padding: 0 20rpx;
}

/* 报修项目 */
.repair-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 报修头部 */
.repair-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.repair-id {
  display: flex;
  align-items: center;
}

.id-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.id-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 报修信息 */
.repair-info {
  margin-bottom: 30rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

/* 报修内容 */
.repair-content {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.content-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.content-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.action-buttons .van-button {
  flex: 1;
  border-radius: 12rpx !important;
  height: 80rpx !important;
  font-size: 28rpx !important;
}

.action-buttons .van-button--default {
  background-color: #f7f8fa !important;
  border-color: #ebedf0 !important;
  color: #646566 !important;
}

.action-buttons .van-button--primary {
  background-color: #1989fa !important;
  border-color: #1989fa !important;
}

/* 反馈输入弹窗 */
.feedback-container {
  padding: 20rpx;
}

.feedback-container .van-field {
  min-height: 200rpx;
}

/* 加载更多和没有更多数据 */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  color: #999;
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 26rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .repair-item {
    padding: 24rpx;
  }
  
  .action-buttons {
    gap: 16rpx;
  }
  
  .action-buttons .van-button {
    height: 72rpx !important;
    font-size: 26rpx !important;
  }
}
