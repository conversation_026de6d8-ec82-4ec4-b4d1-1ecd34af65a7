<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('管理房屋绑定')" />
    <th:block th:include="include :: select2-css" />
    <style>
        .binding-section {
            margin-bottom: 25px;
            background: white;
            border-radius: 3px;
            padding: 20px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .binding-section:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .binding-section h5 {
            margin-top: 0;
            margin-bottom: 20px;
            color: #333;
            font-weight: 600;
            font-size: 18px;
            display: flex;
            align-items: center;
            padding-bottom: 12px;
            border-bottom: 2px solid #f0f0f0;
        }

        .binding-section h5 i {
            margin-right: 10px;
            font-size: 20px;
        }

        /* 不同绑定类型的主题色 */
        .binding-section:nth-child(2) h5 i { color: #007bff; } /* 房屋 */
        .binding-section:nth-child(3) h5 i { color: #fd7e14; } /* 车辆 */
        .binding-section:nth-child(4) h5 i { color: #28a745; } /* 车位 */

        .binding-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 8px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #dee2e6;
            transition: all 0.2s ease;
        }

        .binding-item:hover {
            background-color: #e9ecef;
            transform: translateX(5px);
        }

        .binding-item:last-child {
            margin-bottom: 0;
        }

        .binding-info {
            flex: 1;
        }

        .binding-info strong {
            font-size: 16px;
            color: #333;
        }

        .binding-actions {
            margin-left: 15px;
        }

        .binding-actions .btn {
            margin-left: 5px;
        }

        .add-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 2px dashed #dee2e6;
        }

        .add-section:hover {
            border-color: #007bff;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }

        .binding-add-row {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .binding-select {
            flex: 1;
            min-width: 200px;
            height: 34px;
            padding: 6px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #fff;
            font-size: 14px;
        }

        .binding-select:focus {
            border-color: #007bff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .binding-add-btn {
            white-space: nowrap;
            flex-shrink: 0;
        }

        .owner-title {
            padding: 15px 0;
            margin-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .owner-title h2 {
            color: #333;
            margin: 0;
            font-weight: 600;
            font-size: 16px;
            text-align: left;
        }

        .owner-title .owner-icon {
            font-size: 16px;
            margin-right: 8px;
            color: #666;
        }

        .no-data {
            text-align: center;
            color: #999;
            padding: 20px;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <!-- 业主标题 -->
        <div class="owner-title">
            <h2><i class="fa fa-user owner-icon"></i><span id="ownerName">业主姓名</span></h2>
        </div>

        <!-- 已绑定房屋 -->
        <div class="binding-section">
            <h5><i class="fa fa-home"></i> 已绑定房屋</h5>
            <div id="houseList">
                <div class="no-data">暂无绑定房屋</div>
            </div>
            <div class="add-section">
                <div class="binding-add-row">
                    <select id="houseSelect" class="binding-select">
                        <option value="">请选择房屋</option>
                    </select>
                    <select id="relTypeSelect" class="binding-select" style="flex: 0 0 120px;">
                        <option value="1">业主</option>
                        <option value="2">家庭成员</option>
                        <option value="3">租户</option>
                    </select>
                    <button type="button" class="btn btn-primary binding-add-btn" onclick="addHouseBinding()">
                        <i class="fa fa-plus"></i> 绑定房屋
                    </button>
                </div>
            </div>
        </div>

        <!-- 已绑定车辆 -->
        <div class="binding-section">
            <h5><i class="fa fa-automobile"></i> 已绑定车辆</h5>
            <div id="vehicleList">
                <div class="no-data">暂无绑定车辆</div>
            </div>
            <div class="add-section">
                <div class="binding-add-row">
                    <select id="vehicleSelect" class="binding-select">
                        <option value="">请选择车辆</option>
                    </select>
                    <button type="button" class="btn btn-primary binding-add-btn" onclick="addVehicleBinding()">
                        <i class="fa fa-plus"></i> 绑定车辆
                    </button>
                </div>
            </div>
        </div>

        <!-- 已绑定车位 -->
        <div class="binding-section">
            <h5><i class="fa fa-square"></i> 已绑定车位</h5>
            <div id="parkingList">
                <div class="no-data">暂无绑定车位</div>
            </div>
            <div class="add-section">
                <div class="binding-add-row">
                    <select id="parkingSelect" class="binding-select">
                        <option value="">请选择车位</option>
                    </select>
                    <button type="button" class="btn btn-primary binding-add-btn" onclick="addParkingBinding()">
                        <i class="fa fa-plus"></i> 绑定车位
                    </button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">
        var ownerId = /*[[${ownerId}]]*/ '';
        var prefix = ctx + "oc/owner";
        var bindingPrefix = ctx + "oc/binding";

        $(function() {
            // 初始化Select2
            $('#houseSelect').select2({
                placeholder: "请选择房屋或输入搜索",
                allowClear: true,
                ajax: {
                    url: bindingPrefix + "/owner-house/available",
                    type: 'POST',
                    dataType: 'json',
                    delay: 300,
                    data: function (params) {
                        return {
                            search: params.term,
                            ownerId: ownerId,
                            page: params.page || 1
                        };
                    },
                    processResults: function (data, params) {
                        params.page = params.page || 1;
                        return {
                            results: data.data || [],
                            pagination: {
                                more: (params.page * 20) < (data.total || 0)
                            }
                        };
                    }
                }
            });

            $('#parkingSelect').select2({
                placeholder: "请选择车位或输入搜索",
                allowClear: true,
                ajax: {
                    url: bindingPrefix + "/owner-parking/available",
                    type: 'POST',
                    dataType: 'json',
                    delay: 300,
                    data: function (params) {
                        return {
                            search: params.term,
                            ownerId: ownerId,
                            page: params.page || 1
                        };
                    },
                    processResults: function (data, params) {
                        params.page = params.page || 1;
                        return {
                            results: data.data || [],
                            pagination: {
                                more: (params.page * 20) < (data.total || 0)
                            }
                        };
                    }
                }
            });

            $('#vehicleSelect').select2({
                placeholder: "请选择车辆或输入搜索",
                allowClear: true,
                ajax: {
                    url: bindingPrefix + "/owner-vehicle/available",
                    type: 'POST',
                    dataType: 'json',
                    delay: 300,
                    data: function (params) {
                        return {
                            search: params.term,
                            ownerId: ownerId,
                            page: params.page || 1
                        };
                    },
                    processResults: function (data, params) {
                        params.page = params.page || 1;
                        return {
                            results: data.data || [],
                            pagination: {
                                more: (params.page * 20) < (data.total || 0)
                            }
                        };
                    }
                }
            });

            // 加载数据
            loadBindingData();
        });

        function loadBindingData() {
            $.ajax({
                url: bindingPrefix + "/owner/data",
                type: "POST",
                data: { ownerId: ownerId },
                success: function(res) {
                    if (res.code === 0) {
                        var data = res.data;

                        // 显示业主姓名
                        $('#ownerName').text(data.ownerName || '业主姓名');

                        // 显示绑定的房屋
                        renderHouseList(data.houses || []);

                        // 显示绑定的车位
                        renderParkingList(data.parkings || []);

                        // 显示绑定的车辆
                        renderVehicleList(data.vehicles || []);

                        // 刷新select2选项
                        refreshSelect2Options();
                    } else {
                        $.modal.alertError("加载数据失败：" + res.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("加载数据失败");
                }
            });
        }

        function renderHouseList(houses) {
            var html = '';
            if (houses.length === 0) {
                html = '<div class="no-data">暂无绑定房屋</div>';
            } else {
                houses.forEach(function(house) {
                    html += '<div class="binding-item">';
                    html += '<div class="binding-info">';
                    html += '<strong>' + (house.combina_name || '') + '/' + (house.room || '') + '</strong>';
                    html += '<span class="text-muted ml-2">(' + getRelTypeText(house.rel_type) + ')</span>';
                    if (house.is_default == 1) {
                        html += '<span class="label label-primary ml-2">默认</span>';
                    }
                    if (house.remark) {
                        html += '<br><small class="text-muted">' + house.remark + '</small>';
                    }
                    html += '</div>';
                    html += '<div class="binding-actions">';
                    html += '<button type="button" class="btn btn-danger btn-xs" onclick="removeHouseBinding(\'' + house.rel_id + '\')">';
                    html += '<i class="fa fa-remove"></i> 解绑';
                    html += '</button>';
                    html += '</div>';
                    html += '</div>';
                });
            }
            $('#houseList').html(html);
        }

        function renderParkingList(parkings) {
            var html = '';
            if (parkings.length === 0) {
                html = '<div class="no-data">暂无绑定车位</div>';
            } else {
                parkings.forEach(function(parking) {
                    html += '<div class="binding-item">';
                    html += '<div class="binding-info">';
                    html += '<strong>' + parking.parking_no + '</strong>';
                    html += '<span class="text-muted ml-2">(' + getParkingTypeText(parking.parking_type) + ')</span>';
                    if (parking.remark) {
                        html += '<br><small class="text-muted">' + parking.remark + '</small>';
                    }
                    html += '</div>';
                    html += '<div class="binding-actions">';
                    html += '<button type="button" class="btn btn-danger btn-xs" onclick="removeParkingBinding(\'' + parking.rel_id + '\')">';
                    html += '<i class="fa fa-remove"></i> 解绑';
                    html += '</button>';
                    html += '</div>';
                    html += '</div>';
                });
            }
            $('#parkingList').html(html);
        }

        function renderVehicleList(vehicles) {
            var html = '';
            if (vehicles.length === 0) {
                html = '<div class="no-data">暂无绑定车辆</div>';
            } else {
                vehicles.forEach(function(vehicle) {
                    html += '<div class="binding-item">';
                    html += '<div class="binding-info">';
                    html += '<strong>' + vehicle.plate_no + '</strong>';
                    if (vehicle.vehicle_brand) {
                        html += '<span class="text-muted ml-2">' + vehicle.vehicle_brand;
                        if (vehicle.vehicle_model) {
                            html += ' ' + vehicle.vehicle_model;
                        }
                        html += '</span>';
                    }
                    if (vehicle.remark) {
                        html += '<br><small class="text-muted">' + vehicle.remark + '</small>';
                    }
                    html += '</div>';
                    html += '<div class="binding-actions">';
                    html += '<button type="button" class="btn btn-danger btn-xs" onclick="removeVehicleBinding(\'' + vehicle.rel_id + '\')">';
                    html += '<i class="fa fa-remove"></i> 解绑';
                    html += '</button>';
                    html += '</div>';
                    html += '</div>';
                });
            }
            $('#vehicleList').html(html);
        }

        function refreshSelect2Options() {
            // 清空并重新触发select2的数据加载
            $('#houseSelect').val(null).trigger('change');
            $('#parkingSelect').val(null).trigger('change');
            $('#vehicleSelect').val(null).trigger('change');
        }

        function addHouseBinding() {
            var houseId = $('#houseSelect').val();
            var relType = $('#relTypeSelect').val();
            if (!houseId) {
                $.modal.alertWarning("请选择要绑定的房屋");
                return;
            }

            $.ajax({
                url: bindingPrefix + "/owner-house/add",
                type: "POST",
                data: {
                    ownerId: ownerId,
                    houseId: houseId,
                    relType: relType
                },
                success: function(res) {
                    if (res.code === 0) {
                        $.modal.msgSuccess("绑定成功");
                        $('#houseSelect').val(null).trigger('change');
                        loadBindingData();
                        // 刷新父页面的表格数据
                        if (parent && parent.$.table && parent.$.table.refresh) {
                            parent.$.table.refresh();
                        }
                    } else {
                        $.modal.alertError("绑定失败：" + res.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("绑定失败");
                }
            });
        }

        function addParkingBinding() {
            var parkingId = $('#parkingSelect').val();
            if (!parkingId) {
                $.modal.alertWarning("请选择要绑定的车位");
                return;
            }

            $.ajax({
                url: bindingPrefix + "/owner-parking/add",
                type: "POST",
                data: {
                    ownerId: ownerId,
                    parkingId: parkingId
                },
                success: function(res) {
                    if (res.code === 0) {
                        $.modal.msgSuccess("绑定成功");
                        $('#parkingSelect').val(null).trigger('change');
                        loadBindingData();
                        // 刷新父页面的表格数据
                        if (parent && parent.$.table && parent.$.table.refresh) {
                            parent.$.table.refresh();
                        }
                    } else {
                        $.modal.alertError("绑定失败：" + res.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("绑定失败");
                }
            });
        }

        function addVehicleBinding() {
            var vehicleId = $('#vehicleSelect').val();
            if (!vehicleId) {
                $.modal.alertWarning("请选择要绑定的车辆");
                return;
            }

            $.ajax({
                url: bindingPrefix + "/owner-vehicle/add",
                type: "POST",
                data: {
                    ownerId: ownerId,
                    vehicleId: vehicleId
                },
                success: function(res) {
                    if (res.code === 0) {
                        $.modal.msgSuccess("绑定成功");
                        $('#vehicleSelect').val(null).trigger('change');
                        loadBindingData();
                        // 刷新父页面的表格数据
                        if (parent && parent.$.table && parent.$.table.refresh) {
                            parent.$.table.refresh();
                        }
                    } else {
                        $.modal.alertError("绑定失败：" + res.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("绑定失败");
                }
            });
        }

        function removeParkingBinding(relId) {
            $.modal.confirm("确定要解绑此车位吗？", function() {
                $.ajax({
                    url: bindingPrefix + "/owner-parking/remove",
                    type: "POST",
                    data: { relId: relId },
                    success: function(res) {
                        if (res.code === 0) {
                            $.modal.msgSuccess("解绑成功");
                            loadBindingData();
                            // 刷新父页面的表格数据
                            if (parent && parent.$.table && parent.$.table.refresh) {
                                parent.$.table.refresh();
                            }
                        } else {
                            $.modal.alertError("解绑失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("解绑失败");
                    }
                });
            });
        }

        function removeVehicleBinding(relId) {
            $.modal.confirm("确定要解绑此车辆吗？", function() {
                $.ajax({
                    url: bindingPrefix + "/owner-vehicle/remove",
                    type: "POST",
                    data: { relId: relId },
                    success: function(res) {
                        if (res.code === 0) {
                            $.modal.msgSuccess("解绑成功");
                            loadBindingData();
                            // 刷新父页面的表格数据
                            if (parent && parent.$.table && parent.$.table.refresh) {
                                parent.$.table.refresh();
                            }
                        } else {
                            $.modal.alertError("解绑失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("解绑失败");
                    }
                });
            });
        }

        function removeHouseBinding(relId) {
            $.modal.confirm("确定要解绑此房屋吗？", function() {
                $.ajax({
                    url: bindingPrefix + "/owner-house/remove",
                    type: "POST",
                    data: { relId: relId },
                    success: function(res) {
                        if (res.code === 0) {
                            $.modal.msgSuccess("解绑成功");
                            loadBindingData();
                            // 刷新父页面的表格数据
                            if (parent && parent.$.table && parent.$.table.refresh) {
                                parent.$.table.refresh();
                            }
                        } else {
                            $.modal.alertError("解绑失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("解绑失败");
                    }
                });
            });
        }

        function getRelTypeText(type) {
            switch(type) {
                case 1: return '业主';
                case 2: return '家庭成员';
                case 3: return '租户';
                default: return '未知';
            }
        }

        function getParkingTypeText(type) {
            switch(type) {
                case 1: return '私人车位';
                case 2: return '子母车位';
                default: return '未知类型';
            }
        }

        function closeDialog() {
            // 刷新父页面的表格数据
            if (parent && parent.$.table && parent.$.table.refresh) {
                parent.$.table.refresh();
            }
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</body>
</html>
