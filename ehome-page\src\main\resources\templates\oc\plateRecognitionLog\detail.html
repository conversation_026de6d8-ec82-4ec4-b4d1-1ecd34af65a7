<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('识别记录详情')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>车牌识别记录详情</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row" th:if="${log}">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">记录ID：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${log.log_id}"></p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">用户姓名：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${log.user_name}"></p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">用户手机号：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${log.user_phone}"></p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">文件ID：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${log.file_id ?: '-'}"></p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">车主ID：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${log.owner_id ?: '-'}"></p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">车牌号：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static">
                                            <span th:if="${log.plate_number}" class="label label-primary" th:text="${log.plate_number}"></span>
                                            <span th:unless="${log.plate_number}">-</span>
                                        </p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">识别状态：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static">
                                            <span th:if="${log.recognition_status == 1}" class="label label-success">成功</span>
                                            <span th:unless="${log.recognition_status == 1}" class="label label-danger">失败</span>
                                        </p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">识别置信度：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${log.confidence != null ? #numbers.formatDecimal(log.confidence * 100, 1, 1) + '%' : '-'}"></p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">车牌颜色：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${log.plate_color ?: '-'}"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">找到车主：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static">
                                            <span th:if="${log.owner_found == 1}" class="label label-success">是</span>
                                            <span th:unless="${log.owner_found == 1}" class="label label-warning">否</span>
                                        </p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">车主姓名：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${log.owner_name ?: '-'}"></p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">车主电话：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${log.owner_phone ?: '-'}"></p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">图片大小：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${log.image_size != null ? #numbers.formatDecimal(log.image_size / 1024.0, 1, 1) + ' KB' : '-'}"></p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">识别耗时：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${log.recognition_time != null ? log.recognition_time + ' ms' : '-'}"></p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">识别时间：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${log.create_time}"></p>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">IP地址：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" th:text="${log.ip_address ?: '-'}"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row" th:if="${log.error_message}">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">错误信息：</label>
                                    <div class="col-sm-10">
                                        <div class="alert alert-danger" th:text="${log.error_message}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row" th:unless="${log}">
                            <div class="col-sm-12">
                                <div class="alert alert-warning">
                                    <i class="fa fa-warning"></i> 记录不存在或已被删除
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-sm-12 text-center">
                                <button type="button" class="btn btn-default" onclick="$.modal.close()">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
</body>
</html>
