.repair-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.tab-content {
  padding: 20rpx;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.repair-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.repair-item {
  background: white;
  padding: 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.item-type {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.item-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  color: white;
}

.status-0 {
  background-color: #ff4d4f;
}

.status-1 {
  background-color: #faad14;
}

.status-2 {
  background-color: #52c41a;
}

.item-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.item-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.info-left {
  display: flex;
  gap: 20rpx;
}

.info-user, .info-phone {
  font-size: 24rpx;
  color: #999;
}

.info-time {
  font-size: 24rpx;
  color: #999;
}

.item-address {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #999;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  margin-top: 20rpx;
}

.no-more {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  padding: 20rpx;
}
