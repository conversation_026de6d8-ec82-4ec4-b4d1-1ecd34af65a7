package com.ehome.oc.domain;

import com.ehome.common.annotation.Excel;
import com.ehome.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 车辆导入对象 vehicle_import
 * 
 * <AUTHOR>
 * @date 2024-08-19
 */
public class VehicleImport extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 车牌号码 */
    @Excel(name = "车牌号码")
    private String plateNo;

    /** 车辆类型 */
    @Excel(name = "车辆类型", readConverterExp = "业主车辆=1,非业主车辆=2")
    private String vehicleType;

    /** 车主姓名 */
    @Excel(name = "车主姓名")
    private String ownerRealName;

    /** 车主手机号 */
    @Excel(name = "车主手机号")
    private String ownerPhone;

    /** 绑定住户 */
    @Excel(name = "绑定住户")
    private String bindOwner;

    /** 绑定房屋 */
    @Excel(name = "绑定房屋")
    private String bindHouse;

    /** 绑定车位 */
    @Excel(name = "绑定车位")
    private String bindParking;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    public String getPlateNo() {
        return plateNo;
    }

    public void setPlateNo(String plateNo) {
        this.plateNo = plateNo;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getOwnerRealName() {
        return ownerRealName;
    }

    public void setOwnerRealName(String ownerRealName) {
        this.ownerRealName = ownerRealName;
    }

    public String getOwnerPhone() {
        return ownerPhone;
    }

    public void setOwnerPhone(String ownerPhone) {
        this.ownerPhone = ownerPhone;
    }

    public String getBindOwner() {
        return bindOwner;
    }

    public void setBindOwner(String bindOwner) {
        this.bindOwner = bindOwner;
    }

    public String getBindHouse() {
        return bindHouse;
    }

    public void setBindHouse(String bindHouse) {
        this.bindHouse = bindHouse;
    }

    public String getBindParking() {
        return bindParking;
    }

    public void setBindParking(String bindParking) {
        this.bindParking = bindParking;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("plateNo", getPlateNo())
            .append("vehicleType", getVehicleType())
            .append("ownerRealName", getOwnerRealName())
            .append("ownerPhone", getOwnerPhone())
            .append("bindOwner", getBindOwner())
            .append("bindHouse", getBindHouse())
            .append("bindParking", getBindParking())
            .append("remark", getRemark())
            .toString();
    }
}
