.dashboard-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.stats-section {
  margin-bottom: 40rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.stat-item {
  background: white;
  padding: 30rpx;
  border-radius: 10rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.stat-item.urgent {
  border-left: 4rpx solid #ff976a;
}

.stat-item.processing {
  border-left: 4rpx solid #1989fa;
}

.stat-item.today {
  border-left: 4rpx solid #07c160;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.quick-actions {
  background: white;
  padding: 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.action-item {
  padding: 40rpx 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.action-text {
  font-size: 28rpx;
  color: #333;
}

.recent-section {
  background: white;
  padding: 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.more-link {
  font-size: 24rpx;
  color: #1989fa;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.recent-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.item-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.item-info {
  display: flex;
  gap: 20rpx;
  font-size: 22rpx;
  color: #999;
}

.item-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: white;
  white-space: nowrap;
}

.status-0 {
  background-color: #ff976a;
}

.status-1 {
  background-color: #1989fa;
}

.status-2 {
  background-color: #07c160;
}
