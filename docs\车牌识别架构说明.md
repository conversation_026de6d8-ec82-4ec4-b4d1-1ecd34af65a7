# 车牌识别功能架构说明

## 整体架构

车牌识别功能采用分离式架构设计，将文件上传和识别处理分为两个独立的步骤，符合系统的统一文件管理规范。

## 技术流程

### 1. 文件上传阶段
```
小程序 → 统一文件上传接口 → 文件存储 → 返回fileId
```

**接口**: `/api/wx/file/upload`
**参数**: 
- `file`: 图片文件
- `source`: `plate_recognition` (标识文件来源)
- `bucketType`: `public` (使用公共存储)

**返回**: 
```json
{
  "code": 0,
  "fileId": "file_123456",
  "url": "http://domain/fv/download/file_123456"
}
```

### 2. 车牌识别阶段
```
小程序 → 识别接口 → 读取文件 → 百度AI → 查询车主 → 保存记录
```

**接口**: `/api/wx/vehicle/recognizePlate`
**参数**:
```json
{
  "fileId": "file_123456"
}
```

**返回**:
```json
{
  "code": 200,
  "data": {
    "plateNumber": "湘LZ0170",
    "confidence": "99%",
    "color": "blue",
    "ownerInfo": {
      "ownerName": "张三",
      "ownerPhone": "13800138000",
      "houseName": "1号楼101室"
    }
  }
}
```

## 数据库设计

### 文件信息表 (eh_file_info)
存储上传的图片文件信息，由统一文件上传接口维护。

### 车辆信息表 (eh_vehicle)
存储车辆和车主信息，用于根据车牌号查询车主。

### 识别记录表 (eh_plate_recognition_log)
存储每次识别的详细记录，包括：
- 用户信息
- 识别结果
- 车主查询结果
- 性能数据
- 错误信息

## 核心组件

### 1. PlateRecognitionService
车牌识别服务，负责调用百度AI API。

**主要方法**:
- `recognizePlate(byte[] imageBytes)`: 直接识别图片字节
- `recognizePlateByPath(String imagePath)`: 根据文件路径识别

### 2. PlateRecognitionLogService
识别记录服务，负责保存识别过程和结果。

**主要方法**:
- `saveRecognitionLog(PlateRecognitionLogRecord)`: 保存记录
- `createLogRecord(...)`: 创建记录对象

### 3. WxVehicleController
小程序车辆控制器，提供识别和查询接口。

**主要接口**:
- `POST /api/wx/vehicle/recognizePlate`: 车牌识别
- `GET /api/wx/vehicle/getOwnerByPlate`: 车主查询

### 4. PlateRecognitionLogController
后台管理控制器，提供记录查看和统计功能。

## 优势特点

### 1. 架构优势
- **统一文件管理**: 使用系统统一的文件上传接口
- **模块化设计**: 识别功能独立，便于维护
- **记录完整**: 详细记录每次识别过程
- **错误处理**: 完善的异常处理和日志记录

### 2. 性能优势
- **异步处理**: 文件上传和识别分离
- **缓存支持**: 文件存储支持本地和OSS双重存储
- **索引优化**: 车牌号和社区ID建立复合索引

### 3. 安全优势
- **权限控制**: 需要用户登录和身份验证
- **文件验证**: 检查文件类型和大小
- **数据保护**: 不在日志中记录敏感信息

## 配置说明

### 百度AI配置
```yaml
baidu:
  ai:
    enabled: true
    app-id: your_app_id
    api-key: your_api_key
    secret-key: your_secret_key
```

### 文件上传配置
文件上传使用系统现有配置，支持本地存储和OSS存储。

## 监控和维护

### 1. 日志监控
- 识别请求和结果日志
- 错误和异常日志
- 性能数据日志

### 2. 数据统计
- 识别成功率统计
- 车主查找成功率统计
- 平均识别耗时统计

### 3. 后台管理
- 识别记录查看
- 统计数据展示
- 记录删除管理

## 扩展性

### 1. 识别引擎扩展
可以轻松替换或添加其他OCR识别引擎：
- 腾讯云OCR
- 阿里云OCR
- 本地Tesseract

### 2. 功能扩展
- 识别历史缓存
- 批量识别功能
- 识别结果导出

### 3. 性能优化
- 图片预处理优化
- 识别结果缓存
- 异步处理队列

## 部署建议

1. **测试环境验证**: 先在测试环境完整测试
2. **数据备份**: 部署前备份相关数据表
3. **监控配置**: 配置识别成功率和错误率监控
4. **容量规划**: 根据使用量规划存储和API调用量
