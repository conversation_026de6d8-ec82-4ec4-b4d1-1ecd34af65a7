# 房屋匹配逻辑修复说明

## 问题描述
在业主导入功能中，房屋匹配逻辑存在缺陷：**没有包含楼层字段进行匹配**，这可能导致匹配到错误的房屋。

## 问题分析

### 原始匹配逻辑（有问题）
```java
Record houseRecord = Db.findFirst(
    "SELECT h.house_id FROM eh_house_info h " +
    "LEFT JOIN eh_building b ON h.building_id = b.building_id " +
    "LEFT JOIN eh_unit u ON h.unit_id = u.unit_id " +
    "WHERE h.room = ? AND b.name = ? AND u.name = ? AND h.community_id = ?",
    owner.getRoom(), owner.getBuilding(), owner.getUnit(), communityId
);
```

**问题：** 只匹配了房号、楼栋、单元，没有匹配楼层

### 业务场景问题
假设有以下房屋数据：
- 1栋1单元101房，1层
- 1栋1单元101房，2层

如果导入业主数据：`101, 1栋, 1单元, 2, 张三`

**原始逻辑结果：** 可能匹配到1层的101房（错误）
**期望结果：** 应该匹配到2层的101房

## 修复方案

### 新的匹配逻辑
```java
Record houseRecord = null;
if (StringUtils.isNotEmpty(owner.getFloor())) {
    // 如果提供了楼层信息，精确匹配楼层
    houseRecord = Db.findFirst(
        "SELECT h.house_id FROM eh_house_info h " +
        "LEFT JOIN eh_building b ON h.building_id = b.building_id " +
        "LEFT JOIN eh_unit u ON h.unit_id = u.unit_id " +
        "WHERE h.room = ? AND b.name = ? AND u.name = ? AND h.floor = ? AND h.community_id = ?",
        owner.getRoom(), owner.getBuilding(), owner.getUnit(), owner.getFloor(), communityId
    );
} else {
    // 如果没有提供楼层信息，不匹配楼层
    houseRecord = Db.findFirst(
        "SELECT h.house_id FROM eh_house_info h " +
        "LEFT JOIN eh_building b ON h.building_id = b.building_id " +
        "LEFT JOIN eh_unit u ON h.unit_id = u.unit_id " +
        "WHERE h.room = ? AND b.name = ? AND u.name = ? AND h.community_id = ?",
        owner.getRoom(), owner.getBuilding(), owner.getUnit(), communityId
    );
}
```

### 修复要点

1. **条件匹配**：根据是否提供楼层信息决定匹配策略
2. **精确匹配**：提供楼层时，必须精确匹配楼层
3. **兼容性**：未提供楼层时，保持原有匹配逻辑
4. **错误提示**：更新错误信息，包含楼层信息

## 修复内容

### 1. 房屋匹配逻辑
- **有楼层信息**：房号 + 楼栋 + 单元 + 楼层 + 小区ID
- **无楼层信息**：房号 + 楼栋 + 单元 + 小区ID（保持兼容）

### 2. 错误提示优化
```java
String houseInfo = owner.getBuilding() + "/" + owner.getUnit() + "/" + owner.getRoom();
if (StringUtils.isNotEmpty(owner.getFloor())) {
    houseInfo += "/" + owner.getFloor() + "层";
}
failureMsg.append("对应的房屋不存在：" + houseInfo);
```

### 3. 成功信息优化
```java
String houseInfo = "（" + owner.getBuilding() + "/" + owner.getUnit() + "/" + owner.getRoom();
if (StringUtils.isNotEmpty(owner.getFloor())) {
    houseInfo += "/" + owner.getFloor() + "层";
}
houseInfo += "）";
```

## 业务场景测试

### 场景1：提供完整房屋信息
```excel
房号    楼栋    单元    所在楼层    业主姓名
101     1栋     1单元   2          张三
```

**匹配逻辑：** 精确匹配 1栋1单元101房2层
**结果显示：** `业主 张三（1栋/1单元/101/2层） 导入成功`

### 场景2：不提供楼层信息
```excel
房号    楼栋    单元    所在楼层    业主姓名
101     1栋     1单元              张三
```

**匹配逻辑：** 匹配 1栋1单元101房（任意楼层）
**结果显示：** `业主 张三（1栋/1单元/101） 导入成功`

### 场景3：房屋不存在
```excel
房号    楼栋    单元    所在楼层    业主姓名
999     9栋     9单元   9          张三
```

**匹配逻辑：** 查找失败
**错误显示：** `业主 张三 对应的房屋不存在：9栋/9单元/999/9层`

## 技术实现细节

### 1. 条件判断
- 使用`StringUtils.isNotEmpty(owner.getFloor())`判断是否提供楼层
- 根据条件选择不同的SQL查询语句

### 2. SQL优化
- 保持原有的联表查询结构
- 增加楼层字段的条件匹配
- 确保查询性能不受影响

### 3. 信息显示
- 统一房屋信息显示格式
- 根据是否有楼层信息动态调整显示内容
- 保持用户友好的错误提示

## 兼容性考虑

### 1. 向后兼容
- 不提供楼层信息时，保持原有匹配逻辑
- 现有数据导入不受影响

### 2. 数据完整性
- 楼层信息为可选字段
- 不强制要求楼层信息

### 3. 用户体验
- 提供更准确的房屋匹配
- 更清晰的错误提示信息

## 总结
通过修复房屋匹配逻辑，现在能够：
- ✅ **精确匹配**：包含楼层信息的精确房屋匹配
- ✅ **避免错误**：防止匹配到错误楼层的房屋
- ✅ **保持兼容**：支持不提供楼层信息的导入
- ✅ **清晰反馈**：提供包含楼层信息的错误和成功提示

这个修复确保了业主导入功能能够准确匹配房屋，避免了因楼层信息缺失导致的房屋匹配错误。
