package com.ehome.oc.domain;

import com.ehome.common.annotation.Excel;
import com.ehome.common.core.domain.BaseEntity;

/**
 * 业主导入对象 owner_import
 * 
 * <AUTHOR>
 */
public class OwnerImport extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 房号 */
    @Excel(name = "房号", type = Excel.Type.IMPORT)
    private String room;

    /** 楼栋 */
    @Excel(name = "楼栋", type = Excel.Type.IMPORT)
    private String building;

    /** 单元 */
    @Excel(name = "单元", type = Excel.Type.IMPORT)
    private String unit;

    /** 所在楼层 */
    @Excel(name = "所在楼层", type = Excel.Type.IMPORT)
    private String floor;

    /** 业主姓名 */
    @Excel(name = "业主姓名", type = Excel.Type.IMPORT)
    private String ownerName;

    /** 手机号 */
    @Excel(name = "手机号", type = Excel.Type.IMPORT)
    private String mobile;

    /** 身份证号码 */
    @Excel(name = "身份证号码", type = Excel.Type.IMPORT)
    private String idCard;

    /** 性别 */
    @Excel(name = "性别", type = Excel.Type.IMPORT, readConverterExp = "男=M,女=F")
    private String gender;

    /** 家庭住址 */
    @Excel(name = "家庭住址", type = Excel.Type.IMPORT)
    private String address;

    /** 备注 */
    @Excel(name = "备注", type = Excel.Type.IMPORT)
    private String remark;

    // 非Excel字段，用于内部处理
    private String communityId;
    private String pmsId;

    public String getRoom() {
        return room;
    }

    public void setRoom(String room) {
        this.room = room;
    }

    public String getBuilding() {
        return building;
    }

    public void setBuilding(String building) {
        this.building = building;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getFloor() {
        return floor;
    }

    public void setFloor(String floor) {
        this.floor = floor;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCommunityId() {
        return communityId;
    }

    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }

    public String getPmsId() {
        return pmsId;
    }

    public void setPmsId(String pmsId) {
        this.pmsId = pmsId;
    }

    @Override
    public String toString() {
        return "OwnerImport{" +
                "room='" + room + '\'' +
                ", building='" + building + '\'' +
                ", unit='" + unit + '\'' +
                ", floor='" + floor + '\'' +
                ", ownerName='" + ownerName + '\'' +
                ", mobile='" + mobile + '\'' +
                ", idCard='" + idCard + '\'' +
                ", gender='" + gender + '\'' +
                ", address='" + address + '\'' +
                ", remark='" + getRemark() + '\'' +
                '}';
    }
}
