<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('业委会成员管理')" />
</head>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="config-form">
					<div class="select-list">
						<ul>
							<li>
								姓名：<input type="text" name="owner_name" onkeypress="if(event.keyCode==13) $.table.search()"/>
							</li>
							<li>
								手机号码：<input type="text" name="mobile" onkeypress="if(event.keyCode==13) $.table.search()"/>
							</li>
							<li>
								房屋信息：<input type="text" name="house_info" placeholder="楼栋/房号" onkeypress="if(event.keyCode==13) $.table.search()"/>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
			
	        <div class="btn-group-sm" id="toolbar" role="group">
		        <a class="btn btn-success" onclick="addCommitteeMember()">
		            <i class="fa fa-plus"></i> 新增业委会成员
		        </a>
		        <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
		            <i class="fa fa-remove"></i> 删除
		        </a>
	        </div>
	        <div class="col-sm-12 select-table table-striped">
	            <table id="bootstrap-table"></table>
	        </div>
	    </div>
	</div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/owner";

        $(function() {
            var options = {
                url: prefix + "/committeeList",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "业委会成员",
				escape: false,
				layer:{
					area:['800px','550px'],
					offset: '70px'
				},
                columns: [{
                    checkbox: true
                },
                {
                    field: 'owner_id',
                    title: '成员ID',
                    visible: false
                },
                {
                    field: 'owner_name',
                    title: '姓名'
                },
                {
                    field: 'mobile',
                    title: '手机号'
                },
                {
                    field: 'id_card',
                    title: '身份证号',
                    formatter: function(value, row, index) {
                        if (!value) return '-';
                        // 身份证号脱敏显示
                        return value.substring(0, 6) + '****' + value.substring(value.length - 4);
                    }
                },
				{
					field: 'gender',
					title: '性别',
					formatter: function(value, row, index) {
						if (value == 'M') return '男';
						else if (value == 'F') return '女';
						return '-';
					}
				},
                {
                    field: 'house_info',
                    title: '房屋信息',
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },
				{
					field: 'is_live',
					title: '入住状态',
					formatter: function(value, row, index) {
						if (value == '1') return '<span class="label label-success">已入住</span>';
						else if (value == '0') return '<span class="label label-warning">未入住</span>';
						return '-';
					}
				},
                {
                    field: 'create_time',
                    title: '创建时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="viewDetail(\'' + row.owner_id + '\')"><i class="fa fa-eye"></i>查看</a> ');
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.owner_id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="removeCommitteeMember(\'' + row.owner_id + '\')"><i class="fa fa-remove"></i>移除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 新增业委会成员
        function addCommitteeMember() {
            layer.prompt({
                title: '新增业委会成员',
                formType: 0,
                area: ['400px', '200px'],
                value: '',
                placeholder: '请输入业主ID'
            }, function(value, index) {
                if (!value || value.trim() == '') {
                    $.modal.alertWarning("请输入业主ID");
                    return;
                }

                var ownerId = value.trim();

                // 验证是否为数字
                if (!/^\d+$/.test(ownerId)) {
                    $.modal.alertWarning("业主ID必须为数字");
                    return;
                }

                layer.close(index);

                // 确认添加
                $.modal.confirm("确认将业主ID为 " + ownerId + " 的业主设置为业委会成员吗？", function() {
                    $.ajax({
                        url: prefix + "/addCommitteeMember",
                        type: "post",
                        data: { ownerId: ownerId },
                        success: function(res) {
                            if (res.code == 0) {
                                $.modal.msgSuccess("添加业委会成员成功");
                                $.table.refresh();
                            } else {
                                $.modal.alertError("添加失败：" + res.msg);
                            }
                        },
                        error: function() {
                            $.modal.alertError("添加失败");
                        }
                    });
                });
            });
        }

        // 移除业委会成员
        function removeCommitteeMember(ownerId) {
            $.modal.confirm("确认要将该成员从业委会中移除吗？移除后该成员将变回普通业主。", function() {
                $.ajax({
                    url: prefix + "/removeCommitteeMember",
                    type: "post",
                    data: { ownerId: ownerId },
                    success: function(res) {
                        if (res.code == 0) {
                            $.modal.msgSuccess("移除业委会成员成功");
                            $.table.refresh();
                        } else {
                            $.modal.alertError("移除失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("移除失败");
                    }
                });
            });
        }

        // 查看详情
        function viewDetail(ownerId) {
            var url = prefix + "/detail/" + ownerId;
            $.modal.openTab("业委会成员详情", url);
        }
    </script>
</body>
</html>
