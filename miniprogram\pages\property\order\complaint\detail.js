// 物业端-投诉工单详情页面
const app = getApp()

Page({
  data: {
    id: '',
    detail: null,
    loading: true,
    processing: false,
    showFeedbackDialog: false,
    feedback: '',
    attachments: []
  },

  onLoad(options) {
    const { id } = options
    if (!id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ id })
    this.loadDetail()
    this.loadAttachments()
  },

  // 加载详情
  async loadDetail() {
    this.setData({ loading: true })
    try {
      const res = await app.request({
        url: '/api/wx/property/complaint/detail',
        method: 'POST',
        data: { id: this.data.id }
      })

      if (res.code === 0 && res.data) {
        this.setData({
          detail: res.data,
          loading: false
        })
      } else {
        throw new Error(res.msg || '获取详情失败')
      }
    } catch (error) {
      console.error('获取投诉详情失败:', error)
      wx.showToast({
        title: '获取详情失败',
        icon: 'none'
      })
      this.setData({ loading: false })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 加载附件
  async loadAttachments() {
    try {
      const res = await app.request({
        url: '/api/wx/file/getAttachments',
        method: 'POST',
        data: { 
          businessId: this.data.id,
          businessType: 'complaint'
        }
      })

      if (res.code === 0) {
        this.setData({ attachments: res.data || [] })
      }
    } catch (error) {
      console.error('获取附件失败:', error)
    }
  },

  // 显示办理完成对话框
  showCompleteDialog() {
    this.setData({ 
      showFeedbackDialog: true,
      feedback: ''
    })
  },

  // 隐藏对话框
  hideFeedbackDialog() {
    this.setData({ showFeedbackDialog: false })
  },

  // 输入反馈内容
  onFeedbackInput(e) {
    this.setData({ feedback: e.detail })
  },

  // 确认办理完成
  async confirmComplete() {
    if (!this.data.feedback.trim()) {
      wx.showToast({
        title: '请填写处理反馈',
        icon: 'none'
      })
      return
    }

    this.setData({ processing: true })
    try {
      const res = await app.request({
        url: '/api/wx/property/complaint/updateStatus',
        method: 'POST',
        data: {
          id: this.data.id,
          status: '2', // 已完成
          feedback: this.data.feedback
        }
      })

      if (res.code === 0) {
        wx.showToast({
          title: '处理成功',
          icon: 'success'
        })
        this.setData({ showFeedbackDialog: false })
        // 重新加载详情
        this.loadDetail()
      } else {
        throw new Error(res.msg || '处理失败')
      }
    } catch (error) {
      console.error('处理失败:', error)
      wx.showToast({
        title: error.message || '处理失败',
        icon: 'none'
      })
    } finally {
      this.setData({ processing: false })
    }
  },

  // 预览图片
  previewImage(e) {
    const { url } = e.currentTarget.dataset
    const urls = this.data.attachments.filter(item => 
      item.fileType && item.fileType.startsWith('image/')
    ).map(item => item.url)
    
    wx.previewImage({
      current: url,
      urls: urls
    })
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      '0': '未处理',
      '1': '处理中',
      '2': '已完成'
    }
    return statusMap[status] || '未处理'
  },

  // 获取状态颜色
  getStatusColor(status) {
    const colorMap = {
      '0': '#ff4d4f',
      '1': '#faad14',
      '2': '#52c41a'
    }
    return colorMap[status] || '#ff4d4f'
  }
})
