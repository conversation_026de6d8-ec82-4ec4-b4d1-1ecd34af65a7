// 物业端-投诉工单管理页面
import {handlePropertyPageShow} from '../../../../utils/pageUtils.js'

const app = getApp()

Page({
  data: {
    activeTab: 0,
    tabs: [
      { key: 'pending', title: '待办' },
      { key: 'completed', title: '已办' }
    ],
    complaintList: [],
    loading: false,
    refreshing: false,
    hasMore: true,
    pageNum: 1,
    pageSize: 10
  },

  onLoad(options) {
    console.log('投诉工单管理页面加载')
  },

  onShow() {
    handlePropertyPageShow(this, this.loadCurrentTabData)
  },

  // 加载当前Tab的数据
  loadCurrentTabData() {
    this.loadComplaintList(true)
  },

  // Tab切换
  onTabChange(e) {
    const index = e.detail.index
    this.setData({
      activeTab: index,
      pageNum: 1,
      hasMore: true,
      complaintList: []
    })
    this.loadCurrentTabData()
  },

  // 加载投诉列表
  async loadComplaintList(refresh = false) {
    if (this.data.loading) return

    this.setData({ loading: true })
    if (refresh) {
      this.setData({ pageNum: 1, hasMore: true })
    }

    // 根据当前标签确定状态筛选条件
    const currentTab = this.data.tabs[this.data.activeTab]
    let statusFilter = ''
    if (currentTab.key === 'pending') {
      // 待办：不传status参数，在前端过滤
      statusFilter = ''
    } else {
      // 已办：已完成(2)
      statusFilter = '2'
    }

    try {
      const requestData = {
        pageNum: this.data.pageNum,
        pageSize: this.data.pageSize
      }

      // 只有已办时才传status参数
      if (statusFilter) {
        requestData.status = statusFilter
      }

      const res = await app.request({
        url: '/api/wx/property/complaint/list',
        method: 'POST',
        data: requestData
      })

      if (res.code === 0) {
        let newList = res.data.list || []

        // 如果是待办，需要前端过滤出status=0或status=1的数据
        if (currentTab.key === 'pending') {
          newList = newList.filter(item => item.status == 0 || item.status == 1)
        }

        this.setData({
          complaintList: refresh ? newList : [...this.data.complaintList, ...newList],
          hasMore: newList.length === this.data.pageSize,
          pageNum: this.data.pageNum + 1
        })
      } else {
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载投诉列表失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false, refreshing: false })
    }
  },

  // 查看详情
  viewDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/property/order/complaint/detail?id=${id}`
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ refreshing: true })
    this.loadCurrentTabData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadCurrentTabData()
    }
  }
})
