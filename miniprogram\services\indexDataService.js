/**
 * 首页数据服务
 * 统一管理首页相关的所有数据获取逻辑
 */

import { getIndexCacheManager, CACHE_TYPES } from '../utils/indexCacheManager.js'
import { formatTime, filterHtmlTags } from '../utils/common.js'
import { handleError } from '../utils/errorHandler.js'

/**
 * 首页数据服务类
 */
class IndexDataService {
  constructor() {
    this.cacheManager = getIndexCacheManager()
    this.app = getApp()
  }

  /**
   * 获取新闻列表
   * @param {string} noticeType 通知类型
   * @returns {Promise<Object>} 新闻数据
   */
  async getNewsList(noticeType = '') {
    try {
      const cacheKey = noticeType || 'all'
      const cachedNews = this.cacheManager.get(CACHE_TYPES.NEWS, cacheKey)

      if (cachedNews) {
        console.log(`[IndexDataService] 使用缓存的新闻数据: ${cacheKey}`)
        return {
          success: true,
          data: cachedNews.data,
          hasMore: cachedNews.hasMore || false,
          fromCache: true
        }
      }

      const res = await this.app.request({
        url: '/api/wx/index/notices',
        method: 'POST',
        data: {
          noticeType: noticeType,
          page: 1,
          pageSize: 5
        }
      })

      if (res.code === 0) {
        const list = (res.data.list || [])
          .map(item => ({
            ...item,
            time: formatTime(item.createTime, 'YYYY-MM-DD'),
            title: filterHtmlTags(item.title),
            categoryName: this._getCategoryName(item.noticeType || item.type || '1')
          }))

        const hasMore = res.data.hasMore || false

        // 更新缓存
        this.cacheManager.set(CACHE_TYPES.NEWS, cacheKey, {
          data: list,
          hasMore: hasMore
        })

        console.log(`[IndexDataService] 新闻数据已缓存: ${cacheKey}`)
        return {
          success: true,
          data: list,
          hasMore: hasMore,
          fromCache: false
        }
      } else {
        throw new Error(res.msg || '获取新闻列表失败')
      }
    } catch (error) {
      console.warn('[IndexDataService] 获取新闻列表失败:', error)
      return {
        success: false,
        error: error.message || error,
        data: [],
        hasMore: false
      }
    }
  }

  /**
   * 获取菜单列表
   * @param {string} source 菜单来源
   * @returns {Promise<Object>} 菜单数据
   */
  async getMenuList(source = 'indexNav') {
    try {
      const cacheKey = source
      const cachedMenu = this.cacheManager.get(CACHE_TYPES.MENU, cacheKey)

      if (cachedMenu) {
        console.log(`[IndexDataService] 使用缓存的菜单数据: ${cacheKey}`)
        return {
          success: true,
          data: cachedMenu,
          fromCache: true
        }
      }

      const res = await this.app.request({
        url: '/api/wx/index/getMenus',
        method: 'POST',
        data: { source }
      })

      if (res.code === 0) {
        const menuData = res.data || []

        // 更新缓存
        this.cacheManager.set(CACHE_TYPES.MENU, cacheKey, menuData)

        console.log(`[IndexDataService] 菜单数据已缓存: ${cacheKey}`)
        return {
          success: true,
          data: menuData,
          fromCache: false
        }
      } else {
        throw new Error(res.msg || '获取菜单列表失败')
      }
    } catch (error) {
      console.warn('[IndexDataService] 获取菜单列表失败:', error)
      return {
        success: false,
        error: error.message || error,
        data: []
      }
    }
  }

  /**
   * 获取通知类型字典
   * @returns {Promise<Object>} 字典数据
   */
  async getNoticeTypes() {
    try {
      const cacheKey = 'sys_notice_type'
      const cachedDict = this.cacheManager.get(CACHE_TYPES.DICT, cacheKey)

      if (cachedDict) {
        console.log('[IndexDataService] 使用缓存的字典数据')
        return {
          success: true,
          data: cachedDict,
          fromCache: true
        }
      }

      const res = await this.app.request({
        url: '/api/wx/data/getNoticeTypes',
        method: 'POST'
      })

      if (res.code === 0) {
        const noticeTypes = res.data || []
        // 添加"全部"选项
        const allTypes = [
          { dictValue: '', dictLabel: '全部', dictSort: 0 },
          ...noticeTypes
        ]

        // 更新缓存
        this.cacheManager.set(CACHE_TYPES.DICT, cacheKey, allTypes)

        console.log('[IndexDataService] 字典数据已缓存')
        return {
          success: true,
          data: allTypes,
          fromCache: false
        }
      } else {
        throw new Error(res.msg || '获取通知类型失败')
      }
    } catch (error) {
      console.warn('[IndexDataService] 获取通知类型失败:', error)
      return {
        success: false,
        error: error.message || error,
        data: [{ dictValue: '', dictLabel: '全部', dictSort: 0 }]
      }
    }
  }

  /**
   * 检查认证状态
   * @param {Object} params 请求参数
   * @param {string} mode 获取模式
   * @returns {Promise<Object>} 认证状态数据
   */
  async checkAuthenticationStatus(params = {}, mode = 'cache_first') {
    try {
      // 使用新的 StatusDataManager
      const { getStatusDataManager, FETCH_MODES } = require('../utils/statusDataManager.js')
      const statusManager = getStatusDataManager()

      const result = await statusManager.getStatusData({
        mode: FETCH_MODES[mode.toUpperCase()] || FETCH_MODES.CACHE_FIRST,
        params,
        silent: false
      })

      if (result.success) {
        console.log('[IndexDataService] 认证状态获取成功', { fromCache: result.fromCache })
        return {
          success: true,
          data: result.data,
          result: true,
          token: result.token,
          fromCache: result.fromCache
        }
      } else if (result.needLogin) {
        return {
          success: false,
          needLogin: true,
          error: '需要重新登录'
        }
      } else {
        throw new Error(result.error || '认证状态检查失败')
      }
    } catch (error) {
      console.warn('[IndexDataService] 认证状态检查失败:', error)
      return {
        success: false,
        error: error.message || error,
        result: false
      }
    }
  }

  /**
   * 获取菜单详情
   * @param {string} menuId 菜单ID
   * @returns {Promise<Object>} 菜单详情
   */
  async getMenuDetail(menuId) {
    try {
      const res = await this.app.request({
        url: '/api/wx/data/menuContent',
        method: 'POST',
        data: { id: menuId }
      })

      if (res.code === 0 && res.data) {
        return {
          success: true,
          data: res.data
        }
      } else {
        throw new Error(res.msg || '获取菜单详情失败')
      }
    } catch (error) {
      console.warn('[IndexDataService] 获取菜单详情失败:', error)
      return {
        success: false,
        error: error.message || error
      }
    }
  }

  /**
   * 批量获取数据
   * @param {Array} requests 请求配置数组
   * @returns {Promise<Object>} 批量结果
   */
  async batchGetData(requests = []) {
    try {
      const results = await Promise.allSettled(
        requests.map(req => this[req.method](...(req.params || [])))
      )

      const batchResult = {}
      requests.forEach((req, index) => {
        const result = results[index]
        batchResult[req.key] = result.status === 'fulfilled' 
          ? result.value 
          : { success: false, error: result.reason }
      })

      return batchResult
    } catch (error) {
      console.warn('[IndexDataService] 批量获取数据失败:', error)
      return {}
    }
  }

  /**
   * 清除指定类型的缓存
   * @param {string} type 缓存类型
   */
  clearCache(type) {
    if (type) {
      this.cacheManager.clear(type)
    } else {
      this.cacheManager.clear()
    }
  }

  /**
   * 获取分类名称（内部方法）
   * @param {string} type 类型值
   * @returns {string} 分类名称
   */
  _getCategoryName(type) {
    // 这里可以根据需要实现分类名称映射
    // 暂时返回默认值，后续可以优化
    return '通知公告'
  }
}

// 单例实例
let instance = null

/**
 * 获取数据服务实例
 * @returns {IndexDataService}
 */
export function getIndexDataService() {
  if (!instance) {
    instance = new IndexDataService()
  }
  return instance
}

export default IndexDataService
