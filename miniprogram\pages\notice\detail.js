import {getLoadingManager, handleError} from '../../utils/errorHandler.js'
import {filterHtmlTags, formatTime} from '../../utils/common.js'
import {FeatureConfig} from '../../utils/configHelper.js'
import fileAccessManager from '../../utils/fileAccessManager.js'

const app = getApp()
const loadingManager = getLoadingManager()

Page({
  data: {
    detail: null,
    loading: true,
    comments: [],
    commentCount: 0,
    // 评论分页相关
    commentPage: 1,
    commentPageSize: 10,
    commentHasMore: true,
    commentLoading: false,
    commentText: '',
    replyTo: null,
    replyToCommentId: null,
    replyToReplyId: null,
    replyToUser: null,
    replyPlaceholder: '',
    submitting: false,
    noticeId: null,
    shouldFocus: false,
    // 新增的互动数据
    articleLiked: false,
    articleLikeCount: 0,
    articleShareCount: 0,
    // 评论功能控制
    commentEnabled: false, // 是否启用评论功能
    // 评论输入框状态
    showFullInput: false,
    // 表情相关
    showEmojiPanel: false,
    emojiList: [
      '😀', '😁', '😂', '😃', '😄', '😅', '😆', '😉',
      '😊', '😋', '😎', '😍', '😘', '😗', '😙', '😚',
      '☺', '😇', '😐', '😑', '😶', '😏', '😣', '😥',
      '😮', '😯', '😪', '😫', '😴', '😌', '😛', '😜',
      '😝', '😒', '😓', '😔', '😕', '😲', '😷', '😖',
      '😞', '😟', '😤', '😢', '😭', '😦', '😧', '😨',
      '😬', '😰', '😱', '😳', '😵', '😡', '😠', '👍',
      '👎', '👌', '✌', '👋', '👏', '💪', '❤', '💔',
      '💕', '💖', '💗', '💙', '💚', '💛', '💜', '💝'
    ],
    // 已读用户相关
    showReadUsers: false,
    readUsersCount: 0,
    readUsersList: [],
    displayReadUsersList: [], // 显示的已读用户列表（前20条）
    showReadUsersModal: false, // 是否显示已读用户弹窗
    hasMoreReadUsers: false // 是否有更多已读用户
  },

  onLoad(options) {
    const { id, shareId } = options
    if (!id) {
      this.handleInvalidId()
      return
    }
    this.setData({
      noticeId: id,
      shareId: shareId || null
    })
    this.initializePage(id)

    // 如果是通过分享链接进入，记录分享访问
    if (shareId) {
      this.recordShareVisit(shareId)
    }
  },

  onShow() {
    // 这里可以添加页面显示时的逻辑
  },

  // 初始化页面
  async initializePage(id) {
    await this.getNoticeDetail(id)
    await this.recordReadLog(id)
    // 只在评论开启时加载评论
    if (this.data.commentEnabled) {
      await this.loadComments(id)
    }
    await this.loadNoticeStats(id)
  },

  // 处理无效ID
  handleInvalidId() {
    wx.showModal({
      title: '提示',
      content: '文章参数无效',
      showCancel: false,
      success: () => {
        wx.navigateBack()
      }
    })
  },

  // 获取新闻详情
  async getNoticeDetail(id) {
    try {
      loadingManager.show('加载中...')
      
      const res = await app.request({
        url: `/api/wx/index/notice/detail/${id}`,
        method: 'GET'
      })
      
      if (res.code === 0 && res.data) {
        const detail = this.processDetailData(res.data)

        // 检查评论功能是否启用
        const globalCommentEnabled = FeatureConfig.isCommentEnabled()
        const noticeCommentEnabled = detail.enableComment === 1
        const commentEnabled = globalCommentEnabled && noticeCommentEnabled

        // 预处理HTML内容，为图片添加样式限制
        if (detail.content) {
          detail.content = this.processHtmlContent(detail.content)
        }

        this.setData({
          detail,
          commentEnabled
        })

        // 设置页面标题
        if (detail.title) {
          wx.setNavigationBarTitle({
            title: detail.title.length > 10
              ? detail.title.substring(0, 10) + '...'
              : detail.title
          })
        }

        // 加载已读用户列表
        await this.loadReadUsers(id)
      } else {
        throw new Error(res.msg || '获取文章详情失败')
      }
    } catch (error) {
      handleError(error, '获取文章详情')
      
      // 显示错误页面或返回上一页
      this.showErrorState()
    } finally {
      this.setData({ loading: false })
      loadingManager.hide()
    }
  },

  // 处理详情数据
  processDetailData(data) {
    return {
      ...data,
      date: data.date ? formatTime(data.date, 'YYYY-MM-DD HH:mm') : null,
      createTime: formatTime(data.createTime, 'YYYY-MM-DD HH:mm'),
      updateTime: data.updateTime ? formatTime(data.updateTime, 'YYYY-MM-DD HH:mm') : null,
      title: filterHtmlTags(data.title),
      // content保持HTML格式用于rich-text显示
      attachments: this.processAttachments(data.attachments || []),
      // 添加作者和社区信息
      authorName: data.authorName,
      communityName: data.communityName
    }
  },

  // 处理附件数据
  processAttachments(attachments) {
    return attachments.map(attachment => ({
      ...attachment,
      icon: this.getFileIcon(attachment.type),
      sizeText: this.formatFileSize(attachment.size)
    }))
  },

  // 获取文件图标
  getFileIcon(fileType) {
    if (!fileType) return '📄'

    const type = fileType.toLowerCase()
    const iconMap = {
      // 图片
      'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️', 'bmp': '🖼️',
      // 文档
      'pdf': '📕', 'doc': '📘', 'docx': '📘', 'xls': '📗', 'xlsx': '📗',
      'ppt': '📙', 'pptx': '📙', 'txt': '📄',
      // 压缩文件
      'zip': '🗜️', 'rar': '🗜️', '7z': '🗜️',
      // 音视频
      'mp3': '🎵', 'wav': '🎵', 'mp4': '🎬', 'avi': '🎬'
    }

    return iconMap[type] || '📎'
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  },

  // 预处理HTML内容，为图片添加样式限制
  processHtmlContent(htmlContent) {
    if (!htmlContent || typeof htmlContent !== 'string') {
      return htmlContent
    }

    // 定义图片样式，添加max-height限制防止图片撑开页面
    const imgStyle = 'width: 100% !important; height: auto !important; max-width: 100% !important; max-height: 70vh !important; display: block !important; margin: 24rpx auto !important; object-fit: contain !important;'

    // 为所有img标签添加样式属性
    const processedContent = htmlContent.replace(
      /<img([^>]*?)>/gi,
      (match, attributes) => {
        // 检查是否已经有style属性
        if (/style\s*=\s*["'][^"']*["']/i.test(attributes)) {
          // 如果已有style属性，在其中添加图片样式
          return match.replace(
            /style\s*=\s*["']([^"']*)["']/i,
            (_, existingStyle) => {
              const separator = existingStyle.trim().endsWith(';') ? ' ' : '; '
              return `style="${existingStyle}${separator}${imgStyle}"`
            }
          )
        } else {
          // 如果没有style属性，添加新的style属性
          return `<img${attributes} style="${imgStyle}">`
        }
      }
    )

    return processedContent
  },

  // 显示错误状态
  showErrorState() {
    wx.showModal({
      title: '加载失败',
      content: '文章加载失败，是否重试？',
      confirmText: '重试',
      cancelText: '返回',
      success: (res) => {
        if (res.confirm) {
          // 重新加载
          const pages = getCurrentPages()
          const currentPage = pages[pages.length - 1]
          const options = currentPage.options
          if (options.id) {
            this.initializePage(options.id)
          }
        } else {
          wx.navigateBack()
        }
      }
    })
  },

  // 查看附件
  async viewAttachment(e) {
    const url = e.currentTarget.dataset.url
    const name = e.currentTarget.dataset.name
    const id = e.currentTarget.dataset.id

    const actualFileId = id

    if (!actualFileId && !url) {
      wx.showToast({
        title: '附件链接无效',
        icon: 'none'
      })
      return
    }

    try {
      loadingManager.show('下载中...')

      // 使用新的智能预览方法，基于文件ID
      await fileAccessManager.previewFile(actualFileId, url, name)

      wx.showToast({
        title: '打开成功',
        icon: 'success'
      })

    } catch (error) {
      handleError(error, '附件下载')
    } finally {
      loadingManager.hide()
    }
  },



  // 加载评论列表
  async loadComments(noticeId, isLoadMore = false) {
    try {
      // 检查评论功能是否开启
      if (!this.data.commentEnabled) {
        return
      }

      // 如果是加载更多，检查是否还有数据
      if (isLoadMore && !this.data.commentHasMore) {
        return
      }

      // 如果正在加载，避免重复请求
      if (this.data.commentLoading) {
        return
      }

      this.setData({ commentLoading: true })

      const page = isLoadMore ? this.data.commentPage : 1
      const res = await app.request({
        url: `/api/wx/notice/${noticeId}/comments`,
        method: 'GET',
        data: {
          page: page,
          pageSize: this.data.commentPageSize
        }
      })

      if (res.code === 0) {
        const newComments = this.processCommentsData(res.data.list || [])
        const totalCount = res.data.total || 0

        let comments = []
        if (isLoadMore) {
          // 加载更多，追加到现有评论
          comments = [...this.data.comments, ...newComments]
        } else {
          // 首次加载或刷新，替换所有评论
          comments = newComments
        }

        // 判断是否还有更多数据
        const hasMore = comments.length < totalCount

        this.setData({
          comments,
          commentCount: totalCount,
          commentPage: isLoadMore ? page + 1 : 2,
          commentHasMore: hasMore
        })
      }
    } catch (error) {
      console.error('加载评论失败:', error)
    } finally {
      this.setData({ commentLoading: false })
    }
  },

  // 加载更多评论
  async loadMoreComments() {
    const detail = this.data.detail
    if (detail && detail.id) {
      await this.loadComments(detail.id, true)
    }
  },

  // 记录阅读统计
  async recordReadLog(noticeId) {
    try {
      await app.request({
        url: `/api/wx/notice/${noticeId}/read`,
        method: 'POST'
      })
    } catch (error) {
      // 阅读统计失败不影响用户体验，静默处理
      console.warn('记录阅读统计失败:', error)
    }
  },

  // 处理评论数据
  processCommentsData(comments) {
    return comments.map(comment => ({
      ...comment,
      createTime: formatTime(comment.createTime, 'MM-DD HH:mm'),
      replies: comment.replies ? comment.replies.map(reply => ({
        ...reply,
        createTime: formatTime(reply.createTime, 'MM-DD HH:mm')
      })) : []
    }))
  },

  // 刷新评论
  async refreshComments() {
    const detail = this.data.detail
    if (detail && detail.id) {
      wx.showLoading({ title: '刷新中...', mask: true })
      try {
        // 重置分页状态
        this.setData({
          commentPage: 1,
          commentHasMore: true,
          comments: []
        })
        await this.loadComments(detail.id)
        wx.showToast({ title: '刷新成功', icon: 'success' })
      } catch (error) {
        wx.showToast({ title: '刷新失败', icon: 'none' })
      } finally {
        wx.hideLoading()
      }
    }
  },

  // 加载更多回复
  async loadMoreReplies(e) {
    const parentId = e.currentTarget.dataset.id
    wx.showLoading({ title: '加载中...', mask: true })

    try {
      const res = await app.request({
        url: `/api/wx/notice/comments/${parentId}/replies`,
        method: 'GET'
      })

      if (res.code === 0) {
        const replies = res.data.map(reply => ({
          ...reply,
          createTime: formatTime(reply.createTime, 'MM-DD HH:mm')
        }))

        // 更新对应评论的回复列表
        const comments = this.data.comments.map(comment => {
          if (comment.id === parentId) {
            return {
              ...comment,
              replies: replies,
              hasMoreReplies: false
            }
          }
          return comment
        })

        this.setData({ comments })
        wx.showToast({ title: '加载成功', icon: 'success' })
      }
    } catch (error) {
      handleError(error, '加载回复')
    } finally {
      wx.hideLoading()
    }
  },

  // 显示回复输入框
  showReplyInput(e) {
    const id = e.currentTarget.dataset.id
    const user = e.currentTarget.dataset.user
    this.setData({
      replyTo: { id, userName: user },
      replyToCommentId: id,
      replyToReplyId: null,
      replyToUser: user,
      replyPlaceholder: `回复 @${user}:`,
      commentText: '',
      showFullInput: true,
      shouldFocus: false
    })

    // 自动定位到评论输入框
    setTimeout(() => {
      wx.pageScrollTo({
        selector: '.comment-input-section',
        duration: 300
      })
    }, 100)

    // 自动聚焦到输入框
    setTimeout(() => {
      this.setData({ shouldFocus: true })
      setTimeout(() => {
        this.setData({ shouldFocus: false })
      }, 500)
    }, 400)
  },

  // 取消回复
  cancelReply() {
    this.setData({
      replyTo: null,
      replyToCommentId: null,
      replyToReplyId: null,
      replyToUser: null,
      replyPlaceholder: '',
      commentText: '',
      shouldFocus: false,
      showFullInput: false,
      showEmojiPanel: false // 同时关闭表情面板
    })
    wx.showToast({ title: '已取消回复', icon: 'none', duration: 1000 })
  },

  // 评论输入
  onCommentInput(e) {
    this.setData({
      commentText: e.detail.value
    })
  },

  // 提交评论
  async submitComment() {
    const { detail, commentText, replyTo, submitting, replyToReplyId } = this.data

    if (!detail || !detail.id) {
      wx.showToast({ title: '页面数据异常', icon: 'none' })
      return
    }

    const content = commentText ? commentText.trim() : ''
    if (!content) {
      wx.showToast({ title: '请输入评论内容', icon: 'none' })
      return
    }

    if (submitting) return

    this.setData({ submitting: true })

    try {
      const params = { content }

      if (replyTo && replyTo.id) {
        params.parentId = replyTo.id
        // 如果是回复回复，添加额外的回复ID
        if (replyToReplyId) {
          params.replyToId = replyToReplyId
        }
      }

      const res = await app.request({
        url: `/api/wx/notice/${detail.id}/comments`,
        method: 'POST',
        data: params,
        header: {
          'Content-Type': 'application/json'
        }
      })

      if (res.code === 0) {
        wx.showModal({
          title: '提交成功',
          content: '您的评论已提交，审核通过后将显示',
          showCancel: false,
          confirmText: '知道了'
        })

        // 清空输入
        this.setData({
          commentText: '',
          replyTo: null,
          replyToCommentId: null,
          replyToReplyId: null,
          shouldFocus: false,
          showFullInput: false,
          replyToUser: null,
          replyPlaceholder: '',
          showEmojiPanel: false // 同时关闭表情面板
        })

        // 重置分页状态并刷新评论列表
        this.setData({
          commentPage: 1,
          commentHasMore: true
        })
        await this.loadComments(detail.id)

        // 滚动到评论区域顶部，让用户看到新评论
        setTimeout(() => {
          wx.pageScrollTo({
            selector: '.comments-section',
            duration: 300
          })
        }, 500)
      } else {
        throw new Error(res.msg || '评论失败')
      }
    } catch (error) {
      handleError(error, '发表评论')
    } finally {
      this.setData({ submitting: false })
    }
  },

  // 分享功能
  async onShareAppMessage() {
    const detail = this.data.detail
    if (!detail) {
      return {
        title: '智慧小区',
        path: '/pages/index/index'
      }
    }

    try {
      // 记录分享统计并获取分享ID
      const res = await app.request({
        url: `/api/wx/notice/${detail.id}/share`,
        method: 'POST'
      })

      let sharePath = `/pages/notice/detail?id=${detail.id}`

      if (res.code === 0 && res.data && res.data.shareId) {
        // 添加分享ID参数用于追踪
        sharePath += `&shareId=${res.data.shareId}`

        // 更新分享统计
        this.setData({
          articleShareCount: res.data.shareCount || this.data.articleShareCount
        })
      }

      return {
        title: detail.title || '智慧小区公告',
        path: sharePath,
        imageUrl: detail.coverImage || ''
      }
    } catch (error) {
      console.error('分享统计失败:', error)
      return {
        title: detail.title || '智慧小区公告',
        path: `/pages/notice/detail?id=${detail.id}`,
        imageUrl: detail.coverImage || ''
      }
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    const detail = this.data.detail
    if (!detail) {
      return {
        title: '智慧小区',
        path: '/pages/index/index'
      }
    }

    // 记录分享统计
    this.recordShareStats(detail.id)

    return {
      title: detail.title || '智慧小区公告',
      path: `/pages/notice/detail?id=${detail.id}`,
      imageUrl: detail.coverImage || ''
    }
  },

  // 记录分享统计
  async recordShareStats(noticeId) {
    try {
      const res = await app.request({
        url: `/api/wx/notice/${noticeId}/share`,
        method: 'POST'
      })

      if (res.code === 0) {
        this.setData({
          articleShareCount: res.data.shareCount
        })
      }
    } catch (error) {
      console.error('分享统计失败:', error)
    }
  },

  // 记录分享访问
  async recordShareVisit(shareId) {
    try {
      const res = await app.request({
        url: `/api/wx/notice/share/visit`,
        method: 'POST',
        data: { shareId: shareId }
      })

      if (res.code === 0) {
        console.log('分享访问记录成功')
      }
    } catch (error) {
      console.error('分享访问记录失败:', error)
    }
  },

  // 点赞评论
  async likeComment(e) {
    const commentId = e.currentTarget.dataset.id
    if (!commentId) return

    try {
      const res = await app.request({
        url: `/api/wx/notice/comments/${commentId}/like`,
        method: 'POST'
      })

      if (res.code === 0) {
        const { isLiked, likeCount } = res.data
        // 更新本地数据
        const comments = this.data.comments.map(comment => {
          if (comment.id === commentId) {
            return {
              ...comment,
              isLiked: isLiked,
              likeCount: likeCount
            }
          }
          return comment
        })
        this.setData({ comments })
      }
    } catch (error) {
      console.error('点赞失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },

  // 点赞回复
  async likeReply(e) {
    const replyId = e.currentTarget.dataset.id
    if (!replyId) return

    try {
      const res = await app.request({
        url: `/api/wx/notice/comments/${replyId}/like`,
        method: 'POST'
      })

      if (res.code === 0) {
        const { isLiked, likeCount } = res.data
        // 更新本地数据
        const comments = this.data.comments.map(comment => {
          if (comment.replies) {
            comment.replies = comment.replies.map(reply => {
              if (reply.id === replyId) {
                return {
                  ...reply,
                  isLiked: isLiked,
                  likeCount: likeCount
                }
              }
              return reply
            })
          }
          return comment
        })
        this.setData({ comments })
      }
    } catch (error) {
      console.error('点赞失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },

  // 回复回复
  replyToReply(e) {
    const { commentId, replyId, user } = e.currentTarget.dataset
    this.setData({
      replyTo: { id: commentId, userName: user },
      replyToCommentId: commentId,
      replyToReplyId: replyId,
      replyToUser: user,
      replyPlaceholder: `回复 @${user}:`,
      commentText: '',
      showFullInput: true,
      shouldFocus: false
    })

    // 自动定位到评论输入框
    setTimeout(() => {
      wx.pageScrollTo({
        selector: '.comment-input-section',
        duration: 300
      })
    }, 100)

    // 自动聚焦到输入框
    setTimeout(() => {
      this.setData({ shouldFocus: true })
      setTimeout(() => {
        this.setData({ shouldFocus: false })
      }, 500)
    }, 400)
  },

  // 文章点赞
  async likeArticle() {
    const { detail } = this.data
    if (!detail || !detail.id) return

    try {
      const res = await app.request({
        url: `/api/wx/notice/${detail.id}/like`,
        method: 'POST'
      })

      if (res.code === 0) {
        const { isLiked, likeCount } = res.data
        this.setData({
          articleLiked: isLiked,
          articleLikeCount: likeCount
        })

        wx.showToast({
          title: isLiked ? '点赞成功' : '已取消点赞',
          icon: 'success',
          duration: 1000
        })
      }
    } catch (error) {
      console.error('点赞失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },



  // 滚动到评论区
  scrollToComments() {
    if (!this.data.commentEnabled) {
      wx.showToast({
        title: '评论功能未开启',
        icon: 'none'
      })
      return
    }
    wx.pageScrollTo({
      selector: '#comments-section',
      duration: 300
    })
  },

  // 显示评论输入框
  showCommentInput() {
    if (!this.data.commentEnabled) {
      wx.showToast({
        title: '评论功能未开启',
        icon: 'none'
      })
      return
    }
    this.setData({
      showFullInput: true,
      shouldFocus: true
    })

    // 延迟聚焦
    setTimeout(() => {
      this.setData({ shouldFocus: false })
    }, 500)
  },

  // 隐藏评论输入框
  hideCommentInput() {
    this.setData({
      showFullInput: false,
      commentText: '',
      replyTo: null,
      replyToCommentId: null,
      replyToReplyId: null,
      replyToUser: null,
      replyPlaceholder: '',
      showEmojiPanel: false // 同时关闭表情面板
    })
  },

  // 防止点击输入框内容时关闭
  preventClose() {
    // 空方法，阻止事件冒泡
  },

  // 切换表情面板
  toggleEmojiPanel() {
    this.setData({
      showEmojiPanel: !this.data.showEmojiPanel
    })
  },

  // 选择表情
  selectEmoji(e) {
    const emoji = e.currentTarget.dataset.emoji
    const currentText = this.data.commentText || ''

    // 将表情插入到当前文本末尾
    this.setData({
      commentText: currentText + emoji,
      showEmojiPanel: false // 选择后关闭表情面板
    })
  },

  // 加载公告统计信息
  async loadNoticeStats(noticeId) {
    try {
      const res = await app.request({
        url: `/api/wx/notice/${noticeId}/stats`,
        method: 'GET'
      })

      if (res.code === 0) {
        const { likeCount, shareCount, isLiked } = res.data
        this.setData({
          articleLikeCount: likeCount,
          articleShareCount: shareCount,
          articleLiked: isLiked
        })
      }
    } catch (error) {
      console.error('加载统计信息失败:', error)
    }
  },

  // 加载已读用户列表
  async loadReadUsers(noticeId) {
    try {
      const res = await app.request({
        url: `/api/wx/notice/${noticeId}/readUsers`,
        method: 'GET'
      })

      if (res.code === 0) {
        const { readCount, readUsers } = res.data

        // 处理时间格式
        const processedReadUsers = readUsers.map(user => ({
          ...user,
          readTimeFormatted: formatTime(user.readTime, 'MM-DD HH:mm')
        }))

        // 处理分页显示
        const displayList = processedReadUsers.slice(0, 20)
        const hasMore = processedReadUsers.length > 20

        this.setData({
          showReadUsers: true,
          readUsersCount: readCount,
          readUsersList: processedReadUsers,
          displayReadUsersList: displayList,
          hasMoreReadUsers: hasMore
        })
      } else {
        // 如果接口返回错误（如未开启公开功能），则不显示已读用户区域
        this.setData({
          showReadUsers: false
        })
      }
    } catch (error) {
      console.error('加载已读用户失败:', error)
      this.setData({
        showReadUsers: false
      })
    }
  },

  // 显示已读用户弹窗
  showReadUsersModal() {
    if (this.data.showReadUsers && this.data.readUsersCount > 0) {
      this.setData({
        showReadUsersModal: true
      })
    }
  },

  // 隐藏已读用户弹窗
  hideReadUsersModal() {
    this.setData({
      showReadUsersModal: false
    })
  },

  // 查看全部已读用户
  viewAllReadUsers() {
    // 跳转到全部已读用户页面
    wx.navigateTo({
      url: `/pages/notice/readUsers?noticeId=${this.data.noticeId}&title=${encodeURIComponent(this.data.detail.title || '公告')}`
    })
  }
})