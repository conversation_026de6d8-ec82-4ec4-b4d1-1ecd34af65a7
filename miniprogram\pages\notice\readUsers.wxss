.container {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 页面标题 */
.page-header {
  background: #fff;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.page-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

/* 已读用户容器 */
.read-users-container {
  background: #fff;
  margin: 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.read-users-list {
  padding: 0 32rpx;
}

.read-user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.read-user-item:last-child {
  border-bottom: none;
}

.read-user-house {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.read-user-time {
  font-size: 26rpx;
  color: #666;
  margin-left: 16rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  gap: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
