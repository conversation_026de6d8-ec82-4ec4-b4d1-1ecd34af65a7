# 百度AI车牌识别配置说明

## 配置位置

百度AI相关配置已移动到 `ehome-oc` 模块中，配置文件位置：
- 配置类：`ehome-oc/src/main/java/com/ehome/oc/config/BaiduAiConfig.java`
- 依赖管理：`ehome-oc/pom.xml`

## 配置步骤

### 1. 申请百度AI服务

1. 访问 [百度AI开放平台](https://ai.baidu.com/)
2. 注册并登录百度账号
3. 进入控制台，创建应用
4. 选择"文字识别" -> "车牌识别"服务
5. 获取应用的 APP_ID、API_KEY、SECRET_KEY

### 2. 配置application.yml

在 `ehome-web/src/main/resources/application.yml` 中添加配置：

```yaml
# 百度AI配置
baidu:
  ai:
    # 是否启用百度AI服务
    enabled: true
    # 应用ID（从百度AI控制台获取）
    app-id: 你的APP_ID
    # API Key（从百度AI控制台获取）
    api-key: 你的API_KEY
    # Secret Key（从百度AI控制台获取）
    secret-key: 你的SECRET_KEY
```

### 3. 环境配置示例

#### 开发环境配置
```yaml
baidu:
  ai:
    enabled: true
    app-id: 12345678
    api-key: abcdefghijklmnopqrstuvwxyz
    secret-key: 1234567890abcdefghijklmnopqrstuvwxyz
```

#### 生产环境配置
建议使用环境变量或配置中心管理敏感信息：

```yaml
baidu:
  ai:
    enabled: true
    app-id: ${BAIDU_AI_APP_ID}
    api-key: ${BAIDU_AI_API_KEY}
    secret-key: ${BAIDU_AI_SECRET_KEY}
```

### 4. 配置验证

启动应用后，可以通过以下方式验证配置：

1. 查看启动日志，确认BaiduAiConfig配置类加载成功
2. 调用车牌识别接口测试功能
3. 检查是否有相关错误日志

## 费用说明

### 百度AI车牌识别计费

- **免费额度**：每月1000次免费调用
- **超出免费额度**：按次计费，具体价格请查看百度AI官网
- **建议**：在生产环境中监控调用次数，避免超出预算

### 成本优化建议

1. **缓存策略**：对识别结果进行短时间缓存
2. **图片预处理**：压缩图片大小，提高识别速度
3. **错误重试**：避免因网络问题导致的重复调用
4. **监控告警**：设置调用次数告警，及时了解使用情况

## 安全注意事项

1. **密钥保护**：不要将API密钥提交到代码仓库
2. **访问控制**：限制API调用的IP地址范围
3. **日志安全**：不要在日志中记录API密钥
4. **定期更换**：定期更换API密钥，提高安全性

## 故障排除

### 常见问题

1. **"百度AI服务未启用"**
   - 检查 `enabled` 配置是否为 `true`
   - 确认配置文件路径正确

2. **"APP_ID不能为空"**
   - 检查 `app-id` 配置是否正确
   - 确认配置文件格式正确（注意缩进）

3. **"API调用失败"**
   - 检查网络连接是否正常
   - 确认API密钥是否有效
   - 检查百度AI服务是否正常

4. **"识别准确率低"**
   - 确保图片清晰度足够
   - 检查光线条件是否良好
   - 确认车牌完整可见

### 调试方法

1. **启用调试日志**：
```yaml
logging:
  level:
    com.ehome.oc.service.impl.PlateRecognitionServiceImpl: DEBUG
```

2. **测试API连通性**：
可以使用百度AI官方提供的测试工具验证密钥是否有效

3. **监控调用情况**：
在百度AI控制台查看API调用统计和错误日志

## 模块化设计说明

将百度AI配置移动到 `ehome-oc` 模块的优势：

1. **模块独立性**：车牌识别功能相关的依赖和配置集中管理
2. **减少耦合**：common模块不依赖特定的第三方服务
3. **便于维护**：相关功能的代码和配置在同一模块中
4. **灵活部署**：可以独立部署或禁用车牌识别功能
