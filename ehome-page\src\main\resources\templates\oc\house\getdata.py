import requests
import time

url_template = "https://admin.xtzhwy.com/mkg/api/v2/Charge/getHouseList?communityID=10772&page={page}&pageSize=20&r=0.7600765709002444"

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36",
   "Cookie": "hiido_ui=0.6490742136296532; osudb_third=35f8e45acf3a46fbb389ff6845d39931e7d7bbcc2ac10c75bde65fd246c918a8caee950042a8164d50cfdd5e5978ee342559aed10276db5dcb31af1ee2476133abbc49c2196932ca65f6b47c1dad64502a8756a018694fd136e229f8af8f250d0fbcaadc4c02f9bd8c7db33c15ba585c03574c20c2a83d82c22a8c35583e0d608e930e8899609bd415d1b3a7252c85330dfdaf8f2c5fa35a0314395fc30c45526c824abf6d07a5c887ed37bb4dc1b4a59a51cb5ab5d2139a83304d1f8bf10ee75f5df9157ec6c9bc63815e914938fdf0f906a1e291b5fd09e8db6c44949617d968ad37674d5bb3142ba1af59030a261886a7ade7b295d5292c44d762563dcc0a; osudb_uid=4806379939; osudb_appid=1435186595; osudb_oar=1af3829e6e5d6b8465958b7127114a177c33b18eb4d14bc7900af4850b2f1e4894d6383c9c3a8b7c28d6c7d97c70da09d5d663de5387c3957f8a0cb40d0ff3e5636e5b8d11e02f42760aa99a4db62ee0bc564a7a05e63ead2b8d67b059aaa4ba4e6b4e45c2740d4c164c836a5c52ac28de50026213dd95ae41ce1ae02eb17c51621657c30ad1803795320a657fb524652ca07f1f9432a23280b04a30e58bd698ca5d5b60d956d03f13168cee8ed64cb1eaaf9038b3238f976a945ef5cfb0853f43900b5dc0254a51626bbff55f29516969a6778bd4e0fc1dd5f28bb569f8f772e0bedf36d57cd98b263cbb45237050db8f15c80bd84c9b2d38940b592980171f; osudb_c=00804c32107a000170000cc276ff28d366cc5f694ff103c6d53dc1fc9c49e5779ac510eec9f398156b97b0023118df4bca2918521539f36914cbe4364bbd4fa8ff1f3ebcde96e2809f20ae5c29fe4a4f227000e018a190ef205614da85363cd5e9a025a7ffa1a700b2af027816aa99c837002e7b3e4159fc93b8; osudb_sex=0; osudb_param=; osudb_ustate=1; osudb_nickname=<PERSON>;"
}

with open("getdata.txt", "w", encoding="utf-8") as f:
    for page in range(1, 29):
        url = url_template.format(page=page)
        try:
            resp = requests.get(url, headers=headers, timeout=10)
            resp.raise_for_status()
            f.write(resp.text + "\n")
            print(f"第{page}页数据写入成功")
        except Exception as e:
            print(f"第{page}页请求失败: {e}")
        time.sleep(1)
