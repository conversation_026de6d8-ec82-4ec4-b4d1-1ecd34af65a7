package com.ehome.oc.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 百度AI配置类
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "baidu.ai")
public class BaiduAiConfig {
    
    /** 应用ID */
    private String appId;
    
    /** API Key */
    private String apiKey;
    
    /** Secret Key */
    private String secretKey;
    
    /** 是否启用 */
    private boolean enabled = true;
    
    public String getAppId() {
        return appId;
    }
    
    public void setAppId(String appId) {
        this.appId = appId;
    }
    
    public String getApiKey() {
        return apiKey;
    }
    
    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }
    
    public String getSecretKey() {
        return secretKey;
    }
    
    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
