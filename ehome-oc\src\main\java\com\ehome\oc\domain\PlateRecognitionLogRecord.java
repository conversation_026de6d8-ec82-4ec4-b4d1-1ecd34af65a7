package com.ehome.oc.domain;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 车牌识别记录实体类
 * 
 * <AUTHOR>
 */
public class PlateRecognitionLogRecord {
    
    /** 记录ID */
    private String logId;
    
    /** 社区ID */
    private String communityId;
    
    /** 用户ID */
    private String userId;
    
    /** 用户姓名 */
    private String userName;
    
    /** 用户手机号 */
    private String userPhone;

    /** 上传文件ID */
    private String fileId;

    /** 识别出的车牌号 */
    private String plateNumber;
    
    /** 识别状态(0:失败 1:成功) */
    private Integer recognitionStatus;
    
    /** 识别置信度 */
    private BigDecimal confidence;
    
    /** 车牌颜色 */
    private String plateColor;
    
    /** 是否找到车主(0:否 1:是) */
    private Integer ownerFound;

    /** 车主ID */
    private String ownerId;

    /** 车主姓名 */
    private String ownerName;
    
    /** 车主电话 */
    private String ownerPhone;
    
    /** 图片大小(字节) */
    private Integer imageSize;
    
    /** 识别耗时(毫秒) */
    private Integer recognitionTime;
    
    /** 错误信息 */
    private String errorMessage;
    
    /** 创建时间 */
    private Date createTime;
    
    /** IP地址 */
    private String ipAddress;
    
    // 构造函数
    public PlateRecognitionLogRecord() {
        this.createTime = new Date();
    }
    
    // Getter和Setter方法
    public String getLogId() {
        return logId;
    }
    
    public void setLogId(String logId) {
        this.logId = logId;
    }
    
    public String getCommunityId() {
        return communityId;
    }
    
    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public String getUserName() {
        return userName;
    }
    
    public void setUserName(String userName) {
        this.userName = userName;
    }
    
    public String getUserPhone() {
        return userPhone;
    }
    
    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getPlateNumber() {
        return plateNumber;
    }
    
    public void setPlateNumber(String plateNumber) {
        this.plateNumber = plateNumber;
    }
    
    public Integer getRecognitionStatus() {
        return recognitionStatus;
    }
    
    public void setRecognitionStatus(Integer recognitionStatus) {
        this.recognitionStatus = recognitionStatus;
    }
    
    public BigDecimal getConfidence() {
        return confidence;
    }
    
    public void setConfidence(BigDecimal confidence) {
        this.confidence = confidence;
    }
    
    public String getPlateColor() {
        return plateColor;
    }
    
    public void setPlateColor(String plateColor) {
        this.plateColor = plateColor;
    }
    
    public Integer getOwnerFound() {
        return ownerFound;
    }
    
    public void setOwnerFound(Integer ownerFound) {
        this.ownerFound = ownerFound;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }
    
    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }
    
    public String getOwnerPhone() {
        return ownerPhone;
    }
    
    public void setOwnerPhone(String ownerPhone) {
        this.ownerPhone = ownerPhone;
    }
    
    public Integer getImageSize() {
        return imageSize;
    }
    
    public void setImageSize(Integer imageSize) {
        this.imageSize = imageSize;
    }
    
    public Integer getRecognitionTime() {
        return recognitionTime;
    }
    
    public void setRecognitionTime(Integer recognitionTime) {
        this.recognitionTime = recognitionTime;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public String getIpAddress() {
        return ipAddress;
    }
    
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
}
