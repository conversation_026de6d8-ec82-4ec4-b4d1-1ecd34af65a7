package com.ehome.oc.service.charge;

import com.ehome.common.utils.LoggerUtils;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 收费绑定业务服务
 */
@Service
public class ChargeBindingService {
    
    private static final String LOGGER_NAME = "charge-binding-service";
    private final Logger logger = LoggerUtils.getLog(LOGGER_NAME);

    @Autowired
    private BillPeriodCalculatorService billPeriodCalculatorService;

    /**
     * 刷新所有收费绑定的下次账单日期
     */
    public void refreshAllNextBillTime() {
        logger.info("开始刷新收费绑定的下次账单日期");
        
        try {
            // 获取所有启用的收费绑定，包含收费标准配置信息
            List<Record> bindings = Db.find(
                    "select cb.id, cb.start_time, cb.end_time, cb.period_num, cb.natural_period, " +
                    "cb.charge_standard_id, cs.accounting_period_day, cs.period_type, cs.incomplete_month_handling " +
                    "from eh_charge_binding cb " +
                    "left join eh_charge_standard cs on cb.charge_standard_id = cs.id " +
                    "where cb.is_active = 1 and cs.is_active = 1");
            
            int updateCount = 0;
            for (Record binding : bindings) {
                Long nextBillTime = calculateNextBillTime(binding);
                if (nextBillTime != null) {
                    Db.update("update eh_charge_binding set next_bill_time = ? where id = ?", 
                            nextBillTime, binding.getLong("id"));
                    updateCount++;
                }
            }
            
            logger.info("刷新收费绑定下次账单日期完成，共更新{}条记录", updateCount);
        } catch (Exception e) {
            logger.error("刷新收费绑定下次账单日期失败", e);
        }
    }

    /**
     * 更新单个收费绑定的下次账单时间
     */
    public void updateNextBillTime(Long bindingId) {
        try {
            // 获取绑定信息，包含收费标准配置
            Record binding = Db.findFirst(
                    "select cb.id, cb.start_time, cb.end_time, cb.period_num, cb.natural_period, " +
                    "cb.charge_standard_id, cs.accounting_period_day, cs.period_type, cs.incomplete_month_handling " +
                    "from eh_charge_binding cb " +
                    "left join eh_charge_standard cs on cb.charge_standard_id = cs.id " +
                    "where cb.id = ?", bindingId);
            
            if (binding != null) {
                Long nextBillTime = calculateNextBillTime(binding);
                if (nextBillTime != null) {
                    Db.update("update eh_charge_binding set next_bill_time = ? where id = ?", 
                            nextBillTime, bindingId);
                    logger.debug("更新收费绑定下次账单时间成功，绑定ID：{}", bindingId);
                }
            }
        } catch (Exception e) {
            logger.error("更新收费绑定下次账单时间失败，绑定ID：{}", bindingId, e);
        }
    }

    /**
     * 根据收费标准配置计算下次账单时间
     */
    public Long calculateNextBillTime(Record binding) {
        return billPeriodCalculatorService.calculateNextBillTime(binding);
    }

}
