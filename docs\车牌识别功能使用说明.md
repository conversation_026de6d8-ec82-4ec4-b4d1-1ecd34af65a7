# 车牌识别挪车功能使用说明

## 功能概述

车牌识别挪车功能允许用户通过拍照识别车牌号，自动查询车主信息并提供一键拨号联系车主的服务。

## 配置要求

### 1. 百度AI配置

百度AI相关配置位于 `ehome-oc` 模块中。

在 `application.yml` 中配置百度AI服务：

```yaml
baidu:
  ai:
    enabled: true
    app-id: 你的百度AI应用ID
    api-key: 你的百度AI API Key
    secret-key: 你的百度AI Secret Key
```

**详细配置说明请参考**: `docs/百度AI配置说明.md`

**获取百度AI密钥步骤：**
1. 访问 [百度AI开放平台](https://ai.baidu.com/)
2. 注册并登录账号
3. 创建应用，选择"车牌识别"服务
4. 获取 APP_ID、API_KEY、SECRET_KEY

### 2. 数据库配置

执行 `sql/vehicle_recognition_optimization.sql` 脚本：

```bash
mysql -u username -p database_name < sql/vehicle_recognition_optimization.sql
```

### 3. 菜单配置

在后台管理系统中添加车牌识别菜单：

- **菜单名称**: 车牌识别
- **菜单类型**: page
- **点击方法**: goToVehicleRecognize  
- **图标**: scan
- **状态**: 启用

## 使用流程

### 用户操作流程

1. **进入功能**: 在小程序导航页面点击"车牌识别"
2. **拍照识别**: 
   - 点击"拍照识别"使用相机拍摄
   - 或点击"从相册选择"选择已有图片
3. **查看结果**: 系统自动识别车牌号并查询车主信息
4. **联系车主**: 点击电话号码或"联系车主挪车"按钮拨打电话

### 技术流程

1. **图片上传**: 小程序将图片上传到服务器
2. **车牌识别**: 后端调用百度AI OCR服务识别车牌号
3. **信息查询**: 根据车牌号查询数据库中的车主信息
4. **结果返回**: 将识别结果和车主信息返回给小程序
5. **用户交互**: 用户可以直接拨打车主电话

## API接口说明

### 1. 车牌识别接口

**接口地址**: `POST /api/wx/vehicle/recognizePlate`

**请求参数**: 
- `file`: 图片文件（multipart/form-data）

**响应示例**:
```json
{
  "code": 200,
  "msg": "识别成功",
  "data": {
    "plateNumber": "京A12345",
    "probability": 0.99,
    "confidence": "99%",
    "ownerInfo": {
      "plateNumber": "京A12345",
      "ownerName": "张三",
      "ownerPhone": "13800138000",
      "houseName": "1号楼101室",
      "parkingSpace": "A-001",
      "vehicleBrand": "奔驰",
      "vehicleModel": "C200"
    }
  }
}
```

### 2. 车主信息查询接口

**接口地址**: `GET /api/wx/vehicle/getOwnerByPlate`

**请求参数**:
- `plateNumber`: 车牌号

**响应格式**: 同上

## 错误处理

### 常见错误及解决方案

1. **"百度AI服务未启用"**
   - 检查 `application.yml` 中的 `baidu.ai.enabled` 配置
   - 确认百度AI密钥配置正确

2. **"未识别到车牌信息"**
   - 确保图片清晰，车牌完整可见
   - 光线充足，避免反光或阴影
   - 车牌角度不要过于倾斜

3. **"未找到该车牌对应的车主信息"**
   - 检查数据库中是否有该车牌的记录
   - 确认车牌号格式正确
   - 检查车辆审核状态是否为已审核

4. **"图片文件大小不能超过5MB"**
   - 压缩图片大小
   - 使用合适的图片格式（jpg、png、bmp）

## 性能优化

### 1. 数据库优化
- 为 `plate_no` 字段添加索引
- 为 `community_id, plate_no` 添加复合索引

### 2. 图片处理优化
- 限制上传图片大小（5MB以内）
- 支持的图片格式：jpg、png、bmp
- 建议图片分辨率：1080p以内

### 3. 缓存策略
- 可以考虑对识别结果进行短时间缓存
- 车主信息查询结果缓存

## 安全考虑

1. **用户权限**: 需要登录且通过身份认证
2. **数据保护**: 不在日志中记录敏感的车主信息
3. **接口限流**: 可以考虑添加接口调用频率限制
4. **图片安全**: 上传的图片仅用于识别，不长期存储

## 监控和日志

系统会记录以下关键信息：
- 车牌识别请求和结果
- 车主信息查询记录
- 错误和异常情况
- 用户操作行为

建议定期检查日志，监控功能使用情况和错误率。
