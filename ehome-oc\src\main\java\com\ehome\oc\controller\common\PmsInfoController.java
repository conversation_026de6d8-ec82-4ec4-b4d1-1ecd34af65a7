package com.ehome.oc.controller.common;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.domain.entity.SysUser;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.ShiroUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.framework.shiro.service.SysPasswordService;
import com.ehome.jfinal.model.OcInfoModel;
import com.ehome.jfinal.model.PmsInfoModel;
import com.ehome.oc.service.ICommunityService;
import com.ehome.oc.service.impl.DataInitServiceImpl;
import com.ehome.system.service.ISysDeptService;
import com.ehome.system.service.ISysUserService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

/**
 * 物业信息管理控制器
 */
@Controller
@RequestMapping("/pms/info")
public class PmsInfoController extends BaseController {
    private static final String PREFIX = "pms/info";

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ICommunityService communityService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private DataInitServiceImpl dataInitService;

    /**
     * 跳转到物业管理页面
     */
    @RequestMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list";
    }

    /**
     * 跳转到新增物业页面
     */
    @RequestMapping("/add")
    public String add() {
        return PREFIX + "/add";
    }

    /**
     * 跳转到编辑物业页面
     *
     * @param pmsId 物业ID
     * @param mmap 数据传输对象
     */
    @GetMapping("/edit/{pmsId}")
    public String edit(@PathVariable("pmsId") String pmsId, ModelMap mmap) {
        PmsInfoModel pmsInfo = PmsInfoModel.dao.findById(pmsId);
        mmap.put("pmsInfo", pmsInfo.toMap());
        return PREFIX + "/edit";
    }

    /**
     * 查询物业列表
     * 支持以下查询条件:
     * - 物业名称(模糊查询)
     * - 物业地址(模糊查询)
     * - 物业管理员(精确匹配)
     * - 联系电话(精确匹配)
     * - 公司法人(精确匹配)
     * - 成立日期范围
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select *",
                sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    /**
     * 获取单条物业记录
     * 
     * @return 包含物业信息的响应对象
     */
    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        String pmsId = params.getString("pms_id");
        if (StringUtils.isEmpty(pmsId)) {
            return AjaxResult.error("物业ID不能为空");
        }
        PmsInfoModel model = PmsInfoModel.dao.findById(pmsId);
        return AjaxResult.success(null, model.toMap());
    }

    /**
     * 新增物业信息
     */
    @PostMapping("/addData")
    @ResponseBody
    @Log(title = "新增物业信息", businessType = BusinessType.INSERT)
    public AjaxResult addData() {
        JSONObject params = getParams();
        PmsInfoModel model = new PmsInfoModel();
        model.setColumns(params);
        String pmsId = null;
        model.set("create_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        String ocId = null;
        String flag = null;
        if(model.save()) {
            pmsId = model.getStr("pms_id");
            if(StringUtils.isEmpty(pmsId)){
                return AjaxResult.error("物业ID不能为空");
            }
            OcInfoModel ocInfo = new OcInfoModel();
            ocInfo.set("pms_id", pmsId);
            ocInfo.set("oc_code", Seq.getRandomLetters(5, 1));
            ocInfo.set("pms_name", params.getString("pms_name"));
            ocInfo.set("oc_name", params.getString("pms_name"));
            ocInfo.set("create_time", DateUtils.getTime());
            ocInfo.set("create_by", getLoginName());
            if(ocInfo.save()){
                ocId = ocInfo.getStr("oc_id");
                dataInitService.initAllData(ocId, dataInitService.getAvailableTemplates());
            }

            Record deptRecord = new Record();
            deptRecord.set("parent_id", "0");
            deptRecord.set("pms_id",pmsId);
            deptRecord.set("ancestors","0");
            deptRecord.set("dept_name",model.get("pms_name"));
            deptRecord.set("create_by",getLoginName());
            deptRecord.set("create_time", DateUtils.getNowDate());
            Db.save("sys_dept","dept_id",deptRecord);
            Long deptId = deptRecord.getLong("dept_id");

            SysUser sysUser = new SysUser();
            sysUser.setSalt(ShiroUtils.randomSalt());

            String loginName = model.getStr("manager");
            Record userRecord = new Record();
            userRecord.set("dept_id", deptId);
            userRecord.set("login_name", loginName);
            userRecord.set("user_name", loginName);
            sysUser.setLoginName(loginName);
            userRecord.set("password", passwordService.encryptPassword(loginName, loginName+"#123", sysUser.getSalt()));
            userRecord.set("salt", sysUser.getSalt());
            userRecord.set("pwd_update_date",DateUtils.getNowDate());
            userRecord.set("create_by",getLoginName());
            if (!userService.checkLoginNameUnique(sysUser)){
                flag = "账号已经存在";
            }else{
                Db.save("sys_user", "user_id", userRecord);
                String userId = userRecord.getStr("user_id");
                logger.info("新增物业信息成功，用户ID: {}", userId);

                Db.update("insert into sys_user_role(user_id,role_id) values(?,?)",userId,"100");
                logger.info("新增物业信息成功，用户角色已分配");
            }
        }else{
            flag = "插入失败";
        }
        if(flag!=null){
            PmsInfoModel.dao.deleteById(pmsId);
            OcInfoModel.dao.deleteById(ocId);
            return AjaxResult.error(flag);
        }
        return AjaxResult.success();
    }

    /**
     * 修改物业信息
     */
    @Log(title = "更改物业信息", businessType = BusinessType.UPDATE)
    @PostMapping("/editSave")
    @ResponseBody
    public AjaxResult editSave() {
        JSONObject params = getParams();
        PmsInfoModel model = new PmsInfoModel();
        model.setColumns(params);
        return toAjax(model.update());
    }

    /**
     * 删除物业信息
     *
     * @param ids 物业ID,多个以逗号分隔
     */
    @Log(title = "删除物业", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return error("参数id不能为空");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            Db.update("update eh_pms_info set status = ?  where pms_id = ?", 1,id);
            Db.update("update eh_community set oc_state = ?  where pms_id = ?", 1,id);
        }
        return success();
    }

    /**
     * 校验物业名称是否唯一
     */
    @PostMapping("/checkName")
    @ResponseBody
    public boolean checkName() {
        JSONObject params = getParams();
        String name = params.getString("name");
        if (StringUtils.isEmpty(name)) {
            return false;
        }
        EasySQL sql = new EasySQL();
        sql.append("select * from eh_pms_info where 1=1");
        sql.append(name, "and pms_name = ?");
        sql.append(params.getString("pms_id"), "and pms_id <> ?");
        PmsInfoModel model = PmsInfoModel.dao.findFirst(sql.getSQL(), sql.getParams());
        return model == null;
    }
    /**
     * 修改物业状态
     */
    @PostMapping("/changeStatus")
    @ResponseBody
    public AjaxResult changeStatus() {
        JSONObject params = getParams();
        String pmsId = params.getString("pms_id");
        String status = params.getString("status");
        if (StringUtils.isEmpty(pmsId)) {
            return AjaxResult.error("物业ID不能为空");
        }
        if (StringUtils.isEmpty(status)) {
            return AjaxResult.error("状态不能为空");
        }
        PmsInfoModel model = PmsInfoModel.dao.findById(pmsId);
        model.set("status", status);
        return toAjax(model.update());
    }

    /**
     * 构建列表查询SQL
     */
    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_pms_info where 1=1");

        // 物业名称 - 模糊查询
        sql.appendLike(params.getString("name"), "and pms_name like ?");
        // 物业地址 - 模糊查询
        sql.appendLike(params.getString("address"), "and address like ?");
        // 物业管理员 - 精确匹配
        sql.append(params.getString("manager"), "and manager = ?");
        // 联系电话 - 精确匹配
        sql.append(params.getString("phone"), "and phone = ?");
        // 公司法人 - 精确匹配
        sql.append(params.getString("legal_person"), "and legal_person = ?");
        // 成立日期范围查询
        String beginDate = params.getString("beginDate");
        sql.append(beginDate, "and establishment_date >= ?");
        String endDate = params.getString("endDate");
        sql.append(endDate, "and establishment_date <= ?");

        // 默认按创建时间倒序
        sql.append("order by create_time desc");
        return sql;
    }
}
