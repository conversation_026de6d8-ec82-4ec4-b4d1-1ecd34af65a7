package com.ehome.oc.service.charge;

import com.ehome.common.utils.DateUtils;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 账期计算服务
 * 统一处理所有账期相关的计算逻辑
 */
@Service
public class BillPeriodCalculatorService {

    private static final Logger logger = LoggerFactory.getLogger(BillPeriodCalculatorService.class);

    /**
     * 计算账期信息
     * @param binding 收费绑定信息（包含收费标准配置）
     * @param specifiedMonth 指定月份（格式：yyyy-MM），为null时基于next_bill_time计算
     * @return 账期信息Map，包含startTime、endTime、inMonth、startDate、endDate
     */
    public Map<String, Object> calculateBillPeriod(Record binding, String specifiedMonth) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Integer naturalPeriod = binding.getInt("natural_period");
            Integer periodNum = binding.getInt("period_num");
            if (periodNum == null || periodNum <= 0) {
                periodNum = 1; // 默认1个月
            }
            
            if (specifiedMonth != null) {
                // 指定月份模式：用于批量生成和手动生成
                result = calculateSpecifiedMonthPeriod(binding, specifiedMonth, naturalPeriod, periodNum);
            } else {
                // 基于next_bill_time模式：用于定时任务自动生成
                result = calculateNextBillTimePeriod(binding, naturalPeriod, periodNum);
            }
            
            // 处理不足月情况
            result = handleIncompleteMonth(binding, result);
            
            logger.debug("计算账期完成，绑定ID：{}，账期：{} - {}，月份：{}", 
                    binding.getLong("id"), result.get("startTime"), result.get("endTime"), result.get("inMonth"));
            
            return result;
            
        } catch (Exception e) {
            logger.error("计算账期失败，绑定ID：{}", binding.getLong("id"), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 计算指定月份的账期
     */
    private Map<String, Object> calculateSpecifiedMonthPeriod(Record binding, String specifiedMonth,
                                                            Integer naturalPeriod, Integer periodNum) throws Exception {
        Map<String, Object> result = new HashMap<>();

        // 验证指定月份是否在收费绑定的有效期内
        if (!isMonthInBindingRange(binding, specifiedMonth)) {
            logger.debug("指定月份{}不在收费绑定有效期内，绑定ID：{}", specifiedMonth, binding.getLong("id"));
            return result; // 返回空结果
        }

        // 解析指定月份
        Date monthDate = DateUtils.parseDate(specifiedMonth + "-01", DateUtils.YYYY_MM_DD);
        Calendar monthCal = Calendar.getInstance();
        monthCal.setTime(monthDate);
        
        Calendar startCal = Calendar.getInstance();
        Calendar endCal = Calendar.getInstance();
        
        if (isNaturalPeriod(naturalPeriod)) {
            // 自然月模式：第一期从绑定开始时间到当月月末，后续期为完整自然月
            Long bindingStartTime = binding.getLong("start_time");

            if (bindingStartTime != null && bindingStartTime > 0) {
                Date bindingStartDate = new Date(bindingStartTime * 1000);
                Calendar bindingStartCal = Calendar.getInstance();
                bindingStartCal.setTime(bindingStartDate);

                // 判断是否为第一期账单（指定月份是否为绑定开始月份）
                String bindingStartMonth = DateUtils.parseDateToStr("yyyy-MM", bindingStartDate);

                if (specifiedMonth.equals(bindingStartMonth)) {
                    // 第一期：从绑定开始时间到当月+周期月数的月末
                    startCal.setTime(bindingStartDate);

                    // 结束时间：绑定开始月份 + (周期-1) 个月的月末
                    endCal.setTime(bindingStartDate);
                    endCal.add(Calendar.MONTH, periodNum - 1);
                    endCal.set(Calendar.DAY_OF_MONTH, endCal.getActualMaximum(Calendar.DAY_OF_MONTH));
                    endCal.set(Calendar.HOUR_OF_DAY, 23);
                    endCal.set(Calendar.MINUTE, 59);
                    endCal.set(Calendar.SECOND, 59);
                    endCal.set(Calendar.MILLISECOND, 999);

                    logger.debug("自然月模式第一期，绑定开始：{}，账期：{} - {}",
                            DateUtils.parseDateToStr("yyyy-MM-dd", bindingStartDate),
                            DateUtils.parseDateToStr("yyyy-MM-dd", startCal.getTime()),
                            DateUtils.parseDateToStr("yyyy-MM-dd", endCal.getTime()));
                } else {
                    // 后续期：完整的自然月周期
                    startCal.setTime(monthDate);
                    startCal.set(Calendar.DAY_OF_MONTH, 1);
                    startCal.set(Calendar.HOUR_OF_DAY, 0);
                    startCal.set(Calendar.MINUTE, 0);
                    startCal.set(Calendar.SECOND, 0);
                    startCal.set(Calendar.MILLISECOND, 0);

                    // 结束时间为开始时间加上周期月数，然后到该月的最后一天
                    endCal.setTime(startCal.getTime());
                    endCal.add(Calendar.MONTH, periodNum);
                    endCal.add(Calendar.DAY_OF_MONTH, -1); // 回到上个月的最后一天
                    endCal.set(Calendar.HOUR_OF_DAY, 23);
                    endCal.set(Calendar.MINUTE, 59);
                    endCal.set(Calendar.SECOND, 59);
                    endCal.set(Calendar.MILLISECOND, 999);

                    logger.debug("自然月模式后续期，指定月份：{}，周期：{}个月，账期：{} - {}",
                            specifiedMonth, periodNum,
                            DateUtils.parseDateToStr("yyyy-MM-dd", startCal.getTime()),
                            DateUtils.parseDateToStr("yyyy-MM-dd", endCal.getTime()));
                }
            } else {
                // 如果没有绑定开始时间，回退到简单的自然月模式
                logger.warn("绑定开始时间为空，使用简单自然月模式，绑定ID：{}", binding.getLong("id"));
                startCal.setTime(monthDate);
                startCal.set(Calendar.DAY_OF_MONTH, 1);
                startCal.set(Calendar.HOUR_OF_DAY, 0);
                startCal.set(Calendar.MINUTE, 0);
                startCal.set(Calendar.SECOND, 0);
                startCal.set(Calendar.MILLISECOND, 0);

                endCal.setTime(startCal.getTime());
                endCal.add(Calendar.MONTH, periodNum);
                endCal.add(Calendar.DAY_OF_MONTH, -1);
                endCal.set(Calendar.HOUR_OF_DAY, 23);
                endCal.set(Calendar.MINUTE, 59);
                endCal.set(Calendar.SECOND, 59);
                endCal.set(Calendar.MILLISECOND, 999);
            }
            
        } else {
            // 非自然月模式：基于绑定开始时间的日期
            Long bindingStartTime = binding.getLong("start_time");
            if (bindingStartTime != null && bindingStartTime > 0) {
                Date bindingStartDate = new Date(bindingStartTime * 1000);
                Calendar bindingStartCal = Calendar.getInstance();
                bindingStartCal.setTime(bindingStartDate);
                int startDay = bindingStartCal.get(Calendar.DAY_OF_MONTH);
                
                // 设置账期开始时间为指定月份的绑定开始日期
                startCal.setTime(monthDate);
                startCal.set(Calendar.DAY_OF_MONTH, Math.min(startDay, startCal.getActualMaximum(Calendar.DAY_OF_MONTH)));
                startCal.set(Calendar.HOUR_OF_DAY, 0);
                startCal.set(Calendar.MINUTE, 0);
                startCal.set(Calendar.SECOND, 0);
                startCal.set(Calendar.MILLISECOND, 0);
                
                // 账期结束时间为下个周期的前一天
                endCal.setTime(startCal.getTime());
                endCal.add(Calendar.MONTH, periodNum);
                endCal.add(Calendar.DAY_OF_MONTH, -1);
                endCal.set(Calendar.HOUR_OF_DAY, 23);
                endCal.set(Calendar.MINUTE, 59);
                endCal.set(Calendar.SECOND, 59);
                endCal.set(Calendar.MILLISECOND, 999);
            } else {
                // 如果没有绑定开始时间，回退到自然月模式
                logger.warn("绑定开始时间为空，回退到自然月模式，绑定ID：{}", binding.getLong("id"));
                return calculateSpecifiedMonthPeriod(binding, specifiedMonth, 1, periodNum);
            }
        }
        
        // 转换为yyyyMMdd格式
        Integer startTime = Integer.parseInt(DateUtils.parseDateToStr("yyyyMMdd", startCal.getTime()));
        Integer endTime = Integer.parseInt(DateUtils.parseDateToStr("yyyyMMdd", endCal.getTime()));
        
        result.put("startTime", startTime);
        result.put("endTime", endTime);
        result.put("inMonth", specifiedMonth);
        result.put("startDate", startCal.getTime());
        result.put("endDate", endCal.getTime());
        
        return result;
    }
    
    /**
     * 基于next_bill_time计算账期
     */
    private Map<String, Object> calculateNextBillTimePeriod(Record binding, Integer naturalPeriod, Integer periodNum) {
        Map<String, Object> result = new HashMap<>();
        
        Long nextBillTime = binding.getLong("next_bill_time");
        if (nextBillTime == null || nextBillTime <= 0) {
            logger.warn("next_bill_time为空或无效，绑定ID：{}", binding.getLong("id"));
            return result;
        }
        
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(nextBillTime * 1000);
        
        if (isNaturalPeriod(naturalPeriod)) {
            // 自然月模式：基于next_bill_time的月份计算自然月账期
            Calendar startCal = Calendar.getInstance();
            startCal.setTime(cal.getTime());
            startCal.add(Calendar.MONTH, -periodNum);
            startCal.set(Calendar.DAY_OF_MONTH, 1);
            startCal.set(Calendar.HOUR_OF_DAY, 0);
            startCal.set(Calendar.MINUTE, 0);
            startCal.set(Calendar.SECOND, 0);
            startCal.set(Calendar.MILLISECOND, 0);
            
            Calendar endCal = Calendar.getInstance();
            endCal.setTime(startCal.getTime());
            endCal.add(Calendar.MONTH, periodNum);
            endCal.add(Calendar.DAY_OF_MONTH, -1);
            endCal.set(Calendar.HOUR_OF_DAY, 23);
            endCal.set(Calendar.MINUTE, 59);
            endCal.set(Calendar.SECOND, 59);
            endCal.set(Calendar.MILLISECOND, 999);
            
            // 账期月份使用开始时间的月份
            String inMonth = DateUtils.parseDateToStr("yyyy-MM", startCal.getTime());
            
            Integer startTime = Integer.parseInt(DateUtils.parseDateToStr("yyyyMMdd", startCal.getTime()));
            Integer endTime = Integer.parseInt(DateUtils.parseDateToStr("yyyyMMdd", endCal.getTime()));
            
            result.put("startTime", startTime);
            result.put("endTime", endTime);
            result.put("inMonth", inMonth);
            result.put("startDate", startCal.getTime());
            result.put("endDate", endCal.getTime());
            
        } else {
            // 非自然月模式：基于next_bill_time倒推账期
            Integer billEndTime = Integer.parseInt(DateUtils.parseDateToStr("yyyyMMdd", cal.getTime()));
            
            cal.add(Calendar.MONTH, -periodNum);
            Integer billStartTime = Integer.parseInt(DateUtils.parseDateToStr("yyyyMMdd", cal.getTime()));
            
            String inMonth = DateUtils.parseDateToStr("yyyy-MM", cal.getTime());
            
            result.put("startTime", billStartTime);
            result.put("endTime", billEndTime);
            result.put("inMonth", inMonth);
            result.put("startDate", cal.getTime());
            result.put("endDate", new Date(nextBillTime * 1000));
        }
        
        return result;
    }
    
    /**
     * 判断是否为自然月模式
     */
    private boolean isNaturalPeriod(Integer naturalPeriod) {
        return naturalPeriod != null && naturalPeriod == 1;
    }

    /**
     * 判断账期是否为完整月份
     * @param startDate 账期开始日期
     * @param endDate 账期结束日期
     * @return true表示完整月份，false表示不足月
     */
    private boolean isCompleteMonth(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return false;
        }

        Calendar startCal = Calendar.getInstance();
        startCal.setTime(startDate);

        Calendar endCal = Calendar.getInstance();
        endCal.setTime(endDate);

        // 检查是否为同一个月
        if (startCal.get(Calendar.YEAR) != endCal.get(Calendar.YEAR) ||
            startCal.get(Calendar.MONTH) != endCal.get(Calendar.MONTH)) {
            return false;
        }

        // 检查开始日期是否为月初（1号）
        boolean isStartOfMonth = startCal.get(Calendar.DAY_OF_MONTH) == 1;

        // 检查结束日期是否为月末
        int lastDayOfMonth = startCal.getActualMaximum(Calendar.DAY_OF_MONTH);
        boolean isEndOfMonth = endCal.get(Calendar.DAY_OF_MONTH) == lastDayOfMonth;

        return isStartOfMonth && isEndOfMonth;
    }

    /**
     * 判断指定月份是否在收费绑定的有效期内
     */
    private boolean isMonthInBindingRange(Record binding, String specifiedMonth) {
        try {
            Long startTime = binding.getLong("start_time");
            Long endTime = binding.getLong("end_time");

            if (startTime == null || startTime <= 0) {
                return false;
            }

            Date bindingStartDate = new Date(startTime * 1000);

            // 获取绑定开始月份
            String bindingStartMonth = DateUtils.parseDateToStr("yyyy-MM", bindingStartDate);

            // 指定月份不能早于绑定开始月份
            if (specifiedMonth.compareTo(bindingStartMonth) < 0) {
                return false;
            }

            // 如果有结束时间，指定月份不能晚于绑定结束月份
            if (endTime != null && endTime > 0) {
                Date bindingEndDate = new Date(endTime * 1000);
                String bindingEndMonth = DateUtils.parseDateToStr("yyyy-MM", bindingEndDate);

                if (specifiedMonth.compareTo(bindingEndMonth) > 0) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            logger.error("验证月份范围失败，绑定ID：{}，指定月份：{}", binding.getLong("id"), specifiedMonth, e);
            return false;
        }
    }
    
    /**
     * 处理不足月情况和绑定结束时间限制
     */
    private Map<String, Object> handleIncompleteMonth(Record binding, Map<String, Object> billPeriod) {
        Date startDate = (Date) billPeriod.get("startDate");
        Date endDate = (Date) billPeriod.get("endDate");
        Long bindingStartTime = binding.getLong("start_time");
        Long bindingEndTime = binding.getLong("end_time");
        Integer naturalPeriod = binding.getInt("natural_period");

        if (startDate == null || endDate == null) {
            return billPeriod;
        }

        boolean isIncompleteMonth = false;
        Date actualStartDate = startDate;
        Date actualEndDate = endDate;

        // 首先处理绑定结束时间限制（无条件处理）
        if (bindingEndTime != null && bindingEndTime > 0) {
            Date bindingEnd = new Date(bindingEndTime * 1000);

            logger.debug("检查绑定结束时间影响，绑定ID：{}，绑定结束时间：{}，计算账期结束时间：{}",
                    binding.getLong("id"),
                    DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", bindingEnd),
                    DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", endDate));

            // 修复边界条件：当绑定结束时间小于等于计算出的账期结束时间时，都需要调整
            if (!bindingEnd.after(endDate)) {
                actualEndDate = bindingEnd;
                isIncompleteMonth = true;

                logger.debug("账期结束时间被绑定结束时间限制，绑定ID：{}，调整前：{}，调整后：{}",
                        binding.getLong("id"),
                        DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", endDate),
                        DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", actualEndDate));
            }
        }

        // 然后处理不足月配置相关的逻辑
        Integer incompleteMonthHandling = binding.getInt("incomplete_month_handling");
        if (incompleteMonthHandling != null) {
            // 检查账期开始是否受绑定开始时间影响
            if (bindingStartTime != null && bindingStartTime > 0) {
                Date bindingStart = new Date(bindingStartTime * 1000);
                if (bindingStart.after(startDate)) {
                    actualStartDate = bindingStart;
                    isIncompleteMonth = true;
                }
            }

            // 增强自然月模式的不足月判断
            if (isNaturalPeriod(naturalPeriod)) {
                // 检查是否为完整月份
                if (!isCompleteMonth(actualStartDate, actualEndDate)) {
                    isIncompleteMonth = true;
                    logger.debug("自然月模式检测到不足月情况，绑定ID：{}，账期：{} - {}",
                            binding.getLong("id"),
                            DateUtils.parseDateToStr("yyyy-MM-dd", actualStartDate),
                            DateUtils.parseDateToStr("yyyy-MM-dd", actualEndDate));
                }
            }
        }
        
        if (isIncompleteMonth) {
            // 更新实际的账期时间
            billPeriod.put("startDate", actualStartDate);
            billPeriod.put("endDate", actualEndDate);
            billPeriod.put("startTime", Integer.parseInt(DateUtils.parseDateToStr("yyyyMMdd", actualStartDate)));
            billPeriod.put("endTime", Integer.parseInt(DateUtils.parseDateToStr("yyyyMMdd", actualEndDate)));
            billPeriod.put("isIncompleteMonth", true);

            // 根据不足月处理方式设置标记（只有配置了不足月处理时才设置）
            if (incompleteMonthHandling != null) {
                switch (incompleteMonthHandling) {
                    case 1: // 按天收费
                        billPeriod.put("incompleteMonthHandling", "BY_DAY");
                        break;
                    case 2: // 按月收费
                        billPeriod.put("incompleteMonthHandling", "BY_MONTH");
                        break;
                    case 3: // 不收费
                        billPeriod.put("incompleteMonthHandling", "NO_CHARGE");
                        break;
                }

                logger.debug("检测到不足月情况，绑定ID：{}，处理方式：{}，实际账期：{} - {}",
                        binding.getLong("id"), incompleteMonthHandling,
                        DateUtils.parseDateToStr("yyyy-MM-dd", actualStartDate),
                        DateUtils.parseDateToStr("yyyy-MM-dd", actualEndDate));
            } else {
                logger.debug("检测到绑定结束时间限制，绑定ID：{}，实际账期：{} - {}",
                        binding.getLong("id"),
                        DateUtils.parseDateToStr("yyyy-MM-dd", actualStartDate),
                        DateUtils.parseDateToStr("yyyy-MM-dd", actualEndDate));
            }
        }
        
        return billPeriod;
    }
    
    /**
     * 计算下次账单时间
     * 用于更新收费绑定的next_bill_time字段
     */
    public Long calculateNextBillTime(Record binding) {
        try {
            Long startTime = binding.getLong("start_time");
            Long endTime = binding.getLong("end_time");
            Integer periodNum = binding.getInt("period_num");
            Integer naturalPeriod = binding.getInt("natural_period");
            Long bindingId = binding.getLong("id");

            if (startTime == null || startTime <= 0 || periodNum == null || periodNum <= 0) {
                return null;
            }

            Date startDate = new Date(startTime * 1000);
            Date today = new Date();

            // 如果开始日期在未来，则下次账单日期就是开始日期
            if (startDate.after(today)) {
                return startTime;
            }

            // 如果有结束时间且已过期，则不计算下次账单时间
            if (endTime != null && endTime > 0) {
                Date endDate = new Date(endTime * 1000);
                if (endDate.before(today)) {
                    return null;
                }
            }

            // 查找该绑定最后一个账单的账期结束时间
            Record lastBill = Db.findFirst(
                    "select end_time from eh_charge_bill " +
                    "where charge_binding_id = ? order by end_time desc limit 1", bindingId);

            Calendar nextBillCal = Calendar.getInstance();

            if (lastBill != null) {
                // 如果有历史账单，从最后一个账单的结束时间开始计算
                Long lastBillEndTime = lastBill.getLong("end_time");

                // 将 yyyyMMdd 格式的整数转换为正确的日期
                Date lastBillEndDate = parseYyyyMmddToDate(lastBillEndTime);
                if (lastBillEndDate != null) {
                    nextBillCal.setTime(lastBillEndDate);
                    nextBillCal.add(Calendar.DAY_OF_MONTH, 1); // 下一天作为新账期开始
                    logger.debug("基于最后账单结束时间计算，绑定ID：{}，最后账单结束：{}，下次账单基准：{}",
                            bindingId, lastBillEndTime, DateUtils.parseDateToStr("yyyy-MM-dd", nextBillCal.getTime()));
                } else {
                    // 如果日期解析失败，回退到使用绑定开始时间
                    logger.warn("解析最后账单结束时间失败，使用绑定开始时间，绑定ID：{}，end_time：{}", bindingId, lastBillEndTime);
                    nextBillCal.setTime(startDate);
                }
            } else {
                // 如果没有历史账单，从绑定开始时间计算
                nextBillCal.setTime(startDate);
            }

            if (isNaturalPeriod(naturalPeriod)) {
                // 自然月模式：计算下次账单生成时间
                if (lastBill == null) {
                    // 没有历史账单，下次账单时间应该是绑定开始时间
                    nextBillCal.setTime(startDate);

                    logger.debug("自然月模式首次计算，绑定ID：{}，绑定开始：{}，下次账单时间：{}",
                            bindingId, DateUtils.parseDateToStr("yyyy-MM-dd", startDate),
                            DateUtils.parseDateToStr("yyyy-MM-dd", nextBillCal.getTime()));
                } else {
                    // 有历史账单，下次账单时间为下个周期的1号
                    nextBillCal.add(Calendar.MONTH, periodNum);
                    nextBillCal.set(Calendar.DAY_OF_MONTH, 1);
                    nextBillCal.set(Calendar.HOUR_OF_DAY, 0);
                    nextBillCal.set(Calendar.MINUTE, 0);
                    nextBillCal.set(Calendar.SECOND, 0);
                    nextBillCal.set(Calendar.MILLISECOND, 0);
                }
            } else {
                // 非自然月模式：按周期推进
                nextBillCal.add(Calendar.MONTH, periodNum);
            }

            // 如果有结束时间，确保下次账单时间不超过结束时间
            if (endTime != null && endTime > 0) {
                Date endDate = new Date(endTime * 1000);
                if (nextBillCal.getTime().after(endDate)) {
                    return null;
                }
            }

            return nextBillCal.getTimeInMillis() / 1000;

        } catch (Exception e) {
            logger.error("计算下次账单时间失败，绑定ID: {}", binding.getLong("id"), e);
            return null;
        }
    }

    /**
     * 将 yyyyMMdd 格式的整数转换为 Date 对象
     */
    private Date parseYyyyMmddToDate(Long yyyyMmdd) {
        if (yyyyMmdd == null || yyyyMmdd <= 0) {
            return null;
        }

        try {
            String dateStr = String.valueOf(yyyyMmdd);
            if (dateStr.length() != 8) {
                logger.warn("日期格式错误，期望 yyyyMMdd 格式，实际值：{}", yyyyMmdd);
                return null;
            }

            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd");
            return sdf.parse(dateStr);
        } catch (Exception e) {
            logger.error("解析日期失败，yyyyMMdd：{}", yyyyMmdd, e);
            return null;
        }
    }

    /**
     * 计算不足月的金额比例
     * @param billPeriod 账期信息
     * @return 金额比例（0.0-1.0）
     */
    public BigDecimal calculateIncompleteMonthRatio(Map<String, Object> billPeriod) {
        Boolean isIncompleteMonth = (Boolean) billPeriod.get("isIncompleteMonth");
        String handlingType = (String) billPeriod.get("incompleteMonthHandling");

        if (isIncompleteMonth == null || !isIncompleteMonth) {
            return BigDecimal.ONE; // 完整月份，比例为1
        }

        if ("NO_CHARGE".equals(handlingType)) {
            return BigDecimal.ZERO; // 不收费，比例为0
        }

        if ("BY_MONTH".equals(handlingType)) {
            return BigDecimal.ONE; // 按月收费，比例为1
        }

        if ("BY_DAY".equals(handlingType) || handlingType == null) {
            // 按天收费，或者没有配置不足月处理方式时默认按天计算
            Date startDate = (Date) billPeriod.get("startDate");
            Date endDate = (Date) billPeriod.get("endDate");

            if (startDate == null || endDate == null) {
                return BigDecimal.ONE;
            }

            // 计算实际天数
            long actualDays = (endDate.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000) + 1;

            // 计算该月总天数
            Calendar cal = Calendar.getInstance();
            cal.setTime(startDate);
            int totalDays = cal.getActualMaximum(Calendar.DAY_OF_MONTH);

            BigDecimal ratio = new BigDecimal(actualDays).divide(new BigDecimal(totalDays), 4, RoundingMode.HALF_UP);

            if (handlingType == null) {
                logger.debug("绑定结束时间限制导致不足月，按天计算比例，实际天数：{}，总天数：{}，比例：{}", actualDays, totalDays, ratio);
            } else {
                logger.debug("按天收费计算，实际天数：{}，总天数：{}，比例：{}", actualDays, totalDays, ratio);
            }

            return ratio;
        }

        return BigDecimal.ONE; // 默认比例为1
    }
}
