package com.ehome.oc.controller.assets;

import com.ehome.common.core.controller.BaseController;
import com.ehome.oc.service.IHouseInfoService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 业主管理基础控制器
 * 提供通用的工具方法和业务逻辑
 * 
 * <AUTHOR>
 */
public abstract class OcOwnerBaseController extends BaseController {

    @Autowired
    protected IHouseInfoService houseInfoService;


    /**
     * 更新业主的房屋信息
     */
    protected void updateOwnerHouseInfo(String ownerId) {
        houseInfoService.updateOwnerHouseInfo(ownerId);
    }

    /**
     * 更新车位表中的业主信息
     */
    protected void updateParkingOwnerInfo(String parkingId) {
        try {
            // 获取车位绑定的业主数量
            int ownerCount = Db.queryInt(
                "SELECT COUNT(*) FROM eh_parking_owner_rel WHERE parking_id = ?",
                parkingId
            );

            String ownerName = "";
            Record owner = Db.findFirst(
                "SELECT GROUP_CONCAT(o.owner_name) as names FROM eh_parking_owner_rel r " +
                "LEFT JOIN eh_owner o ON r.owner_id = o.owner_id WHERE r.parking_id = ?",
                parkingId
            );
            if (owner != null && owner.getStr("names") != null) {
                ownerName = owner.getStr("names");
            }

            // 更新车位表中的业主信息
            Db.update(
                "UPDATE eh_parking_space SET owner_count = ?, owner_name = ? WHERE parking_id = ?",
                ownerCount, ownerName, parkingId
            );

            logger.info("更新车位业主信息成功，车位ID: {}, 业主数量: {}, 业主名称: {}", parkingId, ownerCount, ownerName);
        } catch (Exception e) {
            logger.error("更新车位业主信息失败", e);
        }
    }

    /**
     * 更新业主的车位信息
     */
    protected void updateOwnerParkingInfo(String ownerId) {
        try {
            // 获取业主绑定的车位数量
            int parkingCount = Db.queryInt(
                "SELECT COUNT(*) FROM eh_parking_owner_rel WHERE owner_id = ?",
                ownerId
            );

            // 获取业主绑定的车位号（使用GROUP_CONCAT拼接）
            String parkingNo = "";
            Record parking = Db.findFirst(
                "SELECT GROUP_CONCAT(p.parking_no ORDER BY r.create_time ASC SEPARATOR ',') as parking_nos " +
                "FROM eh_parking_owner_rel r " +
                "LEFT JOIN eh_parking_space p ON r.parking_id = p.parking_id " +
                "WHERE r.owner_id = ?",
                ownerId
            );
            if (parking != null && parking.getStr("parking_nos") != null) {
                parkingNo = parking.getStr("parking_nos");
            }

            // 更新业主表的车位数量和车位号
            Db.update(
                "UPDATE eh_owner SET parking_count = ?, parking_no = ? WHERE owner_id = ?",
                parkingCount, parkingNo, ownerId
            );

            logger.info("更新业主车位信息成功，业主ID: {}, 车位数量: {}, 车位号: {}", ownerId, parkingCount, parkingNo);
        } catch (Exception e) {
            logger.error("更新业主车位信息失败", e);
        }
    }

    /**
     * 更新业主的车辆信息
     */
    protected void updateOwnerVehicleInfo(String ownerId) {
        try {
            Record r = Db.findFirst(
                    "SELECT GROUP_CONCAT(v.plate_no) AS car_info, COUNT(*) AS car_count " +
                            "FROM eh_vehicle_owner_rel r LEFT JOIN eh_vehicle v ON r.vehicle_id = v.vehicle_id WHERE r.owner_id = ?",
                    ownerId
            );

            if (r != null) {
                String carInfo = r.getStr("car_info");
                int carCount = r.getInt("car_count");

                Db.update("UPDATE eh_owner SET car_count = ?, car_info = ? WHERE owner_id = ?", carCount, carInfo, ownerId);
                logger.info("更新业主车辆信息成功，业主ID: {}, 车辆数量: {}", ownerId, carCount);
            }
        } catch (Exception e) {
            logger.error("更新业主车辆信息失败", e);
        }
    }
}
