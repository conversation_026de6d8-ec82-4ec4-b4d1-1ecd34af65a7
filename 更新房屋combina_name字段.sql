-- 更新房屋表的combina_name字段
-- 格式：楼栋-单元-楼层

-- 方案1：基本更新（适用于所有字段都不为空的情况）
UPDATE eh_house_info 
SET combina_name = CONCAT(
    IFNULL(building_name, ''), 
    '-', 
    IFNULL(unit_name, ''), 
    '-', 
    IFNULL(floor, '')
)
WHERE building_name IS NOT NULL 
  AND unit_name IS NOT NULL 
  AND floor IS NOT NULL;

-- 方案2：处理空值的更新（推荐使用）
UPDATE eh_house_info 
SET combina_name = CASE 
    WHEN building_name IS NOT NULL AND unit_name IS NOT NULL AND floor IS NOT NULL THEN
        CONCAT(building_name, '-', unit_name, '-', floor)
    WHEN building_name IS NOT NULL AND unit_name IS NOT NULL THEN
        CONCAT(building_name, '-', unit_name)
    WHEN building_name IS NOT NULL THEN
        building_name
    ELSE
        ''
END;

-- 方案3：只更新空的combina_name字段
UPDATE eh_house_info 
SET combina_name = CONCAT(
    IFNULL(building_name, ''), 
    '-', 
    IFNULL(unit_name, ''), 
    '-', 
    IFNULL(floor, '')
)
WHERE (combina_name IS NULL OR combina_name = '')
  AND building_name IS NOT NULL;

-- 方案4：更安全的更新（先查看再更新）
-- 先查看需要更新的数据
SELECT 
    house_id,
    building_name,
    unit_name,
    floor,
    combina_name AS old_combina_name,
    CONCAT(
        IFNULL(building_name, ''), 
        '-', 
        IFNULL(unit_name, ''), 
        '-', 
        IFNULL(floor, '')
    ) AS new_combina_name
FROM eh_house_info 
WHERE building_name IS NOT NULL
LIMIT 10;

-- 然后执行更新
UPDATE eh_house_info 
SET combina_name = CONCAT(
    IFNULL(building_name, ''), 
    '-', 
    IFNULL(unit_name, ''), 
    '-', 
    IFNULL(floor, '')
),
update_time = NOW(),
update_by = 'system'
WHERE building_name IS NOT NULL;

-- 验证更新结果
SELECT 
    house_id,
    building_name,
    unit_name,
    floor,
    combina_name,
    update_time
FROM eh_house_info 
WHERE combina_name LIKE '%-%-%'
LIMIT 10;

-- 统计更新情况
SELECT 
    COUNT(*) as total_houses,
    COUNT(CASE WHEN combina_name IS NOT NULL AND combina_name != '' THEN 1 END) as updated_houses,
    COUNT(CASE WHEN combina_name IS NULL OR combina_name = '' THEN 1 END) as empty_combina_name
FROM eh_house_info;

-- 查看不同格式的combina_name分布
SELECT 
    CASE 
        WHEN combina_name LIKE '%-%-%' THEN '楼栋-单元-楼层'
        WHEN combina_name LIKE '%-%' THEN '楼栋-单元'
        WHEN combina_name != '' THEN '仅楼栋'
        ELSE '空值'
    END as format_type,
    COUNT(*) as count
FROM eh_house_info
GROUP BY format_type;
