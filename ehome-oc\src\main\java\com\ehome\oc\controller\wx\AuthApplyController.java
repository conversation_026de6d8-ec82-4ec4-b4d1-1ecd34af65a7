package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.oc.service.IHouseInfoService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 住户申请认证控制器
 */
@RestController
@RequestMapping("/api/wx/auth/apply")
public class AuthApplyController extends BaseWxController {

    @Autowired
    private IHouseInfoService  houseInfoService;

    /**
     * 获取小区列表
     */
    @GetMapping("/communities")
    public AjaxResult getCommunities() {
        try {
            List<Record> communities = Db.find(
                "SELECT oc_id as value, oc_name as text FROM eh_community WHERE oc_state = 0 ORDER BY create_time"
            );
            
            List<Map<String, Object>> result = new ArrayList<>();
            for (Record community : communities) {
                Map<String, Object> item = new HashMap<>();
                item.put("value", community.getStr("value"));
                item.put("text", community.getStr("text"));
                result.add(item);
            }
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取小区列表失败", e);
            return AjaxResult.error("获取小区列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取楼栋列表
     */
    @GetMapping("/buildings/{communityId}")
    public AjaxResult getBuildings(@PathVariable String communityId) {
        try {
            if (StringUtils.isEmpty(communityId)) {
                return AjaxResult.error("小区ID不能为空");
            }
            
            List<Record> buildings = Db.find(
                "SELECT building_id as value, name as text FROM eh_building WHERE community_id = ? ORDER BY order_index ASC",
                communityId
            );
            
            List<Map<String, Object>> result = new ArrayList<>();
            for (Record building : buildings) {
                Map<String, Object> item = new HashMap<>();
                item.put("value", building.getStr("value"));
                item.put("text", building.getStr("text"));
                result.add(item);
            }
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取楼栋列表失败", e);
            return AjaxResult.error("获取楼栋列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取房屋列表
     */
    @GetMapping("/houses/{buildingId}")
    public AjaxResult getHouses(@PathVariable String buildingId) {
        try {
            if (StringUtils.isEmpty(buildingId)) {
                return AjaxResult.error("楼栋ID不能为空");
            }
            
            List<Record> houses = Db.find(
                "SELECT house_id as value, CONCAT(IFNULL(floor, ''), IFNULL(room, ''), ' (', IFNULL(use_area, 0), '㎡)') as text " +
                "FROM eh_house_info WHERE building_id = ? ORDER BY floor, room",
                buildingId
            );
            
            List<Map<String, Object>> result = new ArrayList<>();
            for (Record house : houses) {
                Map<String, Object> item = new HashMap<>();
                item.put("value", house.getStr("value"));
                item.put("text", house.getStr("text"));
                result.add(item);
            }
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取房屋列表失败", e);
            return AjaxResult.error("获取房屋列表失败：" + e.getMessage());
        }
    }

    /**
     * 提交申请认证
     */
    @Log(title = "提交申请认证", businessType = BusinessType.INSERT)
    @PostMapping("/submit")
    public AjaxResult submitApply(@RequestBody JSONObject params) {
        try {
            logger.info("提交申请认证参数: {}", params.toJSONString());
            // 获取参数
            String communityId = params.getString("communityId");
            String houseId = params.getString("houseId");
            String ownerName = params.getString("ownerName");
            String idCard = params.getString("idCard");
            String mobile = params.getString("mobile");
            Integer relType = params.getInteger("relType");
            String gender = params.getString("gender");
            String isLive = params.getString("isLive");
            String moveDate = params.getString("moveDate");
            String remark = params.getString("remark");
            String attachmentFileId = params.getString("attachmentFileId");
            
            // 参数验证
            if (StringUtils.isEmpty(communityId)) {
                return AjaxResult.error("请选择小区");
            }
            if (StringUtils.isEmpty(houseId)) {
                return AjaxResult.error("请选择房屋");
            }
            if (StringUtils.isEmpty(ownerName)) {
                return AjaxResult.error("请输入姓名");
            }
            if (StringUtils.isEmpty(idCard)) {
                return AjaxResult.error("请输入证件号码");
            }
            if (StringUtils.isEmpty(mobile)) {
                return AjaxResult.error("请输入手机号");
            }
            if (relType == null) {
                return AjaxResult.error("请选择认证身份");
            }
            if (StringUtils.isEmpty(attachmentFileId)) {
                return AjaxResult.error("请上传证件照片");
            }

            // 验证手机号格式
            if (!mobile.matches("^1[3-9]\\d{9}$")) {
                return AjaxResult.error("请输入正确的手机号");
            }

            String currentUserId = "";
            if(getCurrentUser()!=null){
                currentUserId =  String.valueOf(getCurrentUser().getUserId());
            }


            // 检查是否已存在相同的申请
            Record existingRel = Db.findFirst(
                "SELECT t1.* FROM eh_house_owner_rel t1,eh_owner t2 WHERE t1.owner_id = t2.owner_id and t1.house_id = ? AND t2.mobile = ?",
                houseId, mobile
            );

            if (existingRel != null) {
                String statusText = getCheckStatusText(existingRel.getInt("check_status"));
                return AjaxResult.error("您已申请过该房屋的认证，当前状态：" + statusText);
            }

            // 查找或创建业主记录
            String ownerId = findOrCreateOwner(currentUserId, ownerName, mobile, idCard, communityId, gender, isLive, moveDate, remark);
            if(ownerId==null){
                logger.error("创建或查找业主记录失败:{}",params.toJSONString());
                return AjaxResult.error("请稍后再试试");
            }

            // 创建房屋绑定申请记录
            Record rel = new Record();
            rel.set("rel_id", Seq.getId());
            rel.set("house_id", houseId);
            rel.set("owner_id", ownerId);
            rel.set("community_id", communityId);
            rel.set("rel_type", relType);
            rel.set("is_default", 0);
            rel.set("check_status", 0); // 待审核
            rel.set("apply_flag", 1); // 需要走审核流程
            rel.set("remark", "小程序申请认证");
            rel.set("file_id", attachmentFileId); // 保存附件文件ID
            rel.set("create_time", DateUtils.getTime());
            rel.set("create_by", currentUserId);
            
            boolean success = Db.save("eh_house_owner_rel", "rel_id", rel);
            
            if (success) {
                houseInfoService.updateHouseOwnerInfo(houseId);
                houseInfoService.updateOwnerHouseInfo(ownerId);

                return AjaxResult.success("申请提交成功，请等待审核");
            } else {
                return AjaxResult.error("申请提交失败");
            }
            
        } catch (Exception e) {
            logger.error("提交申请认证失败", e);
            return AjaxResult.error("申请提交失败：" + e.getMessage());
        }
    }
    
    /**
     * 查找或创建业主记录
     */
    private String findOrCreateOwner(String userId, String ownerName, String mobile, String idCard, String communityId,
                                   String gender, String isLive, String moveDate, String remark) {
        // 先查找是否已存在业主记录
        Record owner = Db.findFirst("SELECT * FROM eh_owner WHERE mobile = ?", mobile);

        if (owner != null) {
            // 更新业主信息 一般是存在业主表 但是这个人没有关联房屋
            StringBuilder sql = new StringBuilder("UPDATE eh_owner SET owner_name = ?, id_card = ?, community_id = ?, update_time = ?");
            List<Object> params = new ArrayList<>();
            params.add(ownerName);
            params.add(idCard);
            params.add(communityId);
            params.add(DateUtils.getTime());

            // 添加新字段到更新语句
            if (StringUtils.isNotEmpty(gender)) {
                sql.append(", gender = ?");
                params.add(gender);
            }
            if (StringUtils.isNotEmpty(isLive)) {
                sql.append(", is_live = ?");
                params.add(Integer.parseInt(isLive));
            }
            if (StringUtils.isNotEmpty(moveDate)) {
                sql.append(", move_date = ?");
                params.add(moveDate);
            }
            if (StringUtils.isNotEmpty(remark)) {
                sql.append(", remark = ?");
                params.add(remark);
            }

            sql.append(" WHERE mobile = ?");
            params.add(mobile);

            Db.update(sql.toString(), params.toArray());
            return owner.getStr("owner_id");
        } else {
            // 创建新的业主记录
            Record newOwner = new Record();
            newOwner.set("owner_name", ownerName);
            newOwner.set("mobile", mobile);
            newOwner.set("id_card", idCard);
            newOwner.set("community_id", communityId);
            newOwner.set("house_count", 0);
            newOwner.set("create_time", DateUtils.getTime());
            newOwner.set("creator", userId);

            // 设置新字段
            if (StringUtils.isNotEmpty(gender)) {
                newOwner.set("gender", gender);
            }
            if (StringUtils.isNotEmpty(isLive)) {
                newOwner.set("is_live", Integer.parseInt(isLive));
            }
            if (StringUtils.isNotEmpty(moveDate)) {
                newOwner.set("move_date", moveDate);
            }
            if (StringUtils.isNotEmpty(remark)) {
                newOwner.set("remark", remark);
            }

            Db.save("eh_owner", "owner_id", newOwner);

            String ownerId = newOwner.getStr("owner_id");
            return ownerId;
        }
    }
    
    /**
     * 获取审核状态文本
     */
    private String getCheckStatusText(Integer checkStatus) {
        if (checkStatus == null) return "未知";
        switch (checkStatus) {
            case 0: return "待审核";
            case 1: return "已审核";
            case 2: return "审核不通过";
            default: return "未知";
        }
    }
}
