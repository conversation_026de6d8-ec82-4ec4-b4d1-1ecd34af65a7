# 开发环境配置
server:
  port: 8066
  tomcat:
    basedir: D:/ehome

spring:
  datasource:
    druid:
      master:
        # 开发环境数据库配置
        url: ***********************************************************************************************************************************************************
        username: smarthome_prod
        password: a4aSwZHaYpYGJTJw

  # 开发环境特定配置
  devtools:
    restart:
      enabled: true
  thymeleaf:
    cache: false

# 开发环境特定配置
ruoyi:
  profile: D:/ehome/uploadPath
  demoEnabled: true

logging:
  file:
    path: D:/ehome/logs
  level:
    com.ehome: debug
    org.springframework: warn
    # 统一SQL日志配置
    sql-log: debug
    com.ehome.**.mapper: debug
    org.apache.ibatis: debug
    druid.sql.Statement: debug
    druid.sql.DataSource: debug
    com.jfinal.plugin.activerecord: debug
    root: info

# 功能开关
swagger:
  enabled: false

# Token配置 - 永不过期机制
token:
  expire-time: 2592000000   # 30天，极长有效期确保永不过期
  refresh-threshold: 604800000 # 7天，提前刷新确保连续性
  auto-refresh-enabled: true
  secret: ehomeDevSecretKey2025!@#
  force-logout-enabled: false           # 强制下线功能开关，默认关闭
  force-logout-date: "@build.date@"     # 强制下线日期，格式：yyyyMMdd，Maven打包时自动替换为打包日期

# 微信小程序配置
wechat:
  appid: wxf279bf8df3d4d470
  secret: a62d650089f3c5ed044c418cac11ab30

# 阿里云OSS配置 - 开发环境
aliyun:
  oss:
    # 开发环境路径前缀
    path-prefix: uploads/dev