package com.ehome.oc.controller.assets;

import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 车牌识别记录管理控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/oc/plateRecognitionLog")
public class PlateRecognitionLogController extends BaseController {

    private static final String PREFIX = "oc/plateRecognitionLog";

    /**
     * 车牌识别记录管理页面
     */
    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list";
    }

    /**
     * 查询车牌识别记录列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        com.alibaba.fastjson.JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "SELECT log_id, community_id, user_name, user_phone, file_id, plate_number, " +
            "recognition_status, confidence, plate_color, owner_found, owner_id, owner_name, " +
            "owner_phone, image_size, recognition_time, error_message, create_time, ip_address",
            sql.toFullSql()
        );
        
        return getDataTable(paginate);
    }

    /**
     * 构建查询条件
     */
    private EasySQL buildListQuery(com.alibaba.fastjson.JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("FROM eh_plate_recognition_log WHERE 1=1");
        
        // 社区过滤
        sql.append(getSysUser().getCommunityId(), "AND community_id = ?");
        
        // 车牌号搜索
        sql.appendLike(params.getString("plateNumber"), "AND plate_number LIKE ?");
        
        // 用户姓名搜索
        sql.appendLike(params.getString("userName"), "AND user_name LIKE ?");
        
        // 用户手机号搜索
        sql.appendLike(params.getString("userPhone"), "AND user_phone LIKE ?");
        
        // 识别状态过滤
        if (StringUtils.isNotEmpty(params.getString("recognitionStatus"))) {
            sql.append(params.getString("recognitionStatus"), "AND recognition_status = ?");
        }
        
        // 是否找到车主过滤
        if (StringUtils.isNotEmpty(params.getString("ownerFound"))) {
            sql.append(params.getString("ownerFound"), "AND owner_found = ?");
        }
        
        // 时间范围过滤
        if (StringUtils.isNotEmpty(params.getString("startTime"))) {
            sql.append(params.getString("startTime"), "AND create_time >= ?");
        }
        if (StringUtils.isNotEmpty(params.getString("endTime"))) {
            sql.append(params.getString("endTime"), "AND create_time <= ?");
        }
        
        // 排序
        sql.append("ORDER BY create_time DESC");
        
        return sql;
    }

    /**
     * 查看记录详情
     */
    @GetMapping("/detail/{logId}")
    public String detail(@PathVariable("logId") String logId, ModelMap mmap) {
        Record log = Db.findFirst("SELECT * FROM eh_plate_recognition_log WHERE log_id = ?", logId);
        if (log != null) {
            mmap.put("log", log.toMap());
        }
        return PREFIX + "/detail";
    }

    /**
     * 获取记录详情数据
     */
    @PostMapping("/getDetail")
    @ResponseBody
    public AjaxResult getDetail() {
        com.alibaba.fastjson.JSONObject params = getParams();
        String logId = params.getString("logId");
        
        if (StringUtils.isEmpty(logId)) {
            return AjaxResult.error("记录ID不能为空");
        }
        
        Record log = Db.findFirst("SELECT * FROM eh_plate_recognition_log WHERE log_id = ?", logId);
        if (log == null) {
            return AjaxResult.error("记录不存在");
        }
        
        return AjaxResult.success(log.toMap());
    }

    /**
     * 删除记录
     */
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return AjaxResult.error("参数不能为空");
        }
        
        try {
            String[] idArr = ids.split(",");
            for (String id : idArr) {
                Db.deleteById("eh_plate_recognition_log", "log_id", id);
            }
            return AjaxResult.success("删除成功");
        } catch (Exception e) {
            logger.error("删除车牌识别记录失败", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取统计数据
     */
    @PostMapping("/statistics")
    @ResponseBody
    public AjaxResult statistics() {
        try {
            String communityId = getSysUser().getCommunityId();
            
            // 今日统计
            List<Record> todayStats = Db.find(
                "SELECT " +
                "COUNT(*) as total_count, " +
                "SUM(CASE WHEN recognition_status = 1 THEN 1 ELSE 0 END) as success_count, " +
                "SUM(CASE WHEN owner_found = 1 THEN 1 ELSE 0 END) as owner_found_count, " +
                "AVG(CASE WHEN recognition_time > 0 THEN recognition_time ELSE NULL END) as avg_time " +
                "FROM eh_plate_recognition_log " +
                "WHERE community_id = ? AND DATE(create_time) = CURDATE()",
                communityId
            );
            
            // 本月统计
            List<Record> monthStats = Db.find(
                "SELECT " +
                "COUNT(*) as total_count, " +
                "SUM(CASE WHEN recognition_status = 1 THEN 1 ELSE 0 END) as success_count, " +
                "SUM(CASE WHEN owner_found = 1 THEN 1 ELSE 0 END) as owner_found_count " +
                "FROM eh_plate_recognition_log " +
                "WHERE community_id = ? AND YEAR(create_time) = YEAR(NOW()) AND MONTH(create_time) = MONTH(NOW())",
                communityId
            );
            
            // 最近7天每日统计
            List<Record> weeklyStats = Db.find(
                "SELECT DATE(create_time) as date, COUNT(*) as count " +
                "FROM eh_plate_recognition_log " +
                "WHERE community_id = ? AND create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) " +
                "GROUP BY DATE(create_time) " +
                "ORDER BY date DESC",
                communityId
            );
            
            com.alibaba.fastjson.JSONObject result = new com.alibaba.fastjson.JSONObject();
            result.put("today", todayStats.isEmpty() ? new com.alibaba.fastjson.JSONObject() : todayStats.get(0).toMap());
            result.put("month", monthStats.isEmpty() ? new com.alibaba.fastjson.JSONObject() : monthStats.get(0).toMap());
            result.put("weekly", recordToMap(weeklyStats));
            
            return AjaxResult.success(result);
            
        } catch (Exception e) {
            logger.error("获取统计数据失败", e);
            return AjaxResult.error("获取统计数据失败：" + e.getMessage());
        }
    }
}
