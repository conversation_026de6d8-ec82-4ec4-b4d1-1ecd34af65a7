<!-- 全局水印 -->
<global-watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></global-watermark>

<view class="container {{commentEnabled ? 'with-comment' : 'without-comment'}}">
  <!-- 加载中 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="weui-loadmore">
      <view class="weui-loading"></view>
      <view class="weui-loadmore__tips">加载中...</view>
    </view>
  </view>

  <!-- 公告详情 -->
  <view class="article-container" wx:elif="{{detail}}">
    <!-- 文章头部 -->
    <view class="article-header">
      <view class="article-title">{{detail.title}}</view>
      <view class="article-meta">
        <view class="author-info">
          <text class="source-tag source-{{detail.publisherType || 'property'}}">{{detail.publisherType === 'committee' ? '业委会' : '物业'}}</text>
          <text class="author-name">{{detail.signature || detail.authorName}}</text>
          <text class="publish-time">{{detail.date || detail.createTime}}</text>
        </view>
      </view>
    </view>

    <!-- 文章内容 -->
    <view class="article-content">
      <rich-text nodes="{{detail.content}}"></rich-text>
    </view>

    <!-- 附件列表 -->
    <view class="attachments" wx:if="{{detail.attachments && detail.attachments.length > 0}}">
      <view class="attach-title">
        <van-icon name="paperclip" size="32rpx" color="#666" />
        <text>附件 ({{detail.attachments.length}})</text>
      </view>
      <view class="attach-list">
        <view wx:for="{{detail.attachments}}"
              wx:key="id"
              class="attach-item"
              bindtap="viewAttachment"
              data-id="{{item.id}}"
              data-url="{{item.url}}"
              data-name="{{item.name}}"
              data-type="{{item.type}}"
              data-size="{{item.size}}">
          <text class="attach-icon">{{item.icon}}</text>
          <view class="attach-info">
            <text class="attach-name">{{item.name}}</text>
            <text class="attach-size">{{item.sizeText}}</text>
          </view>
          <text class="attach-arrow">→</text>
        </view>
      </view>
    </view>



    <!-- 评论区域 -->
    <view class="comments-section" id="comments-section" wx:if="{{commentEnabled}}">
      <view class="comments-header">
        <text class="comments-title">留言 {{commentCount || 0}}</text>
      </view>

      <!-- 简化的评论输入框 -->
      <view class="comment-input-simple" wx:if="{{!showFullInput}}" bindtap="showCommentInput">
        <view class="input-simple-content">
          <text class="input-placeholder">写留言</text>
          <view class="input-simple-icons">
            <van-icon name="smile-o" size="32rpx" color="#999" />
            <van-icon name="photograph" size="32rpx" color="#999" />
          </view>
        </view>
      </view>



      <!-- 评论列表 -->
      <view class="comments-list" wx:if="{{comments && comments.length > 0}}">
        <view wx:for="{{comments}}" wx:key="id" class="comment-item">
          <!-- 评论主体 -->
          <view class="comment-main">
            <view class="comment-avatar">
              <image class="avatar-img" src="{{item.avatar || '/static/images/default-avatar.png'}}" mode="aspectFill" />
            </view>
            <view class="comment-body">
              <view class="comment-user-info">
                <text class="comment-user">{{item.userName}}</text>
                <text class="pending-tag" wx:if="{{item.isPending}}">待审核</text>
                <text class="comment-time">{{item.createTime}}</text>
                <view class="comment-actions">
                  <view class="action-item" bindtap="showReplyInput" data-id="{{item.id}}" data-user="{{item.userName}}">
                    <text class="action-text">回复</text>
                  </view>
                  <view class="action-item" bindtap="likeComment" data-id="{{item.id}}">
                    <van-icon name="good-job-o" size="40rpx" color="{{item.isLiked ? '#ff4757' : '#999'}}" />
                    <text class="action-text" wx:if="{{item.likeCount > 0}}">{{item.likeCount}}</text>
                  </view>
                </view>
              </view>
              <view class="comment-content">{{item.content}}</view>
            </view>
          </view>

          <!-- 回复列表 -->
          <view class="replies-list" wx:if="{{item.replies && item.replies.length > 0}}">
            <view wx:for="{{item.replies}}" wx:for-item="reply" wx:key="id" class="reply-item">
              <view class="reply-main">
                <view class="reply-avatar">
                  <image class="avatar-img" src="{{reply.avatar || '/static/images/default-avatar.png'}}" mode="aspectFill" />
                </view>
                <view class="reply-body">
                  <view class="reply-user-info">
                    <text class="reply-user">{{reply.userName}}</text>
                    <text wx:if="{{reply.replyToUser}}" class="reply-to">回复@{{reply.replyToUser}}</text>
                    <text class="pending-tag" wx:if="{{reply.isPending}}">待审核</text>
                    <text class="reply-time">{{reply.createTime}}</text>
                    <view class="reply-actions">
                      <view class="action-item" bindtap="replyToReply" data-comment-id="{{item.id}}" data-reply-id="{{reply.id}}" data-user="{{reply.userName}}">
                        <text class="action-text">回复</text>
                      </view>
                      <view class="action-item" bindtap="likeReply" data-id="{{reply.id}}">
                        <van-icon name="good-job-o" size="40rpx" color="{{reply.isLiked ? '#ff4757' : '#999'}}" />
                        <text class="action-text" wx:if="{{reply.likeCount > 0}}">{{reply.likeCount}}</text>
                      </view>
                    </view>
                  </view>
                  <view class="reply-content">{{reply.content}}</view>
                </view>
              </view>
            </view>

            <!-- 查看更多回复 -->
            <view wx:if="{{item.hasMoreReplies}}" class="more-replies"
                  bindtap="loadMoreReplies" data-id="{{item.id}}">
              <text>查看更多回复 ({{item.replyCount - item.replies.length}}条)</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多评论 -->
      <view class="load-more-comments" wx:if="{{comments && comments.length > 0}}">
        <view wx:if="{{commentLoading}}" class="loading-more">
          <view class="weui-loadmore">
            <view class="weui-loading"></view>
            <view class="weui-loadmore__tips">加载中...</view>
          </view>
        </view>
        <view wx:elif="{{commentHasMore}}" class="load-more-btn" bindtap="loadMoreComments">
          <text>加载更多评论</text>
        </view>
        <view wx:else class="no-more-comments">
          <text>没有更多评论了</text>
        </view>
      </view>

      <!-- 空评论状态 -->
      <view class="empty-comments" wx:else>
        <text>暂无留言</text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <image class="empty-image" src="/static/images/empty.png" mode="aspectFit" />
    <text class="empty-text">公告不存在或已删除</text>
  </view>

  <!-- 固定底部互动区域 -->
  <view class="fixed-interaction" wx:if="{{detail}}">
    <!-- 发布人信息 -->
    <view class="publisher-info">
      <van-icon name="manager-o" size="40rpx" color="#666" />
      <text class="publisher-name">{{detail.signature || '管理员'}}</text>
    </view>

    <!-- 操作按钮 -->
    <view class="interaction-buttons">
      <view class="interaction-item" bindtap="likeArticle">
        <van-icon name="good-job-o" size="48rpx" color="{{articleLiked ? '#ff4757' : '#666'}}" />
        <text class="interaction-label" wx:if="{{!articleLikeCount || articleLikeCount == 0}}">点赞</text>
        <text class="interaction-count" wx:else>{{articleLikeCount}}</text>
      </view>
      <button class="interaction-item share-button" open-type="share">
        <van-icon name="share-o" size="48rpx" color="#666" />
        <text class="interaction-label" wx:if="{{!articleShareCount || articleShareCount == 0}}">转发</text>
        <text class="interaction-count" wx:else>{{articleShareCount}}</text>
      </button>
      <view class="interaction-item" bindtap="scrollToComments" wx:if="{{commentEnabled}}">
        <van-icon name="smile-comment-o" size="48rpx" color="#666" />
        <text class="interaction-label" wx:if="{{!commentCount || commentCount == 0}}">留言</text>
        <text class="interaction-count" wx:else>{{commentCount}}</text>
      </view>
      <view class="interaction-item" bindtap="{{showReadUsers && readUsersCount > 0 ? 'showReadUsersModal' : ''}}">
        <van-icon name="eye-o" size="48rpx" color="#666" />
        <text class="interaction-label" wx:if="{{showReadUsers && readUsersCount > 0}}">已读</text>
        <text class="interaction-label" wx:elif="{{!detail.readCount || detail.readCount == 0}}">阅读</text>
        <text class="interaction-count" wx:else>{{detail.readCount}}</text>
      </view>
    </view>
  </view>

  <!-- 悬浮评论输入框 -->
  <view class="floating-input-mask" wx:if="{{showFullInput && commentEnabled}}" bindtap="hideCommentInput">
    <view class="floating-input-container" catchtap="preventClose">
      <view class="floating-input-header" wx:if="{{replyToUser}}">
        <text>{{replyPlaceholder || '回复评论:'}}</text>
        <text class="cancel-reply" bindtap="cancelReply">取消</text>
      </view>
      <view class="floating-input-wrapper">
        <textarea
          class="floating-comment-input"
          placeholder="{{replyToUser ? '写下你的回复...' : '写留言'}}"
          value="{{commentText}}"
          bindinput="onCommentInput"
          maxlength="500"
          auto-height
          focus="{{shouldFocus}}"
        />
        <view class="floating-input-actions">
          <view class="floating-input-tools">
            <van-icon name="smile-o" size="48rpx" color="{{showEmojiPanel ? '#576b95' : '#999'}}" bindtap="toggleEmojiPanel" />
            <van-icon name="photograph" size="48rpx" color="#999" />
          </view>
          <button class="floating-submit-btn"
                  bindtap="submitComment"
                  disabled="{{commentText.length === 0 || submitting}}"
                  type="primary">
            {{submitting ? '发送中...' : '发送'}}
          </button>
        </view>

        <!-- 表情选择面板 -->
        <view class="emoji-panel" wx:if="{{showEmojiPanel}}">
          <view class="emoji-grid">
            <view wx:for="{{emojiList}}" wx:key="index"
                  class="emoji-item"
                  bindtap="selectEmoji"
                  data-emoji="{{item}}">
              {{item}}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 已读用户底部弹出层 -->
  <van-popup show="{{showReadUsersModal}}" position="bottom" round bind:close="hideReadUsersModal">
    <view class="read-users-popup">
      <view class="popup-header">
        <text class="popup-title">已读房屋 ({{readUsersCount}})</text>
        <van-icon name="cross" size="40rpx" color="#999" bindtap="hideReadUsersModal" />
      </view>
      <view class="popup-content">
        <view wx:for="{{displayReadUsersList}}" wx:key="index" class="read-user-item">
          <view class="read-user-house">{{item.houseName}}</view>
          <view class="read-user-time">{{item.readTimeFormatted}}</view>
        </view>
        <!-- 查看全部按钮 -->
        <view class="view-all-btn" wx:if="{{hasMoreReadUsers}}" bindtap="viewAllReadUsers">
          <text class="view-all-text">查看全部 ({{readUsersCount}})</text>
          <van-icon name="arrow" size="24rpx" color="#576b95" />
        </view>
      </view>
    </view>
  </van-popup>
</view>