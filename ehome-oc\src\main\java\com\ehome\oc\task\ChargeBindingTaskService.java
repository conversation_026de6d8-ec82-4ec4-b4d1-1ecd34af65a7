package com.ehome.oc.task;

import com.ehome.common.utils.LoggerUtils;
import com.ehome.oc.service.charge.ChargeBindingService;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 收费绑定定时任务服务
 */
@Service
public class ChargeBindingTaskService {

    private static final String LOGGER_NAME = "charge-binding-task";
    private final Logger logger = LoggerUtils.getLog(LOGGER_NAME);

    @Autowired
    private ChargeBindingService chargeBindingService;

    /**
     * 刷新所有收费绑定的下次账单日期（定时任务入口）
     */
    public void refreshNextBillTime() {
        logger.info("定时任务：开始刷新收费绑定的下次账单日期");
        chargeBindingService.refreshAllNextBillTime();
        logger.info("定时任务：刷新收费绑定的下次账单日期完成");
    }

    /**
     * 计算下次账单时间（兼容现有调用）
     */
    public Long calculateNextBillTimeWithStandard(Record binding) {
        return chargeBindingService.calculateNextBillTime(binding);
    }

}
