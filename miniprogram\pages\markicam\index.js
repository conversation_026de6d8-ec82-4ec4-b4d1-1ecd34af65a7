const app = getApp()

Page({
  data: {
    momentsList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    // 顶部筛选
    filters: {
      moment_type: '', // 1照片 2视频
      team_id: '',
      start_time: '',
      end_time: ''
    },
    // 筛选选项
    typeOptions: [
      { text: '全部类型', value: '', icon: '' },
      { text: '照片', value: '1', icon: '' },
      { text: '视频', value: '2', icon: '' }
    ],
    timeRangeLabel: '时间'
  },

  onShow() {
    // 设置tabBar（使用公共工具函数）
    const { initTabBar } = require('../../utils/pageUtils.js')
    initTabBar('1') // markicam页面为业主页面
  },


  async onLoad() {
    console.log('[Markicam] 页面加载开始')
    this.initializePage()
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  onReachBottom() {
    this.loadMoreData()
  },

  // 初始化页面
  async initializePage() {
    // 筛选项已在data中初始化，直接加载数据
    await this.loadMomentsList(true)
  },

  // 刷新数据
  async refreshData() {
    this.setData({
      page: 1,
      hasMore: true,
      momentsList: []
    })

    await this.loadMomentsList(true)
    wx.stopPullDownRefresh()
  },

  // 加载更多数据
  async loadMoreData() {
    if (!this.data.hasMore || this.data.loading) return

    this.setData({ page: this.data.page + 1 })
    await this.loadMomentsList(false)
  },

  // 获取动态列表
  async loadMomentsList(isRefresh = false) {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      if (isRefresh) {
        wx.showLoading({ title: '加载中...' })
      }

      const params = {
        pageNum: this.data.page,
        pageSize: this.data.pageSize
      }
      const f = this.data.filters || {}
      if (f.moment_type !== '' && f.moment_type != null && f.moment_type !== undefined) {
        params.moment_type = parseInt(f.moment_type)
      }
      if (f.team_id !== '' && f.team_id != null) params.team_id = f.team_id
      if (f.start_time) params.start_time = f.start_time
      if (f.end_time) params.end_time = f.end_time

      const res = await app.request({
        url: '/api/wx/markicam/moments',
        method: 'GET',
        data: params
      })

      if (res.code === 0) {
        const newList = this.processMomentsList(res.data.rows || [])
        const hasMore = newList.length === this.data.pageSize

        this.setData({
          momentsList: isRefresh ? newList : [...this.data.momentsList, ...newList],
          hasMore
        })
      } else {
        throw new Error(res.msg || '获取动态列表失败')
      }
    } catch (error) {
      console.error('[Markicam] 获取动态列表失败:', error)
      wx.showToast({
        title: '获取数据失败',
        icon: 'none',
        duration: 2000
      })

      // 如果是首次加载失败，显示空状态
      if (isRefresh) {
        this.setData({ momentsList: [] })
      }
    } finally {
      this.setData({ loading: false })
      if (isRefresh) {
        wx.hideLoading()
      }
    }
  },

  // 处理动态列表数据
  processMomentsList(list) {
    return list.map(item => ({
      ...item,
      // 格式化时间显示
      timeDisplay: this.formatTime(item.post_time_str),
      // 处理头像，如果没有则使用默认头像
      avatar: item.avatar_url || '/images/default-avatar.png',
      // 处理昵称
      displayName: item.nickname || '匿名用户',
      // 判断媒体类型
      isVideo: item.moment_type === 2,
      isImage: item.moment_type === 1,
      // 解析内容为键值对
      parsedContent: this.parseContent(item.content)
    }))
  },

  // 解析内容为键值对数组 [{label, value}]
  parseContent(contentStr) {
    if (!contentStr) return null
    try {
      const data = typeof contentStr === 'string' ? JSON.parse(contentStr) : contentStr
      if (Array.isArray(data)) {
        return data.map(entry => {
          if (Array.isArray(entry) && entry.length >= 2) {
            return { label: String(entry[0] || ''), value: String(entry[1] || '') }
          }
          if (entry && typeof entry === 'object') {
            const k = Object.keys(entry)[0]
            return k ? { label: String(k), value: String(entry[k] ?? '') } : null
          }
          return null
        }).filter(Boolean)
      }
      return null
    } catch (e) {
      // 非JSON或解析失败，回退为纯文本
      return null
    }
  },

  // iOS兼容的时间解析
  parseDateSafe(input) {
    if (!input) return null
    if (input instanceof Date) return isNaN(input.getTime()) ? null : input
    if (typeof input === 'number') {
      const ms = input < 1e12 ? input * 1000 : input
      const d = new Date(ms)
      return isNaN(d.getTime()) ? null : d
    }
    const s = String(input).trim()
    if (/^\d{4}-\d{2}-\d{2} /.test(s) && s.indexOf('T') === -1) {
      const noMs = s.split('.')[0]
      const isoLike = noMs.replace(' ', 'T')
      const d = new Date(isoLike)
      if (!isNaN(d.getTime())) return d
    }
    if (/^\d{4}[-\/]\d{2}[-\/]\d{2}$/.test(s)) {
      const d = new Date(s.replace(/-/g, '/'))
      return isNaN(d.getTime()) ? null : d
    }
    const trySlash = new Date(s.replace(/-/g, '/'))
    if (!isNaN(trySlash.getTime())) return trySlash
    const direct = new Date(s)
    return isNaN(direct.getTime()) ? null : direct
  },

  // 格式化时间显示
  formatTime(timeStr) {
    if (!timeStr) return ''

    const now = new Date()
    const postTime = this.parseDateSafe(timeStr)
    if (!postTime) {
      const s = String(timeStr).replace('T', ' ')
      return s.length >= 16 ? s.substring(5, 16) : s
    }
    const diff = now - postTime

    if (diff < 60000) return '刚刚'
    if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'
    if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'

    const y = postTime.getFullYear()
    const m = String(postTime.getMonth() + 1).padStart(2, '0')
    const d = String(postTime.getDate()).padStart(2, '0')
    const hh = String(postTime.getHours()).padStart(2, '0')
    const mm = String(postTime.getMinutes()).padStart(2, '0')
    return (now.getFullYear() === y) ? `${m}-${d} ${hh}:${mm}` : `${y}-${m}-${d} ${hh}:${mm}`
  },

  // 时间筛选：打开
  onTimeItemOpen() {
    // 下拉展开时刷新显示标签
    this.updateTimeLabel()
  },

  // 快速选择
  onQuickRange(e) {
    const range = e.currentTarget.dataset.range
    const { start, end, label } = this.calcQuickRange(range)
    this.setData({
      'filters.start_time': start,
      'filters.end_time': end,
      timeRangeLabel: label,
      showDatePicker: range === 'custom'
    })
    if (range !== 'custom') {
      this.resetAndReload()
    }
  },

  // 自定义日期选择（示例为单日）
  onDatePick(e) {
    this.setData({ customDate: e.detail })
  },
  onDateConfirm() {
    const d = new Date(this.data.customDate)
    const day = this.formatDate(d)
    this.setData({
      'filters.start_time': day + ' 00:00:00',
      'filters.end_time': day + ' 23:59:59',
      timeRangeLabel: day,
      showDatePicker: false
    })
    this.resetAndReload()
  },
  onDateCancel() {
    this.setData({ showDatePicker: false })
  },

  resetAndReload() {
    this.setData({ page: 1, hasMore: true, momentsList: [] })
    this.loadMomentsList(true)
  },

  // 计算快捷时间范围
  calcQuickRange(range) {
    const now = new Date()
    const startOfDay = (d) => new Date(d.getFullYear(), d.getMonth(), d.getDate())
    const endOfDay = (d) => new Date(d.getFullYear(), d.getMonth(), d.getDate(), 23, 59, 59)

    let s, e, label
    if (range === 'today') {
      s = startOfDay(now); e = endOfDay(now); label = '今日'
    } else if (range === 'yesterday') {
      const y = new Date(now.getTime() - 86400000)
      s = startOfDay(y); e = endOfDay(y); label = '昨日'
    } else if (range === 'thisMonth') {
      s = new Date(now.getFullYear(), now.getMonth(), 1)
      e = endOfDay(new Date(now.getFullYear(), now.getMonth() + 1, 0))
      label = '本月'
    } else if (range === 'lastMonth') {
      s = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      e = endOfDay(new Date(now.getFullYear(), now.getMonth(), 0))
      label = '上月'
    } else if (range === 'last7') {
      const y = new Date(now.getTime() - 6 * 86400000)
      s = startOfDay(y); e = endOfDay(now); label = '近7天'
    } else { // custom
      s = ''; e = ''; label = '自定义'
    }
    return { start: this.formatDateTime(s), end: this.formatDateTime(e), label }
  },

  formatDateTime(d) {
    if (!d) return ''
    const y = d.getFullYear(), m = String(d.getMonth() + 1).padStart(2, '0'), day = String(d.getDate()).padStart(2, '0')
    const hh = String(d.getHours()).padStart(2, '0'), mm = String(d.getMinutes()).padStart(2, '0'), ss = String(d.getSeconds()).padStart(2, '0')
    return `${y}-${m}-${day} ${hh}:${mm}:${ss}`
  },
  formatDate(d) {
    const y = d.getFullYear(), m = String(d.getMonth() + 1).padStart(2, '0'), day = String(d.getDate()).padStart(2, '0')
    return `${y}-${m}-${day}`
  },

  updateTimeLabel() {
    const { start_time, end_time } = this.data.filters
    if (!start_time && !end_time) {
      this.setData({ timeRangeLabel: '时间' })
      return
    }
    const s = start_time ? start_time.substring(5, 10) : '..'
    const e = end_time ? end_time.substring(5, 10) : '..'
    this.setData({ timeRangeLabel: `${s}~${e}` })
  },

  // 点击位置：按类型筛选
  onLocationTap(e) {
    const { type } = e.currentTarget.dataset
    // 确保type不是undefined，如果是则使用空字符串
    const safeType = type !== undefined ? type : ''
    // 点击位置默认以类型筛选并刷新
    this.setData({
      'filters.moment_type': safeType,
      page: 1,
      hasMore: true,
      momentsList: []
    })
    this.loadMomentsList(true)
  },

  // 筛选变更
  onTypeFilterChange(e) {
    // Vant组件的change事件直接传递value
    const value = e.detail || e
    // 确保value不是undefined，如果是则使用空字符串
    const safeValue = value !== undefined ? value : ''

    this.setData({
      'filters.moment_type': safeValue,
      page: 1,
      hasMore: true,
      momentsList: []
    })
    this.loadMomentsList(true)
  },

  // 预览图片
  previewImage(e) {
    const { url } = e.currentTarget.dataset
    const { momentsList } = this.data

    // 获取所有图片URL用于预览
    const imageUrls = momentsList
      .filter(item => item.isImage && item.url)
      .map(item => item.url)

    wx.previewImage({
      current: url,
      urls: imageUrls
    })
  },

  // 播放视频
  playVideo(e) {
    const { url } = e.currentTarget.dataset
    console.log('播放视频:', url)
  },

  // 视频加载错误处理
  onVideoError(e) {
    console.warn('视频加载失败:', e.detail)
    // 可以在这里添加错误提示或降级处理
  },

  // 点赞功能
  toggleLike(e) {
    const { index } = e.currentTarget.dataset
    const momentsList = [...this.data.momentsList]
    const item = momentsList[index]

    // 这里可以调用点赞API
    item.isLiked = !item.isLiked
    item.likeCount = (item.likeCount || 0) + (item.isLiked ? 1 : -1)

    this.setData({ momentsList })

    wx.showToast({
      title: item.isLiked ? '已点赞' : '已取消',
      icon: 'none',
      duration: 1000
    })
  },

  // 分享功能（微信原生分享）
  onShareAppMessage(res) {
    // 如果是从分享按钮触发
    if (res.from === 'button') {
      const index = res.target.dataset.index
      const moment = this.data.momentsList[index]
      if (moment) {
        return {
          title: `${moment.displayName}分享了工作照片`,
          path: `/pages/markicam/index`,
          imageUrl: moment.isImage ? moment.url : ''
        }
      }
    }
    // 默认分享
    return {
      title: '社区工作照片',
      path: '/pages/markicam/index'
    }
  }
})
