<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('住户详情')" />
    <style>
        .container-div {
            background: #f0f2f5;
            padding: 15px;
        }

        .container-div .row{
            height: initial!important;
        }

        .ibox {
            margin-bottom: 10px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,.1);
            height: auto;
        }
        .ibox-title {
            padding: 15px;
            border-bottom: 1px solid #eee;
            background-color: #f8f8f8;
            border-radius: 4px 4px 0 0;
        }
        .ibox-content {
            padding: 20px;
            border-style: none;
        }

        .form-group {
            margin-bottom: 15px;
        }
        .control-label {
            padding-top: 7px;
            margin-bottom: 0;
            text-align: right;
        }
        .form-control-static {
            padding-top: 7px;
            margin-bottom: 0;
            min-height: 34px;
        }
        .btn-group {
            margin-bottom: 20px;
        }
        .tab-content {
            padding: 20px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
        }
        .nav-tabs {
            margin-bottom: 0;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .badge {
            padding: 4px 8px;
            border-radius: 3px;
        }
        .badge-primary {
            background-color: #1890ff;
        }
        .badge-default {
            background-color: #d9d9d9;
            color: #666;
        }
        .btn-xs {
            padding: 2px 8px;
            font-size: 12px;
            margin: 0 2px;
        }
        .operation-btn {
            margin-bottom: 10px;
        }

        /* 固定底部按钮样式 */
        .fixed-bottom-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #fff;
            border-top: 1px solid #ddd;
            padding: 15px 0;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .fixed-bottom-buttons .btn {
            margin: 0 10px;
            min-width: 100px;
        }

        /* 为页面内容添加底部间距，避免被固定按钮遮挡 */
        body {
            padding-bottom: 80px;
        }
    </style>
    
</head>
<body class="gray-bg">
    <div class="container-div">
        <!-- 业主基本信息 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>业主信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row" id="ownerInfo">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">业主ID：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="owner_id">-</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">姓名：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="owner_name">-</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">性别：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="gender_text">-</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">手机号：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="mobile">-</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">身份证号：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="id_card">-</p>
                                    </div>
                                </div>
                            </div>
                               <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">绑定车辆：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="car_info">-</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">创建时间：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="create_time">-</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">备注：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="remark">-</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>房屋信息</h5>
                    </div>
                    <div class="ibox-content p-0">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>楼栋/单元</th>
                                            <th>房间号</th>
                                            <th>建筑面积</th>
                                            <th>是否默认</th>
                                            <th>关系类型</th>
                                            <th>审核状态</th>
                                            <th>房屋状态</th>
                                        </tr>
                                    </thead>
                                    <tbody id="houseListBody">
                                        <tr>
                                            <td colspan="7" class="text-center">暂无房屋信息</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                </div>

                <!-- 车辆信息 -->
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>车辆信息</h5>
                    </div>
                    <div class="ibox-content p-0">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>车牌号</th>
                                        <th>车辆类型</th>
                                        <th>车主姓名</th>
                                        <th>绑定车位</th>
                                        <th>车辆品牌</th>
                                        <th>备注</th>
                                    </tr>
                                </thead>
                                <tbody id="vehicleListBody">
                                    <tr>
                                        <td colspan="6" class="text-center">暂无车辆信息</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 车位信息 -->
                <div class="ibox" style="margin-bottom: 100px !important;">
                    <div class="ibox-title">
                        <h5>车位信息</h5>
                    </div>
                    <div class="ibox-content p-0">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>车位号</th>
                                        <th>车位类型</th>
                                        <th>车位状态</th>
                                        <th>面积(㎡)</th>
                                        <th>绑定车牌号</th>
                                        <th>备注</th>
                                    </tr>
                                </thead>
                                <tbody id="parkingListBody">
                                    <tr>
                                        <td colspan="6" class="text-center">暂无车位信息</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 底部间距 -->
                <div style="height: 100px;"></div>
            </div>

        </div>
    </div>

    <!-- 固定底部按钮 -->
    <div class="fixed-bottom-buttons">        
            <div class="row">
                <div class="col-sm-12 text-center">
                    <button type="button" class="btn btn-default" onclick="pageBack()">
                        <i class="fa fa-reply"></i> 返回
                    </button>
                    <button type="button" class="btn btn-primary" onclick="editOwner()">
                        <i class="fa fa-edit"></i> 修改
                    </button>
                    <button type="button" class="btn btn-info" onclick="ownerBindings()">
                        <i class="fa fa-link"></i> 绑定信息
                    </button>
                </div>
            </div>        
    </div>

    <th:block th:include="include :: footer" />

    <!-- jsrender模板 -->
    <script id="houseListTemplate" type="text/x-jsrender">
        {{if data.length > 0}}
            {{for data}}
                <tr>
                    <td>{{:building_name || '-'}}/{{:unit_name || '-'}}</td>
                    <td>{{:room || '-'}}</td>
                    <td>{{:total_area || '0'}}㎡</td>
                    <td>{{if is_default == 1}}<span class="badge badge-primary">默认</span>{{else}}<span class="badge badge-default">-</span>{{/if}}</td>
                    <td>{{if rel_type == 1}}业主{{else rel_type == 2}}家庭成员{{else rel_type == 3}}租户{{else}}-{{/if}}</td>
                    <td>{{if check_status == 0}}未审核{{else check_status == 1}}已审核{{else check_status == 2}}审核不通过{{else}}-{{/if}}</td>
                    <td>{{:house_status || '-'}}</td>
                </tr>
            {{/for}}
        {{else}}
            <tr>
                <td colspan="7" class="text-center">暂无房屋信息</td>
            </tr>
        {{/if}}
    </script>

    <script id="vehicleListTemplate" type="text/x-jsrender">
        {{if data.length > 0}}
            {{for data}}
                <tr>
                    <td>{{:plate_no || '-'}}</td>
                    <td>{{if vehicle_type == '1'}}业主车辆{{else vehicle_type == '2'}}公共车辆{{else}}{{:vehicle_type || '-'}}{{/if}}</td>
                    <td>{{:owner_real_name || '-'}}</td>
                    <td>{{:parking_space || '-'}}</td>
                    <td>{{:vehicle_brand || '-'}}</td>
                    <td>{{:remark || '-'}}</td>
                </tr>
            {{/for}}
        {{else}}
            <tr>
                <td colspan="6" class="text-center">暂无车辆信息</td>
            </tr>
        {{/if}}
    </script>

    <script id="parkingListTemplate" type="text/x-jsrender">
        {{if data.length > 0}}
            {{for data}}
                <tr>
                    <td>{{:parking_no || '-'}}</td>
                    <td>{{if parking_type == 1}}私人车位{{else parking_type == 2}}子母车位{{else}}-{{/if}}</td>
                    <td>{{if parking_status == 1}}出售{{else parking_status == 2}}出租{{else parking_status == 3}}自用{{else}}-{{/if}}</td>
                    <td>{{if parking_area}}{{:parking_area}}㎡{{else}}-{{/if}}</td>
                    <td>{{:plate_no || '-'}}</td>
                    <td>{{:remark || '-'}}</td>
                </tr>
            {{/for}}
        {{else}}
            <tr>
                <td colspan="6" class="text-center">暂无车位信息</td>
            </tr>
        {{/if}}
    </script>

    <script type="text/javascript">
        var prefix = ctx + "oc/owner";
        var housePrefix = ctx + "oc/house";
        var ownerId = "";



        $(function() {
            // 从URL中获取业主ID
            var pathArray = window.location.pathname.split('/');
            ownerId = pathArray[pathArray.length - 1];

            // 页面加载完成后执行
            loadOwnerInfo();
            loadHouseList();
            // 加载车辆列表
            loadVehicleList();
            // 加载车位列表
            loadParkingList();
        });

        /* 加载业主信息 */
        function loadOwnerInfo() {
            $.ajax({
                url: prefix + "/record",
                type: "post",
                data: { owner_id: ownerId },
                success: function(res) {
                    if (res.code == 0) {
                        var owner = res.data;
                        // 使用fillRecord填充业主信息
                        fillRecord(owner, "", ",", "#ownerInfo");
                        
                        // 处理性别显示
                        var genderText = owner.gender == 'M' ? '男' : '女';
                        $("#gender_text").text(genderText);
                    } else {
                        $.modal.alertError("加载业主信息失败：" + res.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("加载业主信息失败");
                }
            });
        }
        
        /* 加载房屋列表 */
        function loadHouseList() {
            $.ajax({
                url: prefix + "/binding/houseList",
                type: "post",
                data: { ownerId: ownerId },
                success: function(res) {
                    if (res.code == 0) {
                        var houses = res.rows || [];
                        var template = $.templates("#houseListTemplate");
                        var html = template.render({data:houses});
                        $('#houseListBody').html(html);
                    }
                }
            });
        }

        /* 编辑业主 */
        function editOwner() {
            var url = prefix + '/edit/' + ownerId;
            $.modal.open("修改业主", url);
        }

        /* 绑定信息管理 */
        function ownerBindings() {
            var url = ctx + 'oc/binding/owner/manage/' + ownerId;
            $.modal.popupRight("绑定信息管理", url);
        }

        /* 加载车辆列表 */
        function loadVehicleList() {
            $.ajax({
                url: prefix + "/binding/vehicleList",
                type: "post",
                data: { ownerId: ownerId },
                success: function(res) {
                    if (res.code == 0) {
                        var vehicles = res.rows || [];
                        var template = $.templates("#vehicleListTemplate");
                        var html = template.render({data:vehicles});
                        $('#vehicleListBody').html(html);
                    }
                }
            });
        }

        /* 加载车位列表 */
        function loadParkingList() {
            $.ajax({
                url: prefix + "/binding/parkingList",
                type: "post",
                data: { ownerId: ownerId },
                success: function(res) {
                    if (res.code == 0) {
                        var parkings = res.rows || [];
                        var template = $.templates("#parkingListTemplate");
                        var html = template.render({data:parkings});
                        $('#parkingListBody').html(html);
                    }
                }
            });
        }


        pageBack = function(){
            $.modal.close();
        }
        
    </script>
</body>
</html> 