# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Test Commands

1. **Build the project**:
   ```bash
   mvn clean install -DskipTests
   ```

2. **Run tests**:
   ```bash
   mvn test
   ```

3. **Run a single test class**:
   ```bash
   mvn test -Dtest=TestClassName
   ```

4. **Run a single test method**:
   ```bash
   mvn test -Dtest=TestClassName#testMethodName
   ```

## Code Architecture

The project is a multi-module Maven project with the following key modules:

1. **ehome-common**: Contains shared utilities and common functionality.
2. **ehome-system**: Implements core system features like user management, roles, and permissions.
3. **ehome-framework**: Provides framework-level configurations and integrations (e.g., Shiro for security).
4. **ehome-oc**: Contains business logic for the "智慧家园" (Smart Home) system.
5. **ehome-admin**: Implements administrative features.
6. **ehome-page**: <PERSON>les frontend resources and templates.
7. **ehome-web**: The main web application module.

## Key Technologies

- **Backend**: Spring Boot, Shiro, MyBatis, Druid.
- **Frontend**: Thymeleaf, LayUI.
- **Database**: MySQL.
- **Build**: Maven.

## Important Notes

- The project uses **Spring Boot 2.7.18** and **Java 8**.
- **Shiro** is used for authentication and authorization.
- **Druid** is the database connection pool.
- **FastJSON** is used for JSON processing.
- **Swagger** is used for API documentation.

## Development Workflow

1. Make changes in the relevant module.
2. Run `mvn clean install -DskipTests` to build the project.
3. Test changes locally.
4. Commit and push changes.

## Cursor Rules

- Follow the coding conventions and patterns observed in the existing codebase.
- Avoid introducing new dependencies unless absolutely necessary.
- Ensure all changes are backward-compatible.

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>