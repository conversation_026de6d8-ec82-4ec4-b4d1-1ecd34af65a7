-- 初始化小区服务电话数据
-- 使用方法：将 {community_id} 替换为实际的小区ID

-- 外部号码分类（生活服务类别）
-- 1. 衣 - 服装购物
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'external', '衣', '', 1, '0', NOW());

-- 衣类服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES
(LAST_INSERT_ID(), '{community_id}', '************', 'external', '干洗店', '各品牌干洗店', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '************', 'external', '裁缝店', '各品牌裁缝店', 2, '0', NOW());

-- 2. 食 - 餐饮外卖
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'external', '食', '', 2, '0', NOW());

-- 食类服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES
(LAST_INSERT_ID(), '{community_id}', '************', 'external', '超市', '各大连锁超市', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '10109777', 'external', '外卖', '美团饿了么等', 2, '0', NOW());

-- 3. 住 - 居住服务
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'external', '住', '', 3, '0', NOW());

-- 住类服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES
(LAST_INSERT_ID(), '{community_id}', '95598', 'external', '供电', '国家电网', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '96777', 'external', '燃气', '燃气公司', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '96116', 'external', '供水', '自来水公司', 3, '0', NOW());

-- 4. 行 - 出行交通
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'external', '行', '', 4, '0', NOW());

-- 行类服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES
(LAST_INSERT_ID(), '{community_id}', '************', 'external', '打车', '滴滴高德等', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '122', 'external', '交通事故', '交通管理部门', 2, '0', NOW());

-- 5. 生活 - 生活服务
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'external', '生活', '', 5, '0', NOW());

-- 生活服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES
(LAST_INSERT_ID(), '{community_id}', '************', 'external', '家政', '各品牌家政公司', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '************', 'external', '快递', '各大快递公司', 2, '0', NOW());

-- 6. 政府 - 政务服务
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'external', '政府', '', 6, '0', NOW());

-- 政府服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES
(LAST_INSERT_ID(), '{community_id}', '110', 'external', '报警', '公安部门', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '119', 'external', '火警', '消防部门', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '120', 'external', '急救', '医疗急救', 3, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '12345', 'external', '政务热线', '各级政府部门', 4, '0', NOW());

-- 内部号码分类
-- 1. 物业服务分类
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'internal', '物业服务', '', 7, '0', NOW());

-- 物业服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES 
(LAST_INSERT_ID(), '{community_id}', '8001', 'internal', '物业客服', '物业管理处', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8002', 'internal', '物业经理', '物业管理处', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8003', 'internal', '收费处', '物业管理处', 3, '0', NOW());

-- 2. 安保服务分类
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'internal', '安保服务', '', 8, '0', NOW());

-- 安保服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES 
(LAST_INSERT_ID(), '{community_id}', '8010', 'internal', '门岗值班', '安保部', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8011', 'internal', '巡逻队', '安保部', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8012', 'internal', '监控中心', '安保部', 3, '0', NOW());

-- 3. 维修服务分类
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'internal', '维修服务', '', 9, '0', NOW());

-- 维修服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES 
(LAST_INSERT_ID(), '{community_id}', '8020', 'internal', '水电维修', '维修部', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8021', 'internal', '电梯维修', '维修部', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8022', 'internal', '绿化养护', '维修部', 3, '0', NOW());
