package com.ehome.oc.controller.assets;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.utils.uuid.Seq;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.regex.Pattern;
import org.springframework.web.multipart.MultipartFile;
import com.ehome.common.utils.poi.ExcelUtil;
import com.ehome.oc.domain.VehicleImport;

@Controller
@RequestMapping("/oc/vehicle")
public class VehicleMgrController extends BaseController {

    private static final String PREFIX = "oc/vehicle";

    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list";
    }

    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "select t1.*",
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        String vehicleId = params.getString("vehicle_id");
        if (StringUtils.isEmpty(vehicleId)) {
            return AjaxResult.error("车辆ID不能为空");
        }

        // 获取车辆基本信息
        Record vehicle = Db.findFirst("select * from eh_vehicle where vehicle_id = ?", vehicleId);
        if (vehicle == null) {
            return AjaxResult.error("车辆不存在");
        }
        return AjaxResult.success(null, vehicle.toMap());
    }

    @GetMapping("/add")
    public String add() {
        return PREFIX + "/add";
    }

    @GetMapping("/edit/{vehicleId}")
    public String edit(@PathVariable("vehicleId") String vehicleId, ModelMap mmap) {
        Record vehicle = Db.findFirst("select * from eh_vehicle where vehicle_id = ?", vehicleId);
        mmap.put("vehicle", vehicle.toMap());
        return PREFIX + "/edit";
    }

    @GetMapping("/bindings/{vehicleId}")
    public String bindings(@PathVariable("vehicleId") String vehicleId, ModelMap mmap) {
        Record vehicle = Db.findFirst("select * from eh_vehicle where vehicle_id = ?", vehicleId);
        mmap.put("vehicleId", vehicleId);
        mmap.put("vehicle", vehicle.toMap());
        return PREFIX + "/bindings";
    }

    @Log(title = "新增车辆", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave() {
        JSONObject params = getParams();
        Record vehicle = new Record();
        vehicle.setColumns(params);
        vehicle.set("vehicle_id", Seq.getId());
        vehicle.set("check_status", "1"); // 默认审核通过
        setCreateAndUpdateInfo(vehicle);
        Db.save("eh_vehicle", "vehicle_id", vehicle);
        String vehicleId = vehicle.getStr("vehicle_id");
        return AjaxResult.success(vehicle);
    }

    @Log(title = "修改车辆", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave() {
        JSONObject params = getParams();
        Record vehicle = new Record();
        vehicle.setColumns(params);
        setUpdateInfo(vehicle);
        return toAjax(Db.update("eh_vehicle", "vehicle_id", vehicle));
    }

    @Log(title = "删除车辆", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return error("参数id不能为空");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            Db.deleteById("eh_vehicle", "vehicle_id", id);
        }
        return success();
    }
    
    /**
     * 业主选择对话框
     */
    @GetMapping("/ownerDialog")
    public String ownerDialog() {
        return PREFIX + "/ownerDialog";
    }
    
    /**
     * 查询业主列表
     */
    @RequestMapping("/ownerList")
    @ResponseBody
    public TableDataInfo ownerList() {
        JSONObject params = getParams();
        EasySQL sql = new EasySQL();
        sql.append("from eh_owner where 1=1");
        sql.append(getSysUser().getCommunityId(), "and community_id = ?");
        sql.appendLike(params.getString("owner_name"), "and owner_name like ?");
        sql.appendLike(params.getString("mobile"), "and mobile like ?");
        sql.append(params.getString("role"), "and role = ?");
        sql.append("order by create_time desc");

        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "select *",
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }
    
    /**
     * 停车位选择对话框
     */
    @GetMapping("/parkingSpaceDialog")
    public String parkingSpaceDialog() {
        return PREFIX + "/parkingSpaceDialog";
    }
    
    /**
     * 查询停车位列表
     */
    @RequestMapping("/parkingSpaceList")
    @ResponseBody
    public TableDataInfo parkingSpaceList() {
        JSONObject params = getParams();
        EasySQL sql = new EasySQL();
        sql.append("from eh_parking_space where 1=1");
        sql.append(getSysUser().getCommunityId(), "and community_id = ?");
        sql.appendLike(params.getString("parking_no"), "and parking_no like ?");
        sql.append(params.getString("parking_type"), "and parking_type = ?");
        sql.append(params.getString("parking_status"), "and parking_status = ?");
        sql.append("order by create_time desc");

        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "select *",
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_vehicle t1");
        sql.append("where 1=1");
        sql.append(getSysUser().getCommunityId(), "and t1.community_id = ?");
        sql.appendLike(params.getString("plate_no"), "and t1.plate_no like ?");
        sql.append(params.getString("vehicle_type"), "and t1.vehicle_type = ?");
        sql.append(params.getString("check_status"), "and t1.check_status = ?");
        sql.append("order by t1.create_time desc");
        return sql;
    }

    private void setCreateAndUpdateInfo(Record record) {
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String loginName = getSysUser().getLoginName();
        record.set("community_id", getSysUser().getCommunityId());
        record.set("create_time", now);
        record.set("update_time", now);
        record.set("create_by", loginName);
        record.set("update_by", loginName);
    }

    private void setUpdateInfo(Record record) {
        record.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        record.set("update_by", getSysUser().getLoginName());
    }

    /**
     * 绑定住户
     */
    @Log(title = "车辆绑定住户", businessType = BusinessType.INSERT)
    @PostMapping("/bindOwner")
    @ResponseBody
    public AjaxResult bindOwner() {
        JSONObject params = getParams();
        String vehicleId = params.getString("vehicleId");
        String ownerId = params.getString("ownerId");

        if (StringUtils.isEmpty(vehicleId)) {
            return AjaxResult.error("车辆ID不能为空");
        }
        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("住户ID不能为空");
        }
        try {
            // 检查是否已绑定
            Record existRel = Db.findFirst(
                "SELECT * FROM eh_vehicle_owner_rel WHERE vehicle_id = ? AND owner_id = ?",
                vehicleId, ownerId
            );
            if (existRel != null) {
                return AjaxResult.error("该车辆已绑定到此住户");
            }
            // 创建绑定关系
            Record rel = new Record();
            rel.set("rel_id", Seq.getId());
            rel.set("vehicle_id", vehicleId);
            rel.set("owner_id", ownerId);
            rel.set("is_default", 0);
            rel.set("check_status", "1"); // 默认已审核
            rel.set("create_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
            rel.set("create_by", getSysUser().getLoginName());
            rel.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
            rel.set("update_by", getSysUser().getLoginName());

            boolean success = Db.save("eh_vehicle_owner_rel", "rel_id", rel);
            if (success) {
                // 同步更新车辆表中的住户信息（取默认住户）
                updateVehicleOwnerInfo(vehicleId);
            }
            return toAjax(success);
        } catch (Exception e) {
            logger.error("绑定住户失败", e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 绑定房屋
     */
    @Log(title = "车辆绑定房屋", businessType = BusinessType.INSERT)
    @PostMapping("/bindHouse")
    @ResponseBody
    public AjaxResult bindHouse() {
        JSONObject params = getParams();
        String vehicleId = params.getString("vehicleId");
        String houseId = params.getString("houseId");

        if (StringUtils.isEmpty(vehicleId)) {
            return AjaxResult.error("车辆ID不能为空");
        }
        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }

        try {
            // 检查是否已绑定
            Record existRel = Db.findFirst(
                "SELECT * FROM eh_vehicle_house_rel WHERE vehicle_id = ? AND house_id = ?",
                vehicleId, houseId
            );

            if (existRel != null) {
                return AjaxResult.error("该车辆已绑定到此房屋");
            }

            // 创建绑定关系
            Record rel = new Record();
            rel.set("rel_id", Seq.getId());
            rel.set("vehicle_id", vehicleId);
            rel.set("house_id", houseId);
            rel.set("is_default", 0);
            rel.set("check_status", "1"); // 默认已审核
            rel.set("create_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
            rel.set("create_by", getSysUser().getLoginName());
            rel.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
            rel.set("update_by", getSysUser().getLoginName());

            boolean success = Db.save("eh_vehicle_house_rel", "rel_id", rel);
            if (success) {
                // 同步更新车辆表中的房屋信息
                updateVehicleHouseInfo(vehicleId);
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("绑定房屋失败", e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 绑定车位
     */
    @Log(title = "车辆绑定车位", businessType = BusinessType.INSERT)
    @PostMapping("/bindParkingSpace")
    @ResponseBody
    public AjaxResult bindParkingSpace() {
        JSONObject params = getParams();
        String vehicleId = params.getString("vehicleId");
        String parkingId = params.getString("parkingId");

        if (StringUtils.isEmpty(vehicleId)) {
            return AjaxResult.error("车辆ID不能为空");
        }
        if (StringUtils.isEmpty(parkingId)) {
            return AjaxResult.error("车位ID不能为空");
        }

        try {
            // 检查是否已绑定
            Record existRel = Db.findFirst(
                "SELECT * FROM eh_vehicle_parking_rel WHERE vehicle_id = ? AND parking_id = ?",
                vehicleId, parkingId
            );

            if (existRel != null) {
                return AjaxResult.error("该车辆已绑定到此车位");
            }

            // 创建绑定关系
            Record rel = new Record();
            rel.set("rel_id", Seq.getId());
            rel.set("vehicle_id", vehicleId);
            rel.set("parking_id", parkingId);
            rel.set("is_default", 0);
            rel.set("check_status", "1"); // 默认已审核
            rel.set("create_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
            rel.set("create_by", getSysUser().getLoginName());
            rel.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
            rel.set("update_by", getSysUser().getLoginName());

            boolean success = Db.save("eh_vehicle_parking_rel", "rel_id", rel);
            if (success) {
                // 同步更新车辆表中的车位信息
                updateVehicleParkingInfo(vehicleId);
                // 同步更新车位表中的车牌号信息
                updateParkingVehicleInfo(parkingId);
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("绑定车位失败", e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 更新车辆表中的住户信息
     */
    private void updateVehicleOwnerInfo(String vehicleId) {
        String str = Db.queryStr("SELECT GROUP_CONCAT(concat(o.owner_name, '(', o.mobile,')')) AS owner_name FROM eh_vehicle_owner_rel r LEFT JOIN eh_owner o ON r.owner_id = o.owner_id WHERE r.vehicle_id =  ?", vehicleId);
        if (StringUtils.isEmpty(str)) {
            str = "";
        }
        Db.update("UPDATE eh_vehicle SET owner_name = ? WHERE vehicle_id = ?", str, vehicleId);
        Db.update("UPDATE eh_vehicle SET owner_count = ? WHERE vehicle_id = ?", str.split(",").length, vehicleId);
    }

    /**
     * 更新车辆表中的房屋信息
     */
    private void updateVehicleHouseInfo(String vehicleId) {
        String str = Db.queryStr("SELECT GROUP_CONCAT( concat(h.combina_name, '/', h.room)) AS house_name FROM eh_vehicle_house_rel r LEFT JOIN eh_house_info h ON r.house_id = h.house_id WHERE r.vehicle_id =  ?", vehicleId);
        Db.update("UPDATE eh_vehicle SET house_name = ? WHERE vehicle_id = ?", str, vehicleId);
    }

    /**
     * 更新车辆表中的车位信息
     */
    private void updateVehicleParkingInfo(String vehicleId) {
        String str = Db.queryStr("SELECT GROUP_CONCAT(p.parking_no) AS parking_no FROM eh_vehicle_parking_rel r LEFT JOIN eh_parking_space p ON r.parking_id = p.parking_id WHERE r.vehicle_id =  ?", vehicleId);
        Db.update("UPDATE eh_vehicle SET parking_space = ? WHERE vehicle_id = ?", str, vehicleId);
    }

    /**
     * 更新车位表中的车牌号信息
     */
    private void updateParkingVehicleInfo(String parkingId) {
        try {
            // 获取绑定到该车位的所有车辆车牌号
            String plateNos = Db.queryStr(
                "SELECT GROUP_CONCAT(v.plate_no ORDER BY r.create_time ASC SEPARATOR ',') AS plate_nos " +
                "FROM eh_vehicle_parking_rel r " +
                "LEFT JOIN eh_vehicle v ON r.vehicle_id = v.vehicle_id " +
                "WHERE r.parking_id = ?",
                parkingId
            );

            if (plateNos == null) {
                plateNos = "";
            }

            // 更新车位表中的车牌号信息
            Db.update("UPDATE eh_parking_space SET plate_no = ? WHERE parking_id = ?", plateNos, parkingId);
        } catch (Exception e) {
            logger.error("更新车位车牌号信息失败", e);
        }
    }

    /**
     * 解绑住户
     */
    @Log(title = "车辆解绑住户", businessType = BusinessType.DELETE)
    @PostMapping("/unbindOwner")
    @ResponseBody
    public AjaxResult unbindOwner() {
        JSONObject params = getParams();
        String vehicleId = params.getString("vehicleId");
        String ownerId = params.getString("ownerId");

        if (StringUtils.isEmpty(vehicleId)) {
            return AjaxResult.error("车辆ID不能为空");
        }
        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("住户ID不能为空");
        }

        try {
            // 删除绑定关系
            int result = Db.delete("DELETE FROM eh_vehicle_owner_rel WHERE vehicle_id = ? AND owner_id = ?", vehicleId, ownerId);
            boolean success = result > 0;
            if (success) {
                // 同步更新车辆表中的住户信息
                updateVehicleOwnerInfo(vehicleId);
            }
            return toAjax(success);
        } catch (Exception e) {
            logger.error("解绑住户失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }

    /**
     * 解绑房屋
     */
    @Log(title = "车辆解绑房屋", businessType = BusinessType.DELETE)
    @PostMapping("/unbindHouse")
    @ResponseBody
    public AjaxResult unbindHouse() {
        JSONObject params = getParams();
        String vehicleId = params.getString("vehicleId");
        String houseId = params.getString("houseId");

        if (StringUtils.isEmpty(vehicleId)) {
            return AjaxResult.error("车辆ID不能为空");
        }
        if (StringUtils.isEmpty(houseId)) {
            return AjaxResult.error("房屋ID不能为空");
        }
        try {
            // 删除绑定关系
            int result = Db.delete("DELETE FROM eh_vehicle_house_rel WHERE vehicle_id = ? AND house_id = ?", vehicleId, houseId);
            boolean success = result > 0;
            if (success) {
                // 同步更新车辆表中的房屋信息
                updateVehicleHouseInfo(vehicleId);
            }
            return toAjax(success);
        } catch (Exception e) {
            logger.error("解绑房屋失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }

    /**
     * 解绑车位
     */
    @Log(title = "车辆解绑车位", businessType = BusinessType.DELETE)
    @PostMapping("/unbindParkingSpace")
    @ResponseBody
    public AjaxResult unbindParkingSpace() {
        JSONObject params = getParams();
        String vehicleId = params.getString("vehicleId");
        String parkingId = params.getString("parkingId");

        if (StringUtils.isEmpty(vehicleId)) {
            return AjaxResult.error("车辆ID不能为空");
        }
        if (StringUtils.isEmpty(parkingId)) {
            return AjaxResult.error("车位ID不能为空");
        }

        try {
            // 删除绑定关系
            int result = Db.delete("DELETE FROM eh_vehicle_parking_rel WHERE vehicle_id = ? AND parking_id = ?", vehicleId, parkingId);
            boolean success = result > 0;
            if (success) {
                // 同步更新车辆表中的车位信息
                updateVehicleParkingInfo(vehicleId);
                // 同步更新车位表中的车牌号信息
                updateParkingVehicleInfo(parkingId);
            }
            return toAjax(success);
        } catch (Exception e) {
            logger.error("解绑车位失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }

    /**
     * 查询车辆绑定的住户列表
     */
    @RequestMapping("/ownerBindings")
    @ResponseBody
    public AjaxResult ownerBindings() {
        JSONObject params = getParams();
        String vehicleId = params.getString("vehicleId");
        if (StringUtils.isEmpty(vehicleId)) {
            return AjaxResult.error("车辆ID不能为空");
        }

        String sql = "SELECT r.*, o.owner_name, o.mobile FROM eh_vehicle_owner_rel r " +
                     "LEFT JOIN eh_owner o ON r.owner_id = o.owner_id " +
                     "WHERE r.vehicle_id = ? ORDER BY r.is_default DESC, r.create_time DESC";
        List<Record> list = Db.find(sql, vehicleId);
        return AjaxResult.success(recordToMap(list));
    }

    /**
     * 查询车辆绑定的房屋列表
     */
    @RequestMapping("/houseBindings")
    @ResponseBody
    public AjaxResult houseBindings() {
        JSONObject params = getParams();
        String vehicleId = params.getString("vehicleId");
        if (StringUtils.isEmpty(vehicleId)) {
            return AjaxResult.error("车辆ID不能为空");
        }

        String sql = "SELECT r.*, " +
                     "CONCAT(COALESCE(b.name, ''), CASE WHEN b.name IS NOT NULL THEN '/' ELSE '' END, " +
                     "COALESCE(u.name, ''), CASE WHEN u.name IS NOT NULL THEN '/' ELSE '' END, " +
                     "h.room) as house_name " +
                     "FROM eh_vehicle_house_rel r " +
                     "LEFT JOIN eh_house_info h ON r.house_id = h.house_id " +
                     "LEFT JOIN eh_building b ON h.building_id = b.building_id " +
                     "LEFT JOIN eh_unit u ON h.unit_id = u.unit_id " +
                     "WHERE r.vehicle_id = ? ORDER BY r.is_default DESC, r.create_time DESC";
        List<Record> list = Db.find(sql, vehicleId);
        return AjaxResult.success(recordToMap(list));
    }

    /**
     * 查询车辆绑定的车位列表
     */
    @RequestMapping("/parkingBindings")
    @ResponseBody
    public AjaxResult parkingBindings() {
        JSONObject params = getParams();
        String vehicleId = params.getString("vehicleId");
        if (StringUtils.isEmpty(vehicleId)) {
            return AjaxResult.error("车辆ID不能为空");
        }

        String sql = "SELECT r.*, p.parking_no, p.parking_status FROM eh_vehicle_parking_rel r " +
                     "LEFT JOIN eh_parking_space p ON r.parking_id = p.parking_id " +
                     "WHERE r.vehicle_id = ? ORDER BY r.is_default DESC, r.create_time DESC";
        List<Record> list = Db.find(sql, vehicleId);
        return AjaxResult.success(recordToMap(list));
    }

    /**
     * 删除住户绑定关系
     */
    @Log(title = "删除车辆住户绑定", businessType = BusinessType.DELETE)
    @PostMapping("/removeOwnerBinding")
    @ResponseBody
    public AjaxResult removeOwnerBinding() {
        JSONObject params = getParams();
        String relId = params.getString("relId");
        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }
        try {
            // 获取关系信息
            Record rel = Db.findFirst("SELECT * FROM eh_vehicle_owner_rel WHERE rel_id = ?", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }
            String vehicleId = rel.getStr("vehicle_id");
            // 删除绑定关系
            boolean success = Db.deleteById("eh_vehicle_owner_rel", "rel_id", relId);
            if (success) {
                // 同步更新车辆表中的住户信息
                updateVehicleOwnerInfo(vehicleId);
            }
            return toAjax(success);
        } catch (Exception e) {
            logger.error("删除住户绑定失败", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 删除房屋绑定关系
     */
    @Log(title = "删除车辆房屋绑定", businessType = BusinessType.DELETE)
    @PostMapping("/removeHouseBinding")
    @ResponseBody
    public AjaxResult removeHouseBinding() {
        JSONObject params = getParams();
        String relId = params.getString("relId");
        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }

        try {
            // 获取关系信息
            Record rel = Db.findFirst("SELECT * FROM eh_vehicle_house_rel WHERE rel_id = ?", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }

            String vehicleId = rel.getStr("vehicle_id");

            // 删除绑定关系
            boolean success = Db.deleteById("eh_vehicle_house_rel", "rel_id", relId);

            if (success) {
                // 同步更新车辆表中的房屋信息
                updateVehicleHouseInfo(vehicleId);
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("删除房屋绑定失败", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 删除车位绑定关系
     */
    @Log(title = "删除车辆车位绑定", businessType = BusinessType.DELETE)
    @PostMapping("/removeParkingBinding")
    @ResponseBody
    public AjaxResult removeParkingBinding() {
        JSONObject params = getParams();
        String relId = params.getString("relId");
        if (StringUtils.isEmpty(relId)) {
            return AjaxResult.error("关系ID不能为空");
        }

        try {
            // 获取关系信息
            Record rel = Db.findFirst("SELECT * FROM eh_vehicle_parking_rel WHERE rel_id = ?", relId);
            if (rel == null) {
                return AjaxResult.error("绑定关系不存在");
            }

            String vehicleId = rel.getStr("vehicle_id");
            String parkingId = rel.getStr("parking_id");

            // 删除绑定关系
            boolean success = Db.deleteById("eh_vehicle_parking_rel", "rel_id", relId);

            if (success) {
                // 同步更新车辆表中的车位信息
                updateVehicleParkingInfo(vehicleId);
                // 同步更新车位表中的车牌号信息
                updateParkingVehicleInfo(parkingId);
            }

            return toAjax(success);
        } catch (Exception e) {
            logger.error("删除车位绑定失败", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }


    /**
     * 获取可用于车辆绑定的房屋列表
     */
    @RequestMapping("/getAvailableHousesForVehicle")
    @ResponseBody
    public AjaxResult getAvailableHousesForVehicle() {
        JSONObject params = getParams();
        String search = params.getString("search");
        String vehicleId = params.getString("vehicleId");
        Integer page = params.getInteger("page");

        if (page == null) page = 1;
        int pageSize = 20;

        try {
            EasySQL sql = new EasySQL();
            sql.append("FROM eh_house_info h");
            sql.append("LEFT JOIN eh_building b ON h.building_id = b.building_id");
            sql.append("LEFT JOIN eh_unit u ON h.unit_id = u.unit_id");
            sql.append("WHERE 1=1");
            sql.append(getSysUser().getCommunityId(), "AND h.community_id = ?");
            sql.append("AND h.check_status = 1"); // 只显示已审核的房屋

            // 排除已绑定给该车辆的房屋
            if (StringUtils.isNotEmpty(vehicleId)) {
                sql.append("AND h.house_id NOT IN (SELECT house_id FROM eh_vehicle_house_rel WHERE vehicle_id = '" + vehicleId + "')");
            }

            // 搜索条件
            if (StringUtils.isNotEmpty(search)) {
                sql.append("AND (h.room LIKE '%" + search + "%' OR b.name LIKE '%" + search + "%' OR u.name LIKE '%" + search + "%')");
            }

            sql.append("ORDER BY b.name, u.name, h.room");

            // 使用分页查询
            Page<Record> pageResult = Db.paginate(page, pageSize,
                "SELECT h.house_id as id, CONCAT(COALESCE(b.name, ''), CASE WHEN b.name IS NOT NULL THEN '/' ELSE '' END, COALESCE(u.name, ''), CASE WHEN u.name IS NOT NULL THEN '/' ELSE '' END, h.room) as text",
                sql.toFullSql());

            return AjaxResult.success(recordToMap(pageResult.getList()));
        } catch (Exception e) {
            logger.error("获取可选房屋列表失败", e);
            return AjaxResult.error("获取房屋列表失败");
        }
    }

    /**
     * 导入车辆数据
     */
    @Log(title = "车辆管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<VehicleImport> util = new ExcelUtil<VehicleImport>(VehicleImport.class);
        List<VehicleImport> vehicleList = util.importExcel(file.getInputStream());

        String message = importVehicle(vehicleList, updateSupport, getLoginName());
        return AjaxResult.success(message);
    }

    /**
     * 下载车辆导入模板
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate() {
        ExcelUtil<VehicleImport> util = new ExcelUtil<VehicleImport>(VehicleImport.class);
        return util.importTemplateExcel("车辆数据");
    }

    /**
     * 导入车辆数据处理
     */
    private String importVehicle(List<VehicleImport> vehicleList, boolean updateSupport, String operName) {
        if (vehicleList == null || vehicleList.isEmpty()) {
            throw new RuntimeException("导入车辆数据不能为空！");
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String communityId = getSysUser().getCommunityId();

        for (VehicleImport vehicleImport : vehicleList) {
            try {
                // 验证必填字段
                if (StringUtils.isEmpty(vehicleImport.getPlateNo())) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、车牌号码不能为空");
                    continue;
                }

                if (StringUtils.isEmpty(vehicleImport.getVehicleType())) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、车辆类型不能为空");
                    continue;
                }

                // 验证车牌号格式（简单验证）
                String plateNo = vehicleImport.getPlateNo().trim();
                if (!Pattern.matches("^[\\u4e00-\\u9fa5][A-Z][A-Z0-9]{5}$", plateNo)) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、车牌号格式不正确：").append(plateNo);
                    continue;
                }

                // 检查车牌号是否已存在
                Record existVehicle = Db.findFirst("SELECT vehicle_id FROM eh_vehicle WHERE community_id = ? AND plate_no = ?",
                    communityId, plateNo);
                if (existVehicle != null && !updateSupport) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、车牌号已存在：").append(plateNo);
                    continue;
                }

                // 验证手机号格式（如果填写了）
                String ownerPhone = vehicleImport.getOwnerPhone();
                if (StringUtils.isNotEmpty(ownerPhone) && !Pattern.matches("^1[3-9]\\d{9}$", ownerPhone.trim())) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、手机号格式不正确：").append(ownerPhone);
                    continue;
                }

                String vehicleId;
                if (existVehicle != null && updateSupport) {
                    // 更新现有车辆
                    vehicleId = existVehicle.getStr("vehicle_id");
                    updateExistingVehicle(vehicleId, vehicleImport, operName);
                } else {
                    // 创建新车辆
                    vehicleId = createNewVehicle(vehicleImport, communityId, operName);
                }

                // 处理绑定关系
                processVehicleBindings(vehicleId, vehicleImport, communityId, operName);

                successNum++;
                successMsg.append("<br/>").append(successNum).append("、车牌号 ").append(plateNo).append(" 导入成功");

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、车牌号 " + vehicleImport.getPlateNo() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                logger.error("导入车辆失败", e);
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 更新现有车辆信息
     */
    private void updateExistingVehicle(String vehicleId, VehicleImport vehicleImport, String operName) {
        Record vehicle = new Record();
        vehicle.set("vehicle_id", vehicleId);
        vehicle.set("vehicle_type", vehicleImport.getVehicleType());

        if (StringUtils.isNotEmpty(vehicleImport.getOwnerRealName())) {
            vehicle.set("owner_real_name", vehicleImport.getOwnerRealName());
        }
        if (StringUtils.isNotEmpty(vehicleImport.getOwnerPhone())) {
            vehicle.set("owner_phone", vehicleImport.getOwnerPhone());
        }
        if (StringUtils.isNotEmpty(vehicleImport.getRemark())) {
            vehicle.set("remark", vehicleImport.getRemark());
        }

        vehicle.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        vehicle.set("update_by", operName);

        Db.update("eh_vehicle", "vehicle_id", vehicle);
    }

    /**
     * 创建新车辆
     */
    private String createNewVehicle(VehicleImport vehicleImport, String communityId, String operName) {
        Record vehicle = new Record();
        String vehicleId = Seq.getId();

        vehicle.set("vehicle_id", vehicleId);
        vehicle.set("community_id", communityId);
        vehicle.set("plate_no", vehicleImport.getPlateNo().trim());
        vehicle.set("vehicle_type", vehicleImport.getVehicleType());
        vehicle.set("owner_real_name", vehicleImport.getOwnerRealName());
        vehicle.set("owner_phone", vehicleImport.getOwnerPhone());
        vehicle.set("remark", vehicleImport.getRemark());
        vehicle.set("check_status", "1"); // 默认审核通过
        vehicle.set("create_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        vehicle.set("create_by", operName);
        vehicle.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        vehicle.set("update_by", operName);

        Db.save("eh_vehicle", "vehicle_id", vehicle);
        return vehicleId;
    }

    /**
     * 处理车辆绑定关系
     */
    private void processVehicleBindings(String vehicleId, VehicleImport vehicleImport, String communityId, String operName) {
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);

        // 处理住户绑定
        if (StringUtils.isNotEmpty(vehicleImport.getBindOwner())) {
            String ownerId = findOwnerByNameAndPhone(vehicleImport.getBindOwner(), communityId);
            if (StringUtils.isNotEmpty(ownerId)) {
                createOwnerBinding(vehicleId, ownerId, operName, now);
            }
        }

        // 处理房屋绑定
        if (StringUtils.isNotEmpty(vehicleImport.getBindHouse())) {
            String houseId = findHouseByName(vehicleImport.getBindHouse(), communityId);
            if (StringUtils.isNotEmpty(houseId)) {
                createHouseBinding(vehicleId, houseId, operName, now);
            }
        }

        // 处理车位绑定
        if (StringUtils.isNotEmpty(vehicleImport.getBindParking())) {
            String parkingId = findParkingByName(vehicleImport.getBindParking(), communityId);
            if (StringUtils.isNotEmpty(parkingId)) {
                createParkingBinding(vehicleId, parkingId, operName, now);
            }
        }

        // 更新车辆表中的冗余字段
        updateVehicleOwnerInfo(vehicleId);
        updateVehicleHouseInfo(vehicleId);
        updateVehicleParkingInfo(vehicleId);
    }

    /**
     * 根据姓名和手机号查找住户ID
     */
    private String findOwnerByNameAndPhone(String bindOwner, String communityId) {
        try {
            // 解析绑定住户信息，格式：姓名（手机号）
            if (bindOwner.contains("（") && bindOwner.contains("）")) {
                String[] parts = bindOwner.split("（");
                if (parts.length == 2) {
                    String ownerName = parts[0].trim();
                    String phone = parts[1].replace("）", "").trim();

                    Record owner = Db.findFirst("SELECT owner_id FROM eh_owner WHERE community_id = ? AND owner_name = ? AND mobile = ?",
                        communityId, ownerName, phone);
                    return owner != null ? owner.getStr("owner_id") : null;
                }
            }
        } catch (Exception e) {
            logger.warn("查找住户失败：" + bindOwner, e);
        }
        return null;
    }

    /**
     * 根据房屋名称查找房屋ID
     */
    private String findHouseByName(String bindHouse, String communityId) {
        try {
            // 解析房屋信息，格式：楼栋/单元/房间号
            String[] parts = bindHouse.split("/");
            if (parts.length >= 3) {
                String buildingName = parts[0].trim();
                String unitName = parts[1].trim();
                String room = parts[2].trim();

                String sql = "SELECT h.house_id FROM eh_house_info h " +
                           "LEFT JOIN eh_building b ON h.building_id = b.building_id " +
                           "LEFT JOIN eh_unit u ON h.unit_id = u.unit_id " +
                           "WHERE h.community_id = ? AND b.name = ? AND u.name = ? AND h.room = ?";

                Record house = Db.findFirst(sql, communityId, buildingName, unitName, room);
                return house != null ? house.getStr("house_id") : null;
            }
        } catch (Exception e) {
            logger.warn("查找房屋失败：" + bindHouse, e);
        }
        return null;
    }

    /**
     * 根据车位名称查找车位ID
     */
    private String findParkingByName(String bindParking, String communityId) {
        try {
            Record parking = Db.findFirst("SELECT parking_id FROM eh_parking_space WHERE community_id = ? AND parking_no = ?",
                communityId, bindParking.trim());
            return parking != null ? parking.getStr("parking_id") : null;
        } catch (Exception e) {
            logger.warn("查找车位失败：" + bindParking, e);
        }
        return null;
    }

    /**
     * 创建住户绑定关系
     */
    private void createOwnerBinding(String vehicleId, String ownerId, String operName, String now) {
        try {
            // 检查是否已存在绑定关系
            Record existRel = Db.findFirst("SELECT rel_id FROM eh_vehicle_owner_rel WHERE vehicle_id = ? AND owner_id = ?",
                vehicleId, ownerId);
            if (existRel == null) {
                Record rel = new Record();
                rel.set("rel_id", Seq.getId());
                rel.set("vehicle_id", vehicleId);
                rel.set("owner_id", ownerId);
                rel.set("is_default", 0);
                rel.set("check_status", "1");
                rel.set("create_time", now);
                rel.set("create_by", operName);
                rel.set("update_time", now);
                rel.set("update_by", operName);
                Db.save("eh_vehicle_owner_rel", "rel_id", rel);
            }
        } catch (Exception e) {
            logger.warn("创建住户绑定失败", e);
        }
    }

    /**
     * 创建房屋绑定关系
     */
    private void createHouseBinding(String vehicleId, String houseId, String operName, String now) {
        try {
            // 检查是否已存在绑定关系
            Record existRel = Db.findFirst("SELECT rel_id FROM eh_vehicle_house_rel WHERE vehicle_id = ? AND house_id = ?",
                vehicleId, houseId);
            if (existRel == null) {
                Record rel = new Record();
                rel.set("rel_id", Seq.getId());
                rel.set("vehicle_id", vehicleId);
                rel.set("house_id", houseId);
                rel.set("is_default", 0);
                rel.set("check_status", "1");
                rel.set("create_time", now);
                rel.set("create_by", operName);
                rel.set("update_time", now);
                rel.set("update_by", operName);
                Db.save("eh_vehicle_house_rel", "rel_id", rel);
            }
        } catch (Exception e) {
            logger.warn("创建房屋绑定失败", e);
        }
    }

    /**
     * 创建车位绑定关系
     */
    private void createParkingBinding(String vehicleId, String parkingId, String operName, String now) {
        try {
            // 检查是否已存在绑定关系
            Record existRel = Db.findFirst("SELECT rel_id FROM eh_vehicle_parking_rel WHERE vehicle_id = ? AND parking_id = ?",
                vehicleId, parkingId);
            if (existRel == null) {
                Record rel = new Record();
                rel.set("rel_id", Seq.getId());
                rel.set("vehicle_id", vehicleId);
                rel.set("parking_id", parkingId);
                rel.set("is_default", 0);
                rel.set("check_status", "1");
                rel.set("create_time", now);
                rel.set("create_by", operName);
                rel.set("update_time", now);
                rel.set("update_by", operName);
                Db.save("eh_vehicle_parking_rel", "rel_id", rel);

                // 更新车位表中的车牌号信息
                updateParkingVehicleInfo(parkingId);
            }
        } catch (Exception e) {
            logger.warn("创建车位绑定失败", e);
        }
    }
}