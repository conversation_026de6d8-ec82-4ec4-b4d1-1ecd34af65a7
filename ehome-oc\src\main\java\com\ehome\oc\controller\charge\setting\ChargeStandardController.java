package com.ehome.oc.controller.charge.setting;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.json.ParamsKit;
import com.ehome.oc.constants.ChargeConstants;
import com.ehome.oc.service.charge.ChargeCommonService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;



@Controller
@RequestMapping("/oc/charge/setting/standard")
public class ChargeStandardController extends BaseController{

    private static final String PREFIX = "oc/charge/setting/standard";

    @Autowired
    private ChargeCommonService chargeCommonService;

    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list";
    }

    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select *",
                sql.toFullSql()
        );



        return getDataTable(paginate);
    }

    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        String id = params.getString("id");

        // 使用ChargeCommonService进行参数验证
        AjaxResult validateResult = chargeCommonService.validateId(id, "收费标准ID");
        if (validateResult != null) {
            return validateResult;
        }

        Record fee = chargeCommonService.getChargeStandardDetail(Long.parseLong(id));
        if (fee == null) {
            return chargeCommonService.buildErrorResponse("收费标准不存在");
        }

        // 处理count_info字段，优先从独立表读取，如果没有则从JSON字段解析
        JSONObject countInfo = getCountInfoFromTable(fee.getLong("id"));
        if (countInfo == null && StringUtils.isNotEmpty(fee.getStr("count_info"))) {
            try {
                countInfo = JSONObject.parseObject(fee.getStr("count_info"));
            } catch (Exception e) {
                System.out.println("解析count_info字段失败: " + e.getMessage());
                countInfo = new JSONObject();
            }
        }

        if (countInfo != null) {
            fee.set("count_info", countInfo);
        }

        return AjaxResult.success(null, fee.toMap());
    }

    /**
     * 将数字代码转换为收费类型中文
     */
    private String getChargeTypeName(String chargeTypeCode) {
        return ChargeConstants.getChargeTypeName(Integer.parseInt(chargeTypeCode));
    }

    @GetMapping("/add")
    public String add(String chargeType, String chargeTypeName, ModelMap mmap) {
        mmap.put("chargeType", chargeType);
        mmap.put("chargeTypeName", chargeTypeName);
        return PREFIX + "/add";
    }

    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        Record fee = Db.findFirst("select * from eh_charge_standard where id = ? and is_deleted = 0", id);
        mmap.put("fee", fee.toMap());
        String chargeTypeCode = String.valueOf(fee.getInt("charge_type"));
        mmap.put("chargeType", getChargeTypeName(chargeTypeCode));
        mmap.put("chargeTypeCode", chargeTypeCode);
        return PREFIX + "/edit";
    }

    @Log(title = "新增收费标准", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave() {
        JSONObject params = getParams();

        // 处理表单数据
        Map<String, Object> processedData = processFormData(params);
        Record baseData = (Record) processedData.get("baseData");
        baseData.set("is_current", 1); // 设置为当前版本
        JSONObject countInfoData = (JSONObject) processedData.get("countInfoData");

        // 设置创建和更新信息
        chargeCommonService.setCreateAndUpdateInfo(baseData, getSysUser());

        // 保存数据
        Long id = saveChargeStandardWithCountInfo(baseData, countInfoData);
        Db.update("update eh_charge_standard set parent_id = ? where id = ?",id,id);

        return AjaxResult.success();
    }

    @Log(title = "修改收费标准", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave() {
        JSONObject params = getParams();
        Long originalId = params.getLong("id");

        if (originalId == null || originalId <= 0) {
            return AjaxResult.error("收费标准ID不能为空");
        }

        try {
            // 获取原始记录
            Record originalRecord = Db.findFirst("select * from eh_charge_standard where id = ?", originalId);
            if (originalRecord == null) {
                return AjaxResult.error("收费标准不存在");
            }

            // 将原记录设置为非当前版本
            Db.update("update eh_charge_standard set is_current = 0, updated_at = ?, updated_by = ? where id = ?",
                    DateUtils.getTime(), getSysUser().getUserName(), originalId);

            // 处理表单数据
            Map<String, Object> processedData = processFormData(params);
            Record newVersion = (Record) processedData.get("baseData");
            JSONObject countInfoData = (JSONObject) processedData.get("countInfoData");

            // 设置版本信息
            Long parentId = originalRecord.getLong("parent_id");
            if (parentId == null) {
                parentId = originalId; // 如果原记录没有parent_id，则它就是根记录
            }
            newVersion.set("parent_id", parentId);

            // 获取当前版本号，如果为null则默认为1
            Integer currentVersion = originalRecord.getInt("version");
            if (currentVersion == null) {
                currentVersion = 1;
            }
            newVersion.set("version", currentVersion + 1);
            newVersion.set("is_current", 1);
            newVersion.remove("id"); // 移除ID，让数据库自动生成新ID

            // 设置创建和更新信息
            chargeCommonService.setCreateAndUpdateInfo(newVersion, getSysUser());

            // 保存新版本数据
            Long newVersionId = saveChargeStandardWithCountInfo(newVersion, countInfoData, true);

            // 更新所有使用此收费标准的绑定记录，指向新版本
            Db.update("update eh_charge_binding set charge_standard_id = ? where charge_standard_id = ?", newVersionId, originalId);

            return AjaxResult.success("收费标准更新成功，新版本ID：" + newVersionId);

        } catch (Exception e) {
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }

    @Log(title = "删除收费标准", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        // 使用ChargeCommonService进行批量删除
        AjaxResult validateResult = chargeCommonService.validateId(ids, "参数id");
        if (validateResult != null) {
            return validateResult;
        }

        int deleteCount = chargeCommonService.batchDelete("eh_charge_standard", ids, getSysUser());
        if (deleteCount > 0) {
            return success();
        } else {
            return error("删除失败");
        }
    }

    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_charge_standard where is_deleted = 0 and is_current = 1");
        sql.append(getSysUser().getCommunityId(), "and community_id = ?");
        sql.appendLike(params.getString("feeName"), "and name like ?");
        sql.append(params.getString("feeType"), "and charge_type = ?");
        sql.append(params.getString("collectionMethod"), "and period_type = ?");
        sql.append(params.getString("calculationMethod"), "and count_type = ?");
        sql.append(params.getString("isActive"), "and is_active = ?");

        String beginTime = params.getString("beginTime");
        sql.append(beginTime, "and created_at >= ?");
        String endTime = params.getString("endTime");
        sql.append(endTime, "and created_at <= ?");

        sql.append("order by created_at desc");
        return sql;
    }



    /**
     * 处理表单数据，包括参数解析和格式转换
     * @param params 前端传入的参数
     * @return Map包含处理后的Record和countInfoData
     */
    private Map<String, Object> processFormData(JSONObject params) {
        Record fee = new Record();

        // 处理基础字段
        JSONObject baseData = ParamsKit.getJSONObject(params, "base");
        if (!baseData.isEmpty()) {
            fee.setColumns(baseData);
        } else {
            fee.setColumns(params);
        }

        // 处理count_info扩展字段
        JSONObject countInfoData = ParamsKit.getJSONObject(params, "countInfo");
        if (!countInfoData.isEmpty()) {
            fee.set("count_info", countInfoData.toJSONString());
        }

        // 转换前端数据到数据库格式
        convertFormDataToDbFormat(fee);

        Map<String, Object> result = new HashMap<>();
        result.put("baseData", fee);
        result.put("countInfoData", countInfoData);
        return result;
    }

    /**
     * 保存收费标准记录和相关的count_info数据
     * @param record 收费标准记录
     * @param countInfoData count_info数据
     * @return 保存后的记录ID
     */
    private Long saveChargeStandardWithCountInfo(Record record, JSONObject countInfoData) {
        return saveChargeStandardWithCountInfo(record, countInfoData, false);
    }

    /**
     * 保存收费标准记录和相关的count_info数据
     * @param record 收费标准记录
     * @param countInfoData count_info数据
     * @param isUpdate 是否为更新操作（需要先删除旧的count_info记录）
     * @return 保存后的记录ID
     */
    private Long saveChargeStandardWithCountInfo(Record record, JSONObject countInfoData, boolean isUpdate) {
        // 保存主表数据
        Db.save("eh_charge_standard", "id", record);
        Long recordId = record.getLong("id");

        // 保存count_info到独立表
        if (countInfoData != null && !countInfoData.isEmpty()) {
            if (isUpdate) {
                // 更新操作：先删除旧记录再插入新记录
                updateCountInfo(recordId, countInfoData);
            } else {
                // 新增操作：直接插入
                saveCountInfo(recordId, countInfoData);
            }
        }

        return recordId;
    }

    /**
     * 转换前端表单数据到数据库格式
     */
    private void convertFormDataToDbFormat(Record fee) {
        // 处理违约金相关字段
        if (fee.getStr("late_money_after_day") != null) {
            try {
                fee.set("late_money_after_day", Integer.parseInt(fee.getStr("late_money_after_day")));
            } catch (NumberFormatException e) {
                fee.set("late_money_after_day", 0);
            }
        }
        if (fee.getStr("late_money_proportion") != null) {
            try {
                fee.set("late_money_proportion", Double.parseDouble(fee.getStr("late_money_proportion")));
            } catch (NumberFormatException e) {
                fee.set("late_money_proportion", 0.0);
            }
        }
        if (fee.getStr("late_money_desc") != null) {
            fee.set("late_money_desc", fee.getStr("late_money_desc"));
        } else {
            fee.set("late_money_desc", "无");
        }

        // 设置账期相关字段
        if (fee.getStr("accounting_period_day") != null) {
            try {
                fee.set("accounting_period_day", Integer.parseInt(fee.getStr("accounting_period_day")));
            } catch (NumberFormatException e) {
                fee.set("accounting_period_day", 1);
            }
        } else {
            fee.set("accounting_period_day", 1);
        }

        // 处理计算方式字段映射
        if (fee.getStr("calculation_method") != null) {
            try {
                fee.set("count_type", Integer.parseInt(fee.getStr("calculation_method")));
            } catch (NumberFormatException e) {
                fee.set("count_type", 100);
            }
        }

        // 设置默认值（只在没有设置时才设置默认值）
        if (fee.getInt("period_type") == null) {
            fee.set("period_type", 101); // 默认月度收费
        }
        if (fee.getInt("count_type") == null) {
            fee.set("count_type", 100); // 默认单价计量
        }
        if (fee.getInt("accounting_period_type") == null) {
            fee.set("accounting_period_type", 1); // 默认账期类型
        }
    }

    /**
     * 保存计算信息到独立表
     */
    private void saveCountInfo(Long chargeStandardId, JSONObject countInfoData) {
        if (chargeStandardId == null || countInfoData.isEmpty()) {
            return;
        }



        Record countInfo = new Record();
        countInfo.set("charge_standard_id", chargeStandardId);
        countInfo.set("count_type", countInfoData.getIntValue("count_type") != 0 ? countInfoData.getIntValue("count_type") : ChargeConstants.CountType.UNIT_PRICE_MULTIPLY);
        countInfo.set("area_type", countInfoData.getIntValue("area_type") != 0 ? countInfoData.getIntValue("area_type") : ChargeConstants.AreaType.TOTAL_AREA);
        countInfo.set("area_name", countInfoData.getString("area_name") != null ? countInfoData.getString("area_name") : "");

        // 处理周期性收费价格
        if (countInfoData.getString("price") != null && !countInfoData.getString("price").isEmpty()) {
            try {
                double price = Double.parseDouble(countInfoData.getString("price"));
                countInfo.set("price", price);
            } catch (NumberFormatException e) {
                countInfo.set("price", 0);
            }
        } else {
            countInfo.set("price", 0);
        }

        // 处理走表收费价格
        if (countInfoData.getString("meter_price") != null && !countInfoData.getString("meter_price").isEmpty()) {
            try {
                double meterPrice = Double.parseDouble(countInfoData.getString("meter_price"));
                countInfo.set("meter_price", meterPrice);
            } catch (NumberFormatException e) {
                countInfo.set("meter_price", 0);
            }
        } else {
            countInfo.set("meter_price", 0);
        }

        countInfo.set("unit", countInfoData.getIntValue("unit") != 0 ? countInfoData.getIntValue("unit") : ChargeConstants.Unit.YUAN);
        countInfo.set("ladder_type", countInfoData.getIntValue("ladder_type"));
        countInfo.set("ladder_list", countInfoData.getString("ladder_list"));
        countInfo.set("desc", countInfoData.getString("desc") != null ? countInfoData.getString("desc") : "");
        countInfo.set("regular_price", countInfoData.getDoubleValue("regular_price"));

        countInfo.set("created_at", DateUtils.getNowDate());
        countInfo.set("updated_at", DateUtils.getNowDate());

        Db.save("eh_charge_count_info", "id", countInfo);
    }

    /**
     * 更新计算信息到独立表
     */
    private void updateCountInfo(Long chargeStandardId, JSONObject countInfoData) {
        if (chargeStandardId == null || countInfoData.isEmpty()) {
            return;
        }

        // 先删除旧的记录
        Db.update("DELETE FROM eh_charge_count_info WHERE charge_standard_id = ?", chargeStandardId);

        // 重新插入新的记录
        saveCountInfo(chargeStandardId, countInfoData);
    }

    /**
     * 从独立表获取计算信息
     */
    private JSONObject getCountInfoFromTable(Long chargeStandardId) {
        if (chargeStandardId == null) {
            return null;
        }

        Record countInfoRecord = Db.findFirst("SELECT * FROM eh_charge_count_info WHERE charge_standard_id = ?", chargeStandardId);
        if (countInfoRecord == null) {
            return null;
        }

        JSONObject countInfo = new JSONObject();
        countInfo.put("id", countInfoRecord.getLong("id"));
        countInfo.put("count_type", countInfoRecord.getInt("count_type"));
        countInfo.put("area_type", countInfoRecord.getInt("area_type"));
        countInfo.put("area_name", countInfoRecord.getStr("area_name"));

        // 获取周期性收费价格
        Double price = countInfoRecord.getDouble("price");
        if (price != null) {
            countInfo.put("price", price);
        } else {
            countInfo.put("price", 0);
        }

        // 获取走表收费价格
        Double meterPrice = countInfoRecord.getDouble("meter_price");
        if (meterPrice != null) {
            countInfo.put("meter_price", meterPrice);
        } else {
            countInfo.put("meter_price", 0);
        }

        countInfo.put("unit", countInfoRecord.getInt("unit"));
        countInfo.put("ladder_type", countInfoRecord.getInt("ladder_type"));
        countInfo.put("ladder_list", countInfoRecord.getStr("ladder_list"));
        countInfo.put("desc", countInfoRecord.getStr("desc"));
        countInfo.put("regular_price", countInfoRecord.getDouble("regular_price"));

        return countInfo;
    }



}
