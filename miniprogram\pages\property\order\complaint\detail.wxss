.detail-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.detail-content {
  padding: 20rpx;
}

.info-card,
.contact-card,
.attachment-card,
.process-card {
  background: white;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.type-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}

.status-0 {
  background-color: #ff4d4f;
}

.status-1 {
  background-color: #faad14;
}

.status-2 {
  background-color: #52c41a;
}

.time-text {
  font-size: 24rpx;
  color: #999;
}

.card-content {
  padding: 30rpx;
}

.content-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.address-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.address-text {
  font-size: 24rpx;
  color: #666;
}

.card-title {
  padding: 30rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

.contact-info {
  padding: 30rpx;
}

.contact-item {
  display: flex;
  margin-bottom: 20rpx;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-label {
  font-size: 28rpx;
  color: #666;
  width: 120rpx;
}

.contact-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.attachment-grid {
  padding: 30rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.attachment-item {
  width: 200rpx;
  height: 200rpx;
}

.attachment-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.process-content {
  padding: 30rpx;
}

.process-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.process-info {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.process-handler {
  font-size: 24rpx;
  color: #999;
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
}

.complete-btn {
  width: 100%;
}

.feedback-dialog {
  padding: 20rpx;
}
