<view
  class="van-swipe-cell custom-class"
  data-key="cell"
  catchtap="onClick"
  bindtouchstart="startDrag"
  catchtouchmove="{{ catchMove ? 'noop' : '' }}"
  capture-bind:touchmove="onDrag"
  bindtouchend="endDrag"
  bindtouchcancel="endDrag"
>
  <view style="{{ wrapperStyle }}">
    <view wx:if="{{ leftWidth }}" class="van-swipe-cell__left" data-key="left" catch:tap="onClick">
      <slot name="left" />
    </view>
    <slot />
    <view wx:if="{{ rightWidth }}" class="van-swipe-cell__right" data-key="right" catch:tap="onClick">
      <slot name="right" />
    </view>
  </view>
</view>
