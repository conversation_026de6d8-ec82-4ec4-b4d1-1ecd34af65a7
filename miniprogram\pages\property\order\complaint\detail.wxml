<view class="detail-container">
  <view wx:if="{{loading}}" class="loading">
    <van-loading type="spinner" size="24px">加载中...</van-loading>
  </view>

  <!-- 详情内容 -->
  <view wx:else class="detail-content">
    <!-- 基本信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <view class="header-left">
          <text class="type-text">{{detail.type || '投诉建议'}}</text>
          <view class="status-tag status-{{detail.status}}">
            {{detail.statusText || (detail.status == 0 ? '未处理' : detail.status == 1 ? '处理中' : '已完成')}}
          </view>
        </view>
        <view class="header-right">
          <text class="time-text">{{detail.create_time}}</text>
        </view>
      </view>
      
      <view class="card-content">
        <view class="content-text">{{detail.content}}</view>
        
        <view wx:if="{{detail.address}}" class="address-info">
          <van-icon name="location-o" size="14px" color="#999" />
          <text class="address-text">{{detail.address}}</text>
        </view>
      </view>
    </view>

    <!-- 联系信息卡片 -->
    <view class="contact-card">
      <view class="card-title">联系信息</view>
      <view class="contact-info">
        <view class="contact-item">
          <text class="contact-label">姓名：</text>
          <text class="contact-value">{{detail.name || '--'}}</text>
        </view>
        <view class="contact-item">
          <text class="contact-label">电话：</text>
          <text class="contact-value">{{detail.phone || '--'}}</text>
        </view>
      </view>
    </view>

    <!-- 附件信息 -->
    <view wx:if="{{attachments.length > 0}}" class="attachment-card">
      <view class="card-title">相关图片</view>
      <view class="attachment-grid">
        <view wx:for="{{attachments}}" wx:key="id" class="attachment-item">
          <image 
            wx:if="{{item.fileType && item.fileType.indexOf('image/') === 0}}"
            class="attachment-image" 
            src="{{item.url}}" 
            mode="aspectFill"
            bindtap="previewImage"
            data-url="{{item.url}}"
          />
        </view>
      </view>
    </view>

    <!-- 处理结果 -->
    <view wx:if="{{detail.status == 2 && detail.feedback}}" class="process-card">
      <view class="card-title">处理结果</view>
      <view class="process-content">
        <text class="process-text">{{detail.feedback}}</text>
        <view wx:if="{{detail.handler}}" class="process-info">
          <text class="process-handler">处理人：{{detail.handler}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view wx:if="{{detail && (detail.status == 0 || detail.status == 1)}}" class="action-buttons">
    <van-button 
      type="primary" 
      size="large" 
      loading="{{processing}}"
      disabled="{{processing}}"
      bind:click="showCompleteDialog"
      custom-class="complete-btn"
    >
      办理完成
    </van-button>
  </view>

  <!-- 反馈输入对话框 -->
  <van-dialog
    use-slot
    title="处理反馈"
    show="{{showFeedbackDialog}}"
    show-cancel-button
    bind:confirm="confirmComplete"
    bind:cancel="hideFeedbackDialog"
    confirm-button-loading="{{processing}}"
  >
    <view class="feedback-dialog">
      <van-field
        type="textarea"
        placeholder="请输入处理结果和反馈信息"
        value="{{feedback}}"
        bind:change="onFeedbackInput"
        autosize
        maxlength="500"
        show-word-limit
      />
    </view>
  </van-dialog>
</view>
