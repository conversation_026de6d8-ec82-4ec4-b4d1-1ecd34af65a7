// 巡更任务详情页面
import {handlePropertyPageShow} from '../../../utils/pageUtils.js'

const app = getApp()

Page({
  data: {
    title: '巡更任务',
    taskList: [],
    loading: false,
    refreshing: false,
    filterStatus: 'pending', // pending, completed
    date: '', // 查询的日期，为空表示今日
    isToday: true
  },

  onLoad(options) {
    console.log('巡更任务详情页面加载', options)
    
    const date = options.date || ''
    const isToday = !date
    
    this.setData({
      date: date,
      isToday: isToday,
      title: isToday ? '今日任务' : `${date} 任务`
    })

    wx.setNavigationBarTitle({
      title: this.data.title
    })
  },

  onShow() {
    handlePropertyPageShow(this, this.loadTasks)
  },

  // 加载任务数据
  async loadTasks() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      let url = '/api/wx/patrol/getTodayTasks'
      let params = {}

      // 如果不是今日，则查询历史记录
      if (!this.data.isToday) {
        url = '/api/wx/patrol/getRecords'
        params = {
          startDate: this.data.date,
          endDate: this.data.date,
          pageNum: 1,
          pageSize: 100
        }
      }

      const res = await app.request({
        url: url,
        method: 'POST',
        data: params
      })

      if (res.code === 0) {
        const tasks = this.data.isToday ? (res.data.tasks || []) : (res.data.records || [])
        this.setData({
          taskList: tasks
        })
      } else {
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载巡更任务失败:', error)
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      })
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      })
    }
  },

  // 切换筛选状态
  onFilterChange(e) {
    const status = e.currentTarget.dataset.status
    this.setData({ filterStatus: status })
  },

  // 开始巡更
  startPatrol(e) {
    const recordId = e.currentTarget.dataset.id
    const task = this.data.taskList.find(t => t.record_id === recordId)

    if (!task) {
      wx.showToast({
        title: '任务不存在',
        icon: 'none'
      })
      return
    }

    if (task.status === 1) {
      wx.showToast({
        title: '该任务已完成',
        icon: 'none'
      })
      return
    }

    // 跳转到拍照巡更页面
    wx.navigateTo({
      url: `/pages/property/patrol/camera?recordId=${recordId}`,
      fail: (error) => {
        console.error('跳转失败:', error)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  },

  // 查看巡更详情
  viewPatrolDetail(e) {
    const recordId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/property/patrol/detail?recordId=${recordId}`
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ refreshing: true })
    this.loadTasks().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 获取任务状态文本
  getStatusText(status) {
    if (status === 0) return '待巡更'
    if (status === 1) return '已完成'
    if (status === 2) return '已过期'
    return '未知'
  },

  // 获取任务状态样式
  getStatusClass(status) {
    if (status === 0) return 'status-pending'
    if (status === 1) return 'status-completed'
    if (status === 2) return 'status-overdue'
    return 'status-unknown'
  }
})
