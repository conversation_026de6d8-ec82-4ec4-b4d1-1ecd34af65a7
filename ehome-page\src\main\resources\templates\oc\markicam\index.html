<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('Markicam数据管理')" />
    <style>
        .dashboard-card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
            transition: transform 0.2s;
        }
        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .card-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .card-description {
            color: #666;
            margin-bottom: 15px;
        }
        .sync-status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .status-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <!-- 页面标题 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>Markicam数据管理中心</h5>
                        <div class="ibox-tools">
                            <a class="btn btn-primary btn-sm" onclick="refreshStatus()">
                                <i class="fa fa-refresh"></i> 刷新状态
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div id="syncStatus" class="sync-status status-warning">
                            <i class="fa fa-spinner fa-spin"></i> 正在加载同步状态...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能模块卡片 -->
        <div class="row">
            <!-- 配置管理 -->
            <div class="col-lg-3 col-md-6">
                <div class="dashboard-card text-center">
                    <div class="card-icon text-primary">
                        <i class="fa fa-cog"></i>
                    </div>
                    <div class="card-title">配置管理</div>
                    <div class="card-description">管理Markicam API配置信息</div>
                    <a href="javascript:void(0)" onclick="openPage('config')" class="btn btn-primary btn-block">
                        <i class="fa fa-cog"></i> 配置管理
                    </a>
                </div>
            </div>

            <!-- 照片视频管理 -->
            <div class="col-lg-3 col-md-6">
                <div class="dashboard-card text-center">
                    <div class="card-icon text-info">
                        <i class="fa fa-camera"></i>
                    </div>
                    <div class="card-title">照片视频</div>
                    <div class="card-description">查看和管理照片视频数据</div>
                    <a href="javascript:void(0)" onclick="openPage('moment')" class="btn btn-info btn-block">
                        <i class="fa fa-camera"></i> 照片视频
                    </a>
                    <a href="javascript:void(0)" onclick="syncData('moment')" class="btn btn-outline-info btn-sm btn-block" style="margin-top: 5px;">
                        <i class="fa fa-refresh"></i> 同步数据
                    </a>
                </div>
            </div>

            <!-- 团队成员管理 -->
            <div class="col-lg-3 col-md-6">
                <div class="dashboard-card text-center">
                    <div class="card-icon text-success">
                        <i class="fa fa-users"></i>
                    </div>
                    <div class="card-title">团队成员</div>
                    <div class="card-description">查看和管理团队成员信息</div>
                    <a href="javascript:void(0)" onclick="openPage('member')" class="btn btn-success btn-block">
                        <i class="fa fa-users"></i> 团队成员
                    </a>
                    <a href="javascript:void(0)" onclick="syncData('member')" class="btn btn-outline-success btn-sm btn-block" style="margin-top: 5px;">
                        <i class="fa fa-refresh"></i> 同步数据
                    </a>
                </div>
            </div>

            <!-- 违规车辆管理 -->
            <div class="col-lg-3 col-md-6">
                <div class="dashboard-card text-center">
                    <div class="card-icon text-warning">
                        <i class="fa fa-car"></i>
                    </div>
                    <div class="card-title">违规车辆</div>
                    <div class="card-description">查看和管理违规车辆信息</div>
                    <a href="javascript:void(0)" onclick="openPage('illegal')" class="btn btn-warning btn-block">
                        <i class="fa fa-car"></i> 违规车辆
                    </a>
                    <a href="javascript:void(0)" onclick="syncData('illegal')" class="btn btn-outline-warning btn-sm btn-block" style="margin-top: 5px;">
                        <i class="fa fa-refresh"></i> 同步数据
                    </a>
                </div>
            </div>
        </div>

        <!-- 同步日志和统计信息 -->
        <div class="row">
            <div class="col-lg-8">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>最近同步日志</h5>
                        <div class="ibox-tools">
                            <a href="javascript:void(0)" onclick="openPage('log')">查看全部</a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="table-responsive">
                            <table class="table table-striped" id="recentLogs">
                                <thead>
                                    <tr>
                                        <th>同步类型</th>
                                        <th>状态</th>
                                        <th>数量</th>
                                        <th>耗时</th>
                                        <th>时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="5" class="text-center">
                                            <i class="fa fa-spinner fa-spin"></i> 加载中...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>数据统计</h5>
                    </div>
                    <div class="ibox-content">
                        <div id="dataStatistics">
                            <div class="text-center">
                                <i class="fa fa-spinner fa-spin"></i> 加载中...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>快速操作</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-primary" onclick="syncAllData()">
                                <i class="fa fa-refresh"></i> 同步所有数据
                            </button>
                            <button type="button" class="btn btn-info" onclick="testConnection()">
                                <i class="fa fa-plug"></i> 测试连接
                            </button>
                            <button type="button" class="btn btn-warning" onclick="clearOldLogs()">
                                <i class="fa fa-trash"></i> 清理旧日志
                            </button>
                            <button type="button" class="btn btn-success" onclick="exportReport()">
                                <i class="fa fa-download"></i> 导出报表
                            </button>
                            <a class="btn btn-default" href="" onclick="window.location.href = ctx + 'oc/markicam/team'; return false;">
                                <i class="fa fa-users"></i> 团队管理
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/markicam";
        
        $(function() {
            // 页面加载时获取状态信息
            refreshStatus();
            loadRecentLogs();
            loadDataStatistics();
            
            // 定时刷新状态（每分钟）
            setInterval(function() {
                refreshStatus();
                loadRecentLogs();
            }, 60000);
        });

        function openPage(page) {
            var url = prefix + "/" + page;
            window.location.href = url;
        }

        function refreshStatus() {
            $.get(prefix + "/status", function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    var statusHtml = '';
                    var statusClass = 'status-success';
                    
                    if (data.configCount == 0) {
                        statusHtml = '<i class="fa fa-exclamation-triangle"></i> 尚未配置Markicam API信息';
                        statusClass = 'status-error';
                    } else if (data.lastSyncTime) {
                        var lastSync = new Date(data.lastSyncTime);
                        var now = new Date();
                        var diffHours = (now - lastSync) / (1000 * 60 * 60);
                        
                        if (diffHours > 24) {
                            statusHtml = '<i class="fa fa-exclamation-triangle"></i> 超过24小时未同步数据';
                            statusClass = 'status-warning';
                        } else {
                            statusHtml = '<i class="fa fa-check-circle"></i> 系统运行正常，最后同步时间：' + data.lastSyncTime;
                        }
                    } else {
                        statusHtml = '<i class="fa fa-info-circle"></i> 尚未进行数据同步';
                        statusClass = 'status-warning';
                    }
                    
                    $('#syncStatus').removeClass('status-success status-warning status-error').addClass(statusClass).html(statusHtml);
                } else {
                    $('#syncStatus').removeClass('status-success status-warning').addClass('status-error')
                        .html('<i class="fa fa-exclamation-triangle"></i> 获取状态失败');
                }
            }).fail(function() {
                $('#syncStatus').removeClass('status-success status-warning').addClass('status-error')
                    .html('<i class="fa fa-exclamation-triangle"></i> 网络连接失败');
            });
        }

        function loadRecentLogs() {
            $.get(prefix + "/log/recent", function(result) {
                if (result.code == 0) {
                    var logs = result.data;
                    var tbody = '';
                    
                    if (logs && logs.length > 0) {
                        for (var i = 0; i < logs.length; i++) {
                            var log = logs[i];
                            var statusBadge = '';
                            if (log.sync_status == 1) {
                                statusBadge = '<span class="badge badge-success">成功</span>';
                            } else if (log.sync_status == 0) {
                                statusBadge = '<span class="badge badge-danger">失败</span>';
                            } else {
                                statusBadge = '<span class="badge badge-warning">进行中</span>';
                            }
                            
                            tbody += '<tr>';
                            tbody += '<td>' + log.sync_type + '</td>';
                            tbody += '<td>' + statusBadge + '</td>';
                            tbody += '<td>' + (log.sync_count || 0) + '</td>';
                            tbody += '<td>' + (log.duration || 0) + 's</td>';
                            tbody += '<td>' + (log.start_time || '-') + '</td>';
                            tbody += '</tr>';
                        }
                    } else {
                        tbody = '<tr><td colspan="5" class="text-center">暂无同步日志</td></tr>';
                    }
                    
                    $('#recentLogs tbody').html(tbody);
                }
            });
        }

        function loadDataStatistics() {
            $.get(prefix + "/statistics", function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    var html = '';
                    html += '<div class="row text-center">';
                    html += '<div class="col-xs-6"><h3 class="text-primary">' + (data.momentCount || 0) + '</h3><p>照片视频</p></div>';
                    html += '<div class="col-xs-6"><h3 class="text-success">' + (data.memberCount || 0) + '</h3><p>团队成员</p></div>';
                    html += '</div>';
                    html += '<div class="row text-center">';
                    html += '<div class="col-xs-6"><h3 class="text-warning">' + (data.illegalCount || 0) + '</h3><p>违规车辆</p></div>';
                    html += '<div class="col-xs-6"><h3 class="text-info">' + (data.teamCount || 0) + '</h3><p>团队数量</p></div>';
                    html += '</div>';
                    
                    $('#dataStatistics').html(html);
                }
            });
        }

        function syncData(type) {
            var typeMap = {
                'moment': '照片视频',
                'member': '团队成员',
                'illegal': '违规车辆'
            };
            
            $.modal.confirm("确定要同步" + typeMap[type] + "数据吗？", function() {
                $.modal.loading("正在同步数据...");
                $.post(prefix + "/sync/" + type, {}, function(result) {
                    $.modal.closeLoading();
                    if (result.code == 0) {
                        $.modal.alertSuccess("同步成功");
                        refreshStatus();
                        loadRecentLogs();
                        loadDataStatistics();
                    } else {
                        $.modal.alertError("同步失败：" + result.msg);
                    }
                }).fail(function() {
                    $.modal.closeLoading();
                    $.modal.alertError("同步失败");
                });
            });
        }

        function syncAllData() {
            $.modal.confirm("确定要同步所有数据吗？这可能需要较长时间。", function() {
                $.modal.loading("正在同步所有数据...");
                $.post(prefix + "/sync/all", {}, function(result) {
                    $.modal.closeLoading();
                    if (result.code == 0) {
                        $.modal.alertSuccess("同步完成");
                        refreshStatus();
                        loadRecentLogs();
                        loadDataStatistics();
                    } else {
                        $.modal.alertError("同步失败：" + result.msg);
                    }
                }).fail(function() {
                    $.modal.closeLoading();
                    $.modal.alertError("同步失败");
                });
            });
        }

        function testConnection() {
            $.modal.loading("正在测试连接...");
            $.post(prefix + "/test/connection", {}, function(result) {
                $.modal.closeLoading();
                if (result.code == 0) {
                    $.modal.alertSuccess("连接测试成功");
                } else {
                    $.modal.alertError("连接测试失败：" + result.msg);
                }
            }).fail(function() {
                $.modal.closeLoading();
                $.modal.alertError("连接测试失败");
            });
        }

        function clearOldLogs() {
            $.modal.confirm("确定要清理30天前的旧日志吗？", function() {
                $.post(prefix + "/log/clearOld", {}, function(result) {
                    if (result.code == 0) {
                        $.modal.alertSuccess("清理完成");
                        loadRecentLogs();
                    } else {
                        $.modal.alertError("清理失败：" + result.msg);
                    }
                });
            });
        }

        function exportReport() {
            window.location.href = prefix + "/report/export";
        }
    </script>
</body>
</html>
