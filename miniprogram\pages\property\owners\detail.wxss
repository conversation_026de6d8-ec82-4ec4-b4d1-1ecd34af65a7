/* 房屋详情页面样式 */
.container {
  height: 100vh;
  width: 100%;
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.loading-container,
.error-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scroll-container {
  flex: 1;
  height: 100%;
}

.detail-content {
  padding: 24rpx;
  box-sizing: border-box;
}

/* 信息卡片 */
.info-card {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.card-title {
  margin-left: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

/* 房屋信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0;
}

.info-item {
  padding: 32rpx;
  border-right: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
}

.info-item:nth-child(even) {
  border-right: none;
}

.info-item:nth-last-child(-n+2) {
  border-bottom: none;
}

.info-label {
  font-size: 24rpx;
  color: #969799;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  color: #323233;
  font-weight: 500;
}

/* 业主简洁列表 */
.owner-simple-list {
  padding: 15rpx 32rpx 32rpx;
}

.owner-simple-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.owner-simple-item:last-child {
  margin-bottom: 0;
}

.owner-simple-item:active {
  background-color: #e6f7ff;
  border-color: #1989fa;
  transform: scale(0.98);
}

.owner-simple-info {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.owner-left {
  display: flex;
  gap: 8rpx;
}

.owner-simple-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.owner-simple-role {
  font-size: 22rpx;
  color: #fff;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  text-align: center;
  font-weight: 500;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 32rpx;
}

.owner-simple-mobile {
  font-size: 28rpx;
  color: #1989fa;
  font-weight: 500;
  letter-spacing: 1rpx;
}

/* 车位列表 */
.parking-list {
  padding: 0 32rpx 32rpx;
}

.parking-item {
  padding: 32rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.parking-item:last-child {
  border-bottom: none;
}

.parking-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.parking-no {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.parking-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.parking-type {
  font-size: 24rpx;
  color: #969799;
}

.parking-fee {
  display: flex;
  align-items: center;
}

.fee-label {
  font-size: 24rpx;
  color: #969799;
}

.fee-value {
  font-size: 28rpx;
  color: #ff7875;
  font-weight: 600;
}

/* 车辆列表 */
.vehicle-list {
  padding: 0 32rpx 32rpx;
}

.vehicle-item {
  padding: 32rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.vehicle-item:last-child {
  border-bottom: none;
}

.vehicle-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.vehicle-plate {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
}

.vehicle-owner {
  font-size: 24rpx;
  color: #969799;
}

.vehicle-detail {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}

/* 详情行 */
.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 24rpx;
  color: #969799;
  min-width: 140rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #646566;
  flex: 1;
}

/* 空状态 */
.empty-state {
  padding: 80rpx 20rpx;
  text-align: center;
}
