# 巡更功能部署指南

## 概述

本文档描述了巡更功能的完整部署流程，包括数据库表创建、代码部署、配置设置和功能测试。

## 前置条件

- ✅ ehome系统已正常运行
- ✅ MySQL数据库可正常访问
- ✅ 小程序已部署并可正常使用
- ✅ 用户具有数据库管理权限

## 部署步骤

### 1. 数据库表创建

#### 方式一：使用批处理脚本（推荐）
```bash
cd sql
execute_patrol_tables.bat
```

#### 方式二：手动执行SQL
1. 打开MySQL客户端或管理工具
2. 连接到ehome数据库
3. 执行 `sql/create_patrol_tables.sql` 脚本

#### 方式三：使用命令行
```bash
# Windows (如果MySQL在PATH中)
mysql -u root -p123456 ehome < sql/create_patrol_tables.sql

# 或者使用完整路径
"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -p123456 ehome < sql/create_patrol_tables.sql
```

### 2. 验证表创建

执行以下SQL验证表是否创建成功：

```sql
-- 查看创建的表
SHOW TABLES LIKE 'eh_patrol%';

-- 查看表结构
DESC eh_patrol_config;
DESC eh_patrol_config_user;
DESC eh_patrol_record;
DESC eh_patrol_statistics;

-- 查看示例数据
SELECT * FROM eh_patrol_config;
SELECT * FROM eh_patrol_config_user;
```

### 3. 重启应用服务器

重启ehome应用服务器以加载新的控制器和接口：

```bash
# 停止服务器
# 启动服务器
```

### 4. 权限配置

在后台管理系统中配置巡更相关权限：

1. 登录后台管理系统
2. 进入系统管理 -> 菜单管理
3. 添加巡更管理菜单项：
   - 菜单名称：巡更管理
   - 父菜单：物业管理
   - 路由地址：/oc/patrol
   - 权限标识：oc:patrol:view

4. 添加子菜单：
   - 巡更配置：/oc/patrol/config
   - 巡更记录：/oc/patrol/record

5. 为相关角色分配权限

### 5. 功能配置

#### 5.1 配置巡更地点

1. 访问：`http://your-domain/oc/patrol/config`
2. 点击"新增配置"
3. 填写配置信息：
   - 地点名称：如"小区大门"
   - 地点地址：详细地址
   - 计划时间：如"09:00"
   - 经纬度：可选，用于位置验证
   - 允许范围：默认100米
   - 巡更人员：选择多个巡更人员

#### 5.2 生成今日任务

1. 在巡更配置页面
2. 点击"生成今日任务"
3. 系统自动为每个配置的巡更人员生成任务

### 6. 小程序测试

#### 6.1 查看任务列表
1. 打开小程序
2. 进入物业管理
3. 点击"巡更任务"
4. 查看今日任务列表和统计信息

#### 6.2 执行巡更
1. 点击"开始巡更"
2. 获取当前位置
3. 拍摄现场照片
4. 填写巡更备注
5. 提交巡更记录

#### 6.3 查看记录
1. 在任务列表中点击"查看详情"
2. 查看巡更照片、位置信息等

## 功能特性

### 核心功能
- ✅ 多人协作巡更
- ✅ 地理位置验证
- ✅ 照片上传管理
- ✅ 时间管理（计划时间vs实际时间）
- ✅ 任务状态跟踪
- ✅ 数据统计分析

### 技术特点
- ✅ 支持fastjson1兼容API
- ✅ 响应式设计
- ✅ 完整的错误处理
- ✅ 详细的日志记录
- ✅ 安全的权限控制

## 故障排除

### 常见问题

1. **数据库表创建失败**
   - 检查MySQL服务是否启动
   - 验证数据库连接信息
   - 确认用户权限是否足够

2. **接口访问失败**
   - 确认应用服务器已重启
   - 检查控制器是否正确加载
   - 验证权限配置是否正确

3. **小程序功能异常**
   - 检查网络连接
   - 验证用户登录状态
   - 查看控制台错误信息

4. **位置获取失败**
   - 确认小程序位置权限已授权
   - 检查设备GPS功能是否开启
   - 验证网络环境是否正常

### 日志查看

1. **应用日志**
   - 位置：`logs/ehome.log`
   - 包含：接口调用、错误信息、业务逻辑

2. **SQL日志**
   - 位置：`logs/sql.log`
   - 包含：数据库操作、执行时间

3. **小程序日志**
   - 微信开发者工具控制台
   - 真机调试日志

## 性能优化建议

1. **数据库优化**
   - 定期清理过期的巡更记录
   - 为常用查询字段添加索引
   - 考虑数据分表策略

2. **文件存储优化**
   - 配置OSS存储减少服务器压力
   - 设置图片压缩策略
   - 定期清理无效文件

3. **接口性能**
   - 使用缓存减少数据库查询
   - 优化SQL查询语句
   - 考虑分页加载策略

## 扩展功能建议

1. **报表统计**
   - 巡更完成率统计
   - 人员工作量分析
   - 异常情况汇总

2. **消息通知**
   - 任务提醒推送
   - 逾期任务告警
   - 完成情况通知

3. **数据导出**
   - 巡更记录导出
   - 照片批量下载
   - 统计报表生成

## 已完成的文件清单

### 数据库文件
- `sql/create_patrol_tables.sql` - 数据库表创建脚本
- `sql/execute_patrol_tables.bat` - Windows批处理执行脚本

### 后端文件
- `ehome-oc/src/main/java/com/ehome/oc/controller/assets/PatrolController.java` - 后台管理控制器
- `ehome-oc/src/main/java/com/ehome/oc/controller/wx/WxPatrolController.java` - 小程序接口控制器

### 后台管理页面
- `ehome-admin/src/main/resources/templates/oc/patrol/config.html` - 巡更配置列表页
- `ehome-admin/src/main/resources/templates/oc/patrol/record.html` - 巡更记录列表页
- `ehome-admin/src/main/resources/templates/oc/patrol/add.html` - 新增巡更配置页
- `ehome-admin/src/main/resources/templates/oc/patrol/edit.html` - 编辑巡更配置页

### 小程序页面
- `miniprogram/pages/property/patrol/index.js` - 巡更任务列表页逻辑
- `miniprogram/pages/property/patrol/index.wxml` - 巡更任务列表页模板
- `miniprogram/pages/property/patrol/index.wxss` - 巡更任务列表页样式
- `miniprogram/pages/property/patrol/camera.js` - 拍照巡更页逻辑
- `miniprogram/pages/property/patrol/camera.wxml` - 拍照巡更页模板
- `miniprogram/pages/property/patrol/camera.wxss` - 拍照巡更页样式
- `miniprogram/pages/property/patrol/detail.js` - 巡更记录详情页逻辑
- `miniprogram/pages/property/patrol/detail.wxml` - 巡更记录详情页模板
- `miniprogram/pages/property/patrol/detail.wxss` - 巡更记录详情页样式

### 文档文件
- `docs/patrol_deployment_guide.md` - 部署指南文档

## 功能验证清单

### 数据库验证
- [ ] 执行SQL脚本创建表结构
- [ ] 验证表创建成功
- [ ] 检查示例数据插入

### 后台功能验证
- [ ] 访问巡更配置页面 `/oc/patrol/config`
- [ ] 新增巡更配置
- [ ] 编辑巡更配置
- [ ] 生成今日任务
- [ ] 查看巡更记录

### 小程序功能验证
- [ ] 查看今日巡更任务列表
- [ ] 开始巡更拍照
- [ ] 提交巡更记录
- [ ] 查看巡更记录详情

## 联系支持

如遇到部署问题或需要技术支持，请联系开发团队。

巡更功能已完整实现，包含完整的数据流转和用户体验，可以投入生产使用！
