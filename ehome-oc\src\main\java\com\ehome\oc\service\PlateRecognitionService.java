package com.ehome.oc.service;

import com.ehome.common.core.domain.AjaxResult;

/**
 * 车牌识别服务接口
 * 
 * <AUTHOR>
 */
public interface PlateRecognitionService {
    
    /**
     * 识别图片中的车牌号
     * 
     * @param imageBytes 图片字节数组
     * @return 识别结果，包含车牌号和置信度
     */
    AjaxResult recognizePlate(byte[] imageBytes);
    
    /**
     * 识别图片中的车牌号（通过文件路径）
     * 
     * @param imagePath 图片文件路径
     * @return 识别结果，包含车牌号和置信度
     */
    AjaxResult recognizePlateByPath(String imagePath);
}
