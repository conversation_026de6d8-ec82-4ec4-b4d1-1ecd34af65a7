// 物业端报修详情页面
import {handlePropertyPageShow} from '../../../utils/pageUtils.js'

const app = getApp()

Page({
  data: {
    searchKeyword: '',
    repairList: [],
    filteredList: [],
    loading: false,
    refreshing: false,
    hasMore: true,
    pageNum: 1,
    pageSize: 10,
    
    // 弹窗相关
    showActionDialog: false,
    showFeedbackDialog: false,
    currentAction: '',
    currentRepairId: '',
    feedback: '',
    processing: false,
    
    // 状态映射
    statusMap: {
      0: { text: '待处理', color: '#ff976a' },
      1: { text: '处理中', color: '#1989fa' },
      2: { text: '已完成', color: '#07c160' }
    }
  },

  onLoad(options) {
    console.log('报修详情页面加载')
  },

  onShow() {
    handlePropertyPageShow(this, this.loadRepairList)
  },

  onPullDownRefresh() {
    this.loadRepairList(true).finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadRepairList()
    }
  },

  // 加载报修列表
  async loadRepairList(refresh = false) {
    if (refresh) {
      this.setData({
        pageNum: 1,
        repairList: [],
        refreshing: true
      })
    }

    if (this.data.loading) return
    this.setData({ loading: true })

    try {
      const res = await app.request({
        url: '/api/wx/property/repair/list',
        method: 'POST',
        data: {
          pageNum: this.data.pageNum,
          pageSize: this.data.pageSize,
          status: '0' // 只查询待处理的报修
        }
      })

      if (res.code === 0) {
        const newList = res.data.list || []
        const updatedList = refresh ? newList : [...this.data.repairList, ...newList]
        
        this.setData({
          repairList: updatedList,
          filteredList: updatedList,
          hasMore: newList.length === this.data.pageSize,
          pageNum: this.data.pageNum + 1
        })
      } else {
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载报修列表失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false, refreshing: false })
    }
  },

  // 搜索输入
  onSearchInput(event) {
    const keyword = event.detail
    this.setData({ searchKeyword: keyword })
    this.filterRepairList(keyword)
  },

  // 搜索
  onSearch(event) {
    const keyword = event.detail
    this.filterRepairList(keyword)
  },

  // 清空搜索
  onSearchClear() {
    this.setData({ searchKeyword: '' })
    this.filterRepairList('')
  },

  // 过滤报修列表
  filterRepairList(keyword) {
    if (!keyword) {
      this.setData({ filteredList: this.data.repairList })
      return
    }

    const filtered = this.data.repairList.filter(item => {
      return item.id.includes(keyword) ||
             (item.name && item.name.includes(keyword)) ||
             (item.content && item.content.includes(keyword))
    })

    this.setData({ filteredList: filtered })
  },

  // 操作按钮点击
  onActionClick(event) {
    const { action, id } = event.currentTarget.dataset
    this.setData({
      currentAction: action,
      currentRepairId: id,
      showActionDialog: true
    })
  },

  // 确认操作
  onConfirmAction() {
    const { currentAction } = this.data
    
    this.setData({ showActionDialog: false })
    
    if (currentAction === 'complete') {
      // 办结需要填写反馈
      this.setData({ showFeedbackDialog: true })
    } else {
      this.updateRepairStatus()
    }
  },

  // 取消操作
  onCancelAction() {
    this.setData({ 
      showActionDialog: false,
      currentAction: '',
      currentRepairId: ''
    })
  },

  // 更新报修状态
  async updateRepairStatus() {
    const { currentAction, currentRepairId, feedback } = this.data
    
    let status = '0'
    switch (currentAction) {
      case 'transfer':
        status = '1' // 转单 -> 处理中
        break
      case 'pause':
        status = '0' // 暂停 -> 待处理
        break
      case 'return':
        status = '0' // 退单 -> 待处理
        break
      case 'complete':
        status = '2' // 办结 -> 已完成
        break
    }

    this.setData({ processing: true })
    
    try {
      const res = await app.request({
        url: '/api/wx/property/repair/updateStatus',
        method: 'POST',
        data: {
          id: currentRepairId,
          status: status,
          feedback: feedback
        }
      })

      if (res.code === 0) {
        wx.showToast({
          title: '操作成功',
          icon: 'success'
        })
        
        this.setData({
          showFeedbackDialog: false,
          feedback: '',
          currentAction: '',
          currentRepairId: ''
        })
        
        // 重新加载列表
        this.loadRepairList(true)
      } else {
        throw new Error(res.msg || '操作失败')
      }
    } catch (error) {
      console.error('更新状态失败:', error)
      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      })
    } finally {
      this.setData({ processing: false })
    }
  },

  // 反馈输入
  onFeedbackInput(event) {
    this.setData({ feedback: event.detail })
  },

  // 取消反馈
  onCancelFeedback() {
    this.setData({
      showFeedbackDialog: false,
      feedback: '',
      currentAction: '',
      currentRepairId: ''
    })
  },

  // 确认反馈
  onConfirmFeedback() {
    if (!this.data.feedback.trim()) {
      wx.showToast({
        title: '请填写处理反馈',
        icon: 'none'
      })
      return
    }
    this.updateRepairStatus()
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return ''
    const date = new Date(timeStr)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
  },

  // 获取操作文本
  getActionText(action) {
    const actionMap = {
      transfer: '转单',
      pause: '暂停', 
      return: '退单',
      complete: '办结'
    }
    return actionMap[action] || ''
  }
})
