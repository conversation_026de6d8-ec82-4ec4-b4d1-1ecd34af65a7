<view class="repair-container">
  <!-- Tab切换 -->
  <van-tabs active="{{activeTab}}" bind:change="onTabChange" sticky>
    <van-tab wx:for="{{tabs}}" wx:key="key" title="{{item.title}}">
      <view class="tab-content">
        <view wx:if="{{loading && repairList.length === 0}}" class="loading">
          <van-loading type="spinner" size="24px">加载中...</van-loading>
        </view>

        <view wx:elif="{{repairList.length === 0}}" class="empty">
          <van-empty description="暂无数据" />
        </view>

        <view wx:else class="repair-list">
          <view wx:for="{{repairList}}" wx:key="id" class="repair-item" bindtap="viewDetail" data-id="{{item.id}}">
            <view class="item-header">
              <view class="item-type">{{item.type || '报修'}}</view>
              <view class="item-status status-{{item.status}}">{{item.statusText}}</view>
            </view>
            <view class="item-content">{{item.contentSummary}}</view>
            <view class="item-info">
              <view class="info-left">
                <text class="info-user">{{item.name}}</text>
                <text class="info-phone">{{item.phone}}</text>
              </view>
              <view class="info-right">
                <text class="info-time">{{item.create_time}}</text>
              </view>
            </view>
            <view wx:if="{{item.address}}" class="item-address">
              <van-icon name="location-o" size="12px" />
              <text>{{item.address}}</text>
            </view>
          </view>

          <view wx:if="{{loading}}" class="loading-more">
            <van-loading type="spinner" size="16px">加载更多...</van-loading>
          </view>

          <view wx:if="{{!hasMore && repairList.length > 0}}" class="no-more">
            没有更多数据了
          </view>
        </view>
      </view>
    </van-tab>
  </van-tabs>
</view>
