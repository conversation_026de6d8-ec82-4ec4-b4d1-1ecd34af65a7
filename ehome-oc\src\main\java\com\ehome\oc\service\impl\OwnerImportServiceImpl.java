package com.ehome.oc.service.impl;

import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.oc.domain.OwnerImport;
import com.ehome.oc.service.IHouseInfoService;
import com.ehome.oc.service.IOwnerImportService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业主导入服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class OwnerImportServiceImpl implements IOwnerImportService {
    
    private static final Logger logger = LoggerFactory.getLogger(OwnerImportServiceImpl.class);
    
    @Autowired
    private IHouseInfoService houseInfoService;
    
    @Override
    public String importOwner(List<OwnerImport> ownerList, Boolean isUpdateSupport, String operName, String communityId, String pmsId) {
        if (StringUtils.isNull(ownerList) || ownerList.size() == 0) {
            throw new RuntimeException("导入业主数据不能为空！");
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String loginName = operName;

        // 手机号格式验证已移除，支持更灵活的导入

        for (OwnerImport owner : ownerList) {
            try {
                // 验证必填字段
                if (StringUtils.isEmpty(owner.getOwnerName())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、业主姓名不能为空");
                    continue;
                }

                // 验证房屋信息（如果提供了房屋信息）
                String houseId = null;
                if (StringUtils.isNotEmpty(owner.getRoom()) || StringUtils.isNotEmpty(owner.getBuilding()) || StringUtils.isNotEmpty(owner.getUnit())) {
                    // 如果提供了房屋信息，则房号、楼栋、单元都必须填写
                    if (StringUtils.isEmpty(owner.getRoom()) || StringUtils.isEmpty(owner.getBuilding()) || StringUtils.isEmpty(owner.getUnit())) {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、业主 " + owner.getOwnerName() + " 房屋信息不完整（房号、楼栋、单元必须同时填写）");
                        continue;
                    }

                    // 查找房屋是否存在（包含楼层匹配）
                    Record houseRecord = null;
                    if (StringUtils.isNotEmpty(owner.getFloor())) {
                        // 如果提供了楼层信息，精确匹配楼层
                        houseRecord = Db.findFirst(
                            "SELECT h.house_id FROM eh_house_info h " +
                            "LEFT JOIN eh_building b ON h.building_id = b.building_id " +
                            "LEFT JOIN eh_unit u ON h.unit_id = u.unit_id " +
                            "WHERE h.room = ? AND b.name = ? AND u.name = ? AND h.floor = ? AND h.community_id = ?",
                            owner.getRoom(), owner.getBuilding(), owner.getUnit(), owner.getFloor(), communityId
                        );
                    } else {
                        // 如果没有提供楼层信息，不匹配楼层
                        houseRecord = Db.findFirst(
                            "SELECT h.house_id FROM eh_house_info h " +
                            "LEFT JOIN eh_building b ON h.building_id = b.building_id " +
                            "LEFT JOIN eh_unit u ON h.unit_id = u.unit_id " +
                            "WHERE h.room = ? AND b.name = ? AND u.name = ? AND h.community_id = ?",
                            owner.getRoom(), owner.getBuilding(), owner.getUnit(), communityId
                        );
                    }

                    if (houseRecord == null) {
                        failureNum++;
                        String houseInfo = owner.getBuilding() + "/" + owner.getUnit() + "/" + owner.getRoom();
                        if (StringUtils.isNotEmpty(owner.getFloor())) {
                            houseInfo += "/" + owner.getFloor() + "层";
                        }
                        failureMsg.append("<br/>" + failureNum + "、业主 " + owner.getOwnerName() + " 对应的房屋不存在：" + houseInfo);
                        continue;
                    }
                    houseId = houseRecord.getStr("house_id");
                }

                // 手机号格式验证已移除，支持更灵活的导入

                // 检查业主是否已存在（根据姓名+房屋信息判断）
                Record existingOwner = null;
                String existingOwnerId = null;

                if (houseId != null) {
                    // 如果有房屋信息，检查该房屋是否已有同名业主
                    Record existingRel = Db.findFirst(
                        "SELECT r.owner_id, o.owner_name FROM eh_house_owner_rel r " +
                        "LEFT JOIN eh_owner o ON r.owner_id = o.owner_id " +
                        "WHERE r.house_id = ? AND o.owner_name = ? AND o.community_id = ?",
                        houseId, owner.getOwnerName(), communityId
                    );

                    if (existingRel != null) {
                        existingOwnerId = existingRel.getStr("owner_id");
                        existingOwner = Db.findFirst("SELECT * FROM eh_owner WHERE owner_id = ?", existingOwnerId);
                    }
                } else {
                    // 如果没有房屋信息，通过手机号查找（如果手机号不为空）
                    if (StringUtils.isNotEmpty(owner.getMobile())&&owner.getMobile().length()==11) {
                        existingOwner = Db.findFirst("SELECT * FROM eh_owner WHERE mobile = ? AND community_id = ?",
                            owner.getMobile(), communityId);
                        if (existingOwner != null) {
                            existingOwnerId = existingOwner.getStr("owner_id");
                        }
                    }
                }

                if (existingOwner != null) {
                    // 业主已存在的情况
                    if (houseId != null) {
                        // 有房屋信息且业主已存在，说明该房屋已有同名业主，跳过
                        successNum++;
                        String houseInfo = "（" + owner.getBuilding() + "/" + owner.getUnit() + "/" + owner.getRoom();
                        if (StringUtils.isNotEmpty(owner.getFloor())) {
                            houseInfo += "/" + owner.getFloor() + "层";
                        }
                        houseInfo += "）";
                        successMsg.append("<br/>" + successNum + "、业主 " + owner.getOwnerName() + houseInfo + " 已存在，跳过");
                        continue;
                    } else {
                        // 无房屋信息但业主已存在，根据更新选项处理
                        if (isUpdateSupport) {
                            // 更新现有业主信息
                            Record updateRecord = new Record();
                            updateRecord.set("owner_name", owner.getOwnerName());
                            updateRecord.set("mobile", owner.getMobile());
                            updateRecord.set("id_card", owner.getIdCard());
                            updateRecord.set("gender", owner.getGender());
                            updateRecord.set("address", owner.getAddress());
                            updateRecord.set("remark", owner.getRemark());
                            updateRecord.set("update_time", now);
                            updateRecord.set("updater", loginName);

                            Db.update("eh_owner", "owner_id", updateRecord.set("owner_id", existingOwnerId));
                            successNum++;
                            successMsg.append("<br/>" + successNum + "、业主 " + owner.getOwnerName() + " 更新成功");
                        } else {
                            successNum++;
                            successMsg.append("<br/>" + successNum + "、业主 " + owner.getOwnerName() + " 已存在，跳过");
                        }
                        continue;
                    }
                } else {
                    // 新增业主
                    Record ownerRecord = new Record();
                    ownerRecord.set("owner_name", owner.getOwnerName());
                    ownerRecord.set("mobile", owner.getMobile());
                    ownerRecord.set("id_card", owner.getIdCard());
                    ownerRecord.set("gender", owner.getGender());
                    ownerRecord.set("address", owner.getAddress());
                    ownerRecord.set("community_id", communityId);
                    ownerRecord.set("pms_id", pmsId);
                    ownerRecord.set("role", 1); // 默认为业主
                    ownerRecord.set("is_live", 0); // 默认未入住
                    ownerRecord.set("house_count", 0);
                    ownerRecord.set("member_count", 0);
                    ownerRecord.set("parking_count", 0);
                    ownerRecord.set("car_count", 0);
                    ownerRecord.set("complaint_count", 0);
                    ownerRecord.set("repair_count", 0);
                    ownerRecord.set("arrears_count", 0);
                    ownerRecord.set("create_time", now);
                    ownerRecord.set("update_time", now);
                    ownerRecord.set("creator", loginName);
                    ownerRecord.set("updater", loginName);
                    ownerRecord.set("remark", StringUtils.isNotEmpty(owner.getRemark()) ? owner.getRemark() : "Excel导入");

                    Db.save("eh_owner", "owner_id", ownerRecord);
                    existingOwnerId = ownerRecord.getStr("owner_id");
                }

                // 如果提供了房屋信息，创建业主房屋关系
                if (houseId != null && existingOwnerId != null) {
                    // 检查是否已经存在关系（这里应该不会存在，因为前面已经检查过了）
                    Record existingRelCheck = Db.findFirst(
                        "SELECT * FROM eh_house_owner_rel WHERE house_id = ? AND owner_id = ?",
                        houseId, existingOwnerId
                    );

                    if (existingRelCheck == null) {
                        Record rel = new Record();
                        rel.set("rel_id", Seq.getId());
                        rel.set("house_id", houseId);
                        rel.set("owner_id", existingOwnerId);
                        rel.set("community_id", communityId);
                        rel.set("rel_type", 1); // 业主
                        rel.set("is_default", 1); // 设为默认房屋
                        rel.set("check_status", 1); // 已审核
                        rel.set("create_time", now);
                        rel.set("create_by", loginName);

                        Db.save("eh_house_owner_rel", "rel_id", rel);

                        // 更新房屋的业主信息
                        houseInfoService.updateHouseOwnerInfo(houseId);
                        // 更新业主的房屋信息
                        houseInfoService.updateOwnerHouseInfo(existingOwnerId);

                        successNum++;
                        String houseInfo = "（" + owner.getBuilding() + "/" + owner.getUnit() + "/" + owner.getRoom();
                        if (StringUtils.isNotEmpty(owner.getFloor())) {
                            houseInfo += "/" + owner.getFloor() + "层";
                        }
                        houseInfo += "）";
                        successMsg.append("<br/>" + successNum + "、业主 " + owner.getOwnerName() + houseInfo + " 导入成功");
                    }
                } else if (houseId == null && existingOwnerId != null) {
                    // 仅业主信息，无房屋绑定
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、业主 " + owner.getOwnerName() + " 导入成功");
                }

            } catch (Exception e) {
                failureNum++;
                String name = owner.getOwnerName();
                String msg = "<br/>" + failureNum + "、业主 " + name + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                logger.error(msg, e);
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
