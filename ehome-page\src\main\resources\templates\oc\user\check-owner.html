<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('业主管理')" />
</head>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="config-form">
					<div class="select-list">
						<ul>
							<li>
								业主姓名：<input type="text" name="owner_name" onkeypress="if(event.keyCode==13) $.table.search()"/>
							</li>
							<li>
								手机号码：<input type="text" name="mobile" onkeypress="if(event.keyCode==13) $.table.search()"/>
							</li>
							<li>
								房屋信息：<input type="text" name="house_info" placeholder="楼栋/房号" onkeypress="if(event.keyCode==13) $.table.search()"/>
							</li>
							<li>
								审核结果：<select name="checkStatus" onchange="$.table.search()">
									<option value="">全部</option>
									<option value="0" selected>待审核</option>
									<option value="1">审核通过</option>
									<option value="2">审核不通过</option>
								</select>
							</li>
							<li>
								关系类型：<select name="rel_type" onchange="$.table.search()">
									<option value="">全部</option>
									<option value="1">业主</option>
									<option value="2">家庭成员</option>
									<option value="3">租户</option>
								</select>
							</li>
							<li class="select-time">
								<label>申请时间：</label>
								<input type="text" class="time-input" id="startTime" placeholder="开始时间" name="beginTime"/>
								<span>-</span>
								<input type="text" class="time-input" id="endTime" placeholder="结束时间" name="endTime"/>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
			
	        <div class="btn-group-sm" id="toolbar" role="group">
		        <a class="btn btn-success multiple disabled" onclick="batchApprove()">
		            <i class="fa fa-check"></i> 批量通过
		        </a>
		        <a class="btn btn-danger multiple disabled" onclick="batchReject()">
		            <i class="fa fa-times"></i> 批量不通过
		        </a>
	        </div>
	        <div class="col-sm-12 select-table table-striped">
	            <table id="bootstrap-table"></table>
	        </div>
	    </div>
	</div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/owner";

        $(function() {
            var options = {
                url: prefix + "/checkList",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "业主",
				escape: false,
				layer:{
					area:['800px','550px'],
					offset: '70px'
				},
                columns: [{
                    checkbox: true
                },
                {
                    field: 'rel_id',
                    title: '关系ID',
                    visible: false
                },
                {
                    field: 'owner_name',
                    title: '业主姓名'
                },
                {
                    field: 'mobile',
                    title: '手机号'
                },
                {
                    field: 'id_card',
                    title: '身份证号',
                    formatter: function(value, row, index) {
                        if (!value) return '-';
                        // 身份证号脱敏显示
                        return value.substring(0, 6) + '****' + value.substring(value.length - 4);
                    }
                },
				{
					field: 'gender',
					title: '性别',
					formatter: function(value, row, index) {
						if (value == 'M') return '男';
						else if (value == 'F') return '女';
						return '-';
					}
				},
                {
                    field: 'house_info',
                    title: '申请房屋',
                    formatter: function(value, row, index) {
                        var houseInfo = '';
                        if (row.combina_name) {
                            houseInfo = row.combina_name;
                            if (row.room) {
                                houseInfo += '/' + row.room;
                            }
                            if (row.use_area) {
                                houseInfo += ' (' + row.use_area + '㎡)';
                            }
                        }
                        return houseInfo || '-';
                    }
                },
				{
					field: 'rel_type',
					title: '关系类型',
					formatter: function(value, row, index) {
						var roleDatas = [
							{ dictValue: "1", dictLabel: "业主" },
							{ dictValue: "2", dictLabel: "家庭成员" },
							{ dictValue: "3", dictLabel: "租户" }
						];
						return $.table.selectDictLabel(roleDatas, value) || '-';
					}
				},
                {
                    field: 'apply_time',
                    title: '申请时间',
                    formatter: function(value, row, index) {
                        return value ? value.substring(0, 16) : '-';
                    }
                },
                {
                    field: 'approve_info',
                    title: '审批信息',
                    formatter: function(value, row, index) {
                        if (value){
							try {
								var info = JSON.parse(value);
								var result = '';
								if (info.approve_time) {
									result += '时间: ' + info.approve_time.substring(0, 16) + '<br>';
								}
								if (info.approve_by) {
									result += '审批人: ' + info.approve_by;
								}
								if (info.reject_reason) {
									result += '<br>原因: ' + info.reject_reason;
								}
								return result || '-';
							} catch (e) {
								console.error("解析审批信息失败:", e);
								return '-';
							}
						}else{
							return '-';
						}
                    }
                },
                {
                    field: 'apply_remark',
                    title: '申请备注',
                    align: 'center',
					formatter: function(value, row, index) {
						return value == null ? '-' : (value.length > 10 ? value.substring(0, 10) + '...' : value);
					}
                },
                {
                    field: 'file_id',
                    title: '证件图片',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (!value) {
                            return '<span class="text-muted">未上传</span>';
                        }
                        return '<a href="javascript:void(0)" onclick="viewAttachment(\'' + value + '\')" class="btn btn-info btn-xs">' +
                               '<i class="fa fa-image"></i> 查看证件</a>';
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
						if(row.check_status==0){
							actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="singleApprove(\'' + row.rel_id + '\')"><i class="fa fa-check"></i> 通过</a> ');
							actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="singleReject(\'' + row.rel_id + '\')"><i class="fa fa-times"></i> 不通过</a>');
						}
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 批量审批通过
        function batchApprove() {
            var rows = $.table.selectColumns("rel_id");
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            $.modal.confirm("确认要批量审批通过选中的 " + rows.length + " 条记录吗？", function() {
                var data = {
                    "relIds": rows.join(","),
                    "approveType": "approve"
                };
                $.operate.submit(prefix + "/approve", "post", "json", data);
            });
        }

        // 批量审批不通过
        function batchReject() {
            var rows = $.table.selectColumns("rel_id");
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            layer.prompt({
                title: '请输入不通过原因',
                formType: 2,
                area: ['400px', '200px']
            }, function(value, index) {
                if (!value || value.trim() == '') {
                    $.modal.alertWarning("请输入不通过原因");
                    return;
                }
                layer.close(index);

                var data = {
                    "relIds": rows.join(","),
                    "approveType": "reject",
                    "rejectReason": value.trim()
                };
                $.operate.submit(prefix + "/approve", "post", "json", data);
            });
        }

        // 单个审批通过
        function singleApprove(relId) {
            $.modal.confirm("确认要审批通过该记录吗？", function() {
                var data = {
                    "relIds": relId,
                    "approveType": "approve"
                };
                $.operate.submit(prefix + "/approve", "post", "json", data);
            });
        }

        // 单个审批不通过
        function singleReject(relId) {
            layer.prompt({
                title: '请输入不通过原因',
                formType: 2,
                area: ['400px', '200px']
            }, function(value, index) {
                if (!value || value.trim() == '') {
                    $.modal.alertWarning("请输入不通过原因");
                    return;
                }
                layer.close(index);

                var data = {
                    "relIds": relId,
                    "approveType": "reject",
                    "rejectReason": value.trim()
                };
                $.operate.submit(prefix + "/approve", "post", "json", data);
            });
        }

        // 查看详情
        function viewDetail(ownerId) {
            var url = prefix + "/detail/" + ownerId;
            $.modal.openTab("业主详情", url);
        }

        // 查看附件
        function viewAttachment(fileId) {
            if (!fileId) {
                $.modal.alertWarning("文件ID不能为空");
                return;
            }

            // 构建文件访问URL - 使用PC端的下载接口
            var fileUrl = ctx + "common/download/" + fileId;

            // 在新窗口中打开图片
            window.open(fileUrl, '_blank');
        }
    </script>
</body>
</html>