// 房屋详情页面
const app = getApp()

Page({
  data: {
    loading: true,
    error: '',
    houseId: '',
    houseDetail: null
  },

  onLoad(options) {
    console.log('[房屋详情] 页面加载', options)
    
    const { houseId } = options
    if (!houseId) {
      this.setData({
        loading: false,
        error: '房屋ID不能为空'
      })
      return
    }

    this.setData({ houseId })
    this.loadHouseDetail()
  },

  /**
   * 加载房屋详情
   */
  async loadHouseDetail() {
    try {
      this.setData({ loading: true, error: '' })

      const response = await app.request({
        url: `/api/wx/owner/houseDetail/${this.data.houseId}`,
        method: 'GET'
      })

      if (response.code === 0) {
        this.setData({
          houseDetail: response.data,
          loading: false
        })
      } else {
        throw new Error(response.msg || '获取房屋详情失败')
      }
    } catch (error) {
      console.error('[房屋详情] 加载失败:', error)
      this.setData({
        loading: false,
        error: error.message || '加载失败'
      })
    }
  },

  /**
   * 获取房屋类型文本
   */
  getHouseTypeText(type) {
    const typeMap = {
      '1': '住宅',
      '2': '公寓',
      '3': '商铺',
      '4': '办公'
    }
    return typeMap[type] || '未知类型'
  },

  /**
   * 获取关系类型文本
   */
  getRelTypeText(type) {
    const typeMap = {
      '1': '业主',
      '2': '家庭成员',
      '3': '租户',
      '4': '其他'
    }
    return typeMap[String(type)] || '其他'
  },

  /**
   * 获取角色文本
   */
  getRoleText(role) {
    const roleMap = {
      '1': '户主',
      '2': '家庭成员',
      '3': '租户',
      '4': '其他'
    }
    return roleMap[role] || '其他'
  },

  /**
   * 获取车位类型文本
   */
  getParkingTypeText(type) {
    const typeMap = {
      '1': '地上车位',
      '2': '地下车位',
      '3': '机械车位',
      '4': '临时车位'
    }
    return typeMap[type] || '普通车位'
  },

  /**
   * 获取车辆类型文本
   */
  getVehicleTypeText(type) {
    const typeMap = {
      '1': '小型汽车',
      '2': '大型汽车',
      '3': '摩托车',
      '4': '电动车',
      '5': '其他'
    }
    return typeMap[type] || '未知类型'
  },

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    if (!timeStr) return '未知时间'

    try {
      const date = new Date(timeStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    } catch (error) {
      return timeStr
    }
  },

  /**
   * 拨打业主电话
   */
  callOwner(event) {
    const mobile = event.currentTarget.dataset.mobile
    if (!mobile) {
      wx.showToast({
        title: '电话号码为空',
        icon: 'none'
      })
      return
    }

    wx.makePhoneCall({
      phoneNumber: mobile,
      fail: (error) => {
        console.error('[房屋详情] 拨打电话失败:', error)
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadHouseDetail().finally(() => {
      wx.stopPullDownRefresh()
    })
  }
})
