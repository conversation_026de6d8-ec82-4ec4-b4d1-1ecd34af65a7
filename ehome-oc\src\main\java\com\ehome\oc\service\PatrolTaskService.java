package com.ehome.oc.service;

import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.LoggerUtils;
import com.ehome.common.utils.uuid.Seq;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 巡更任务业务服务
 */
@Service
public class PatrolTaskService {
    
    private static final String LOGGER_NAME = "patrol-task-service";
    private final Logger logger = LoggerUtils.getLog(LOGGER_NAME);

    /**
     * 为指定社区生成今日巡更任务
     * 
     * @param communityId 社区ID
     * @return 生成的任务数量
     * @throws Exception 生成任务过程中的异常
     */
    public int generateTodayTasks(String communityId) throws Exception {
        logger.info("开始为社区 {} 生成今日巡更任务", communityId);
        
        String today = DateUtils.dateTimeNow("yyyy-MM-dd");
        String currentTime = DateUtils.getTime();

        // 检查今日是否已生成任务
        int existCount = Db.queryInt(
            "SELECT COUNT(*) FROM eh_patrol_record WHERE community_id = ? AND patrol_date = ?",
            communityId, today);

        if (existCount > 0) {
            logger.warn("社区 {} 今日任务已生成，任务数量：{}", communityId, existCount);
            throw new RuntimeException("今日任务已生成，请勿重复操作");
        }

        // 获取启用的巡更配置
        List<Record> configs = Db.find(
            "SELECT * FROM eh_patrol_config WHERE community_id = ? AND is_active = 1",
            communityId);

        logger.info("社区 {} 找到 {} 个启用的巡更配置", communityId, configs.size());

        int taskCount = 0;

        for (Record config : configs) {
            String configId = config.getStr("config_id");
            String locationName = config.getStr("location_name");

            // 获取该配置的巡更人员
            List<Record> users = Db.find(
                "SELECT * FROM eh_patrol_config_user WHERE config_id = ?", configId);

            logger.debug("配置 {} ({}) 有 {} 个巡更人员", configId, locationName, users.size());

            // 为每个人员生成巡更任务
            for (Record user : users) {
                String recordId = Seq.getId();

                Db.update("INSERT INTO eh_patrol_record " +
                    "(record_id, config_id, community_id, location_name, location_address, " +
                    "planned_time, patrol_user_id, patrol_user_name, patrol_date, " +
                    "status, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0, ?)",
                    recordId,
                    configId,
                    communityId,
                    config.getStr("location_name"),
                    config.getStr("location_address"),
                    config.getStr("planned_time"),
                    user.getStr("user_id"),
                    user.getStr("user_name"),
                    today,
                    currentTime);

                taskCount++;
                
                logger.debug("为用户 {} 生成巡更任务，地点：{}，时间：{}", 
                    user.getStr("user_name"), locationName, config.getStr("planned_time"));
            }
        }

        logger.info("社区 {} 成功生成 {} 个巡更任务", communityId, taskCount);
        return taskCount;
    }

    /**
     * 获取指定社区今日已生成的任务数量
     * 
     * @param communityId 社区ID
     * @return 今日任务数量
     */
    public int getTodayTaskCount(String communityId) {
        String today = DateUtils.dateTimeNow("yyyy-MM-dd");
        return Db.queryInt(
            "SELECT COUNT(*) FROM eh_patrol_record WHERE community_id = ? AND patrol_date = ?",
            communityId, today);
    }

    /**
     * 检查指定社区今日是否已生成任务
     * 
     * @param communityId 社区ID
     * @return 是否已生成任务
     */
    public boolean isTodayTasksGenerated(String communityId) {
        return getTodayTaskCount(communityId) > 0;
    }
}
