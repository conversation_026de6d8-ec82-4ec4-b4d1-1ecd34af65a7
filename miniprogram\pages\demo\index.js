// demo/index.js
const { getSystemInfoSyncCompat } = require('../../utils/systemInfoCompat')
const app = getApp()

Page({
  data: {
    statusBarHeight: 0,
    showLoginDialog: false,
    showPrivacyDialog: false
  },

  onLoad() {
    // 获取状态栏高度
    const systemInfo = getSystemInfoSyncCompat()
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    })

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '睦邻共治'
    })
  },

  onShow() {
    // 设置tabBar（使用公共工具函数）
    const { initTabBar } = require('../../utils/pageUtils.js')
    initTabBar('1') // demo页面默认为业主用户
  },

  // 显示登录提醒弹窗
  showLoginTip() {
    this.setData({
      showLoginDialog: true
    })
  },

  // 显示演示小区提示
  showDemoTip() {
    wx.showModal({
      title: '演示小区',
      content: '这是睦邻共治演示小区，您可以体验各项功能。如需使用完整服务，请登录您的真实小区账号。',
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  // 确认登录 - 显示隐私保护弹窗
  confirmLogin() {
    this.setData({
      showLoginDialog: false,
      showPrivacyDialog: true
    })
  },

  // 显示隐私保护弹窗
  showPrivacyDialog() {
    this.setData({
      showLoginDialog: false,
      showPrivacyDialog: true
    })
  },

  // 同意隐私保护
  agreePrivacy() {
    this.setData({ showPrivacyDialog: false })
    wx.navigateTo({
      url: '/pages/login/index'
    })
  },

  // 拒绝隐私保护
  rejectPrivacy() {
    this.setData({ showPrivacyDialog: false })
    wx.showToast({
      title: '已取消登录',
      icon: 'none'
    })
  },

  // 打开隐私保护指引
  openPrivacyGuide() {
    wx.navigateTo({
      url: '/pages/about/privacy'
    })
  },

  // 取消登录
  cancelLogin() {
    this.setData({
      showLoginDialog: false
    })
  },

  // 直接跳转到登录页面
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/index'
    })
  },

  // 服务电话功能（无需登录）
  goServiceTel() {
    wx.navigateTo({
      url: '/pages/serviceTel/index'
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '睦邻共治，智慧物业服务',
      path: '/pages/demo/index',
      imageUrl: '/static/images/share-bg.png'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '睦邻共治，智慧物业服务',
      query: '',
      imageUrl: '/static/images/share-bg.png'
    }
  }
})
