<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('房屋信息管理')" />
    <style>
        .container-div {
            background: #f0f2f5;
        }
        .fixed-table-toolbar {
            display: none;
        }
        .ibox-tools{
            bottom: 5px;
        }
        .ibox {
            margin-bottom: 10px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,.1);
        }
        .ibox-title {
            padding: 15px;
            border-bottom: 1px solid #eee;
            background-color: #f8f8f8;
            border-radius: 4px 4px 0 0;
        }
        .ibox-content {
            padding: 20px;
            border-style: none;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .control-label {
            padding-top: 7px;
            margin-bottom: 0;
            text-align: right;
        }
        .form-control-static {
            padding-top: 7px;
            margin-bottom: 0;
            min-height: 34px;
        }
        .table-responsive {
            margin-top: 10px;
        }
        #bootstrap-table th {
            background-color: #f8f8f8;
            font-weight: bold;
        }
        #bootstrap-table td {
            vertical-align: middle;
        }
        .badge {
            padding: 4px 8px;
            border-radius: 3px;
        }
        .badge-primary {
            background-color: #1890ff;
        }
        .badge-default {
            background-color: #d9d9d9;
            color: #666;
        }
        .btn-xs {
            padding: 2px 8px;
            font-size: 12px;
            margin: 0 2px;
        }
        .modal-body {
            padding: 20px;
        }
        .input-group {
            margin-bottom: 15px;
        }

        /* 固定底部按钮样式 */
        .fixed-bottom-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #fff;
            border-top: 1px solid #ddd;
            padding: 15px 0;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .fixed-bottom-buttons .btn {
            margin: 0 10px;
            min-width: 100px;
        }

        /* 为页面内容添加底部间距，避免被固定按钮遮挡 */
        body {
            padding-bottom: 80px;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <input type="hidden" id="houseId" th:value="${houseId}"/>
        <div class="row" style="margin-top: 10px;margin-left: -30px;margin-right: -30px;">
            <div class="col-sm-12">
                <!-- 房屋信息展示区域 -->
                <div class="ibox" style="margin-bottom: 10px;">
                    <div class="ibox-title">
                        <h5>房屋信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row" id="houseInfo">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">楼栋/单元：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="buildingUnitInfo"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">房间号：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="roomInfo"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">楼层：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="floorInfo"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">建筑面积：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="totalAreaInfo"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">室内面积：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="useAreaInfo"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">房屋类型：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="houseTypeInfo"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">房屋状态：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="houseStatusInfo">--</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">创建时间：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="createTimeInfo">--</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">备注：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="remarkInfo">--</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 住户列表区域 -->
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>住户列表</h5>

                    </div>
                    <div class="ibox-content">
                        <div class="table-responsive">
                            <table id="bootstrap-table" data-mobile-responsive="true"></table>
                        </div>
                    </div>
                </div>

                <!--车辆列表区域-->
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>车辆列表</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="table-responsive">
                            <table id="vehicle-table" data-mobile-responsive="true"></table>
                        </div>
                    </div>
                </div>
                <!--车位列表区域-->
                <div class="ibox" style="margin-bottom: 100px;">
                    <div class="ibox-title">
                        <h5>车位列表</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="table-responsive">
                            <table id="parking-table" data-mobile-responsive="true"></table>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- 固定底部按钮 -->
    <div class="fixed-bottom-buttons">
        <div class="row">
            <div class="col-sm-12 text-center">
                <button type="button" class="btn btn-default" onclick="pageBack()">
                    <i class="fa fa-reply"></i> 返回
                </button>
                <button type="button" class="btn btn-primary" onclick="editHouse()">
                    <i class="fa fa-edit"></i> 修改
                </button>
                <button type="button" class="btn btn-info" onclick="houseBindings()">
                    <i class="fa fa-link"></i> 绑定管理
                </button>
            </div>
        </div>
    </div>





    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/house";
        var bindingPrefix = ctx + "oc/binding";
        var houseId = $("#houseId").val();
        var relTypeDatas = [
            { dictValue: "1", dictLabel: "业主" },
            { dictValue: "2", dictLabel: "家庭成员" },
            { dictValue: "3", dictLabel: "租户" }
        ];
        var checkStatusDatas = [
            { dictValue: "0", dictLabel: "未审核" },
            { dictValue: "1", dictLabel: "已审核" },
            { dictValue: "2", dictLabel: "审核不通过" }
        ];
        var statusDatas = [
            { dictValue: "2002", dictLabel: "未销售" },
            { dictValue: "2001", dictLabel: "已入住" },
            { dictValue: "2003", dictLabel: "已交房" },
            { dictValue: "2005", dictLabel: "已装修" },
            { dictValue: "2004", dictLabel: "未入住" },
            { dictValue: "2009", dictLabel: "装修中" }
        ];
        var houseTypeDatas = [
            { dictValue: "1", dictLabel: "住宅" },
            { dictValue: "2", dictLabel: "商铺" }
        ];

        var vehicleTypeDatas = [
            { dictValue: "1", dictLabel: "业主车辆" },
            { dictValue: "2", dictLabel: "公共车辆" }
        ];

        var parkingTypeDatas = [
            { dictValue: "1", dictLabel: "私人车位" },
            { dictValue: "2", dictLabel: "子母车位" }
        ];

        var parkingStatusDatas = [
            { dictValue: "1", dictLabel: "出售" },
            { dictValue: "2", dictLabel: "出租" },
            { dictValue: "3", dictLabel: "自用" }
        ];
        
        $(function() {
            // 加载房屋信息
            loadHouseInfo();

            // 初始化住户列表
            initOwnerTable();
            // 初始化车辆列表
            initVehicleTable();
            // 初始化车位列表
            initParkingTable();
        });
        
        // 加载房屋信息
        function loadHouseInfo() {
            $.ajax({
                url: prefix + "/detail",
                type: "post",
                data: { house_id: houseId },
                success: function(res) {
                    if (res.code == 0) {
                        var house = res.data;
                        $("#buildingUnitInfo").text(house.buildingName + "/" + house.unitName);
                        $("#roomInfo").text(house.room);
                        $("#floorInfo").text(house.floor);
                        $("#totalAreaInfo").text(house.total_area + "㎡");
                        $("#useAreaInfo").text(house.use_area + "㎡");

                        // 使用字典数据显示房屋类型
                        $("#houseTypeInfo").html($.table.selectDictLabel(houseTypeDatas, house.house_type) || "-");

                        // 使用字典数据显示房屋状态
                        $("#houseStatusInfo").html($.table.selectDictLabel(statusDatas, house.house_status) || "-");

                        $("#createTimeInfo").text(house.create_time);
                        $("#remarkInfo").text(house.remark || "-");
                    } else {
                        $.modal.alertError("加载房屋信息失败：" + res.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("加载房屋信息失败");
                }
            });
        }
        
        // 初始化住户列表
        function initOwnerTable() {
            var options = {
                url: bindingPrefix + "/house-owner/list",
                queryParams: { houseId: houseId },
                modalName: "住户",
                striped: true,
                bordered: true,
                columns: [{
                    checkbox: true,
                    width: 40
                },
                {
                    field: 'owner_name',
                    title: '住户姓名',
                    align: 'center',
                    width: 100,
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },
                {
                    field: 'mobile',
                    title: '联系电话',
                    align: 'center',
                    width: 120,
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },
                {
                    field: 'check_status',
                    title: '审核状态',
                    align: 'center',
                    width: 80,
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(checkStatusDatas, value) || '-';
                    }
                },
                {
                    field: 'rel_type',
                    title: '人员角色',
                    align: 'center',
                    width: 100,
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(relTypeDatas, value) || '-';
                    }
                },
                {
                    field: 'house_info',
                    title: '已绑定房屋',
                    width:120
                },
                {
                    field: 'remark',
                    title: '备注',
                    align: 'center',
                    width: 100,
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },            
                {
                    title: '操作',
                    align: 'center',
                    width: 200,
                    formatter: function(value, row, index) {
                        var actions = [];
                        if (row.check_status == '1') { // 已审核通过
                            actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="unbindOwner(\'' + row.rel_id + '\')"><i class="fa fa-unlink"></i> 解绑</a> ');
                            if (row.is_default != 1) {
                                actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="setDefault(\'' + row.rel_id + '\',\'' + row.owner_id + '\')"><i class="fa fa-check"></i> 设为默认</a>');
                            }
                        } else if (row.check_status == '0') { // 待审核
                            actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="approveOwner(\'' + row.rel_id + '\')"><i class="fa fa-check"></i> 审核通过</a> ');
                            actions.push('<a class="btn btn-warning btn-xs" href="javascript:void(0)" onclick="rejectOwner(\'' + row.rel_id + '\')"><i class="fa fa-times"></i> 审核不通过</a>');
                        } else if (row.check_status == '2') { // 审核不通过
                            actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="approveOwner(\'' + row.rel_id + '\')"><i class="fa fa-check"></i> 审核通过</a> ');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        }
        

        
        // 解绑住户
        function unbindOwner(relId) {
            $.modal.confirm("确定解绑该住户吗？", function() {
                $.ajax({
                    url: bindingPrefix + "/house-owner/remove",
                    type: "post",
                    data: { relId: relId },
                    success: function(res) {
                        if (res.code == 0) {
                            $.modal.msgSuccess("解绑成功");
                            $.table.refresh("bootstrap-table");
                        } else {
                            $.modal.alertError("解绑失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("解绑失败");
                    }
                });
            });
        }
        
        // 设置默认住户
        function setDefault(relId, ownerId) {
            $.modal.confirm("确定将该住户设为默认住户吗？", function() {
                $.ajax({
                    url: bindingPrefix + "/house-owner/setDefault",
                    type: "post",
                    data: { relId: relId, houseId: houseId, ownerId: ownerId},
                    success: function(res) {
                        if (res.code == 0) {
                            $.modal.msgSuccess("设置成功");
                            $.table.refresh("bootstrap-table");
                        } else {
                            $.modal.alertError("设置失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("设置失败");
                    }
                });
            });
        }

        // 审核通过住户
        function approveOwner(relId) {
            $.modal.confirm("确定审核通过该住户吗？", function() {
                $.ajax({
                    url: bindingPrefix + "/house-owner/approve",
                    type: "post",
                    data: { relId: relId, checkStatus: '1' },
                    success: function(res) {
                        if (res.code == 0) {
                            $.modal.msgSuccess("审核通过成功");
                            $.table.refresh("bootstrap-table");
                        } else {
                            $.modal.alertError("审核失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("审核失败");
                    }
                });
            });
        }

        // 审核不通过住户
        function rejectOwner(relId) {
            $.modal.confirm("确定审核不通过该住户吗？", function() {
                $.ajax({
                    url: bindingPrefix + "/house-owner/approve",
                    type: "post",
                    data: { relId: relId, checkStatus: '2' },
                    success: function(res) {
                        if (res.code == 0) {
                            $.modal.msgSuccess("审核不通过成功");
                            $.table.refresh("bootstrap-table");
                        } else {
                            $.modal.alertError("审核失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("审核失败");
                    }
                });
            });
        }

        // ================= 车辆相关 =================
        // 初始化车辆列表
        function initVehicleTable() {
            var options = {
                id: "vehicle-table",
                url: bindingPrefix + "/house-vehicle/list",
                queryParams: { houseId: houseId },
                modalName: "车辆",
                striped: true,
                bordered: true,
                showToolbar: false,
                search: false,
                showSearch: false,
                showRefresh: false,
                showColumns: false,
                columns: [
                    { checkbox: true, width: 40 },
                    { field: 'plate_no', title: '车牌号', align: 'center', width: 120 },
                    { field: 'vehicle_type', title: '车辆类型', align: 'center', width: 100, formatter: function(value) { return $.table.selectDictLabel(vehicleTypeDatas, value) || '-'; } },
                    { field: 'owner_real_name', title: '车主姓名', align: 'center', width: 100, formatter: function(value) { return value || '-'; } },
                    { field: 'parking_space', title: '绑定车位', align: 'center', width: 120, formatter: function(value) { return value || '-'; } },
                    { field: 'remark', title: '备注', align: 'center', width: 100 },
                    { title: '操作', align: 'center', width: 100, formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="unbindVehicle(\'' + row.rel_id + '\')"><i class="fa fa-unlink"></i> 解绑</a> ');
                        return actions.join('');
                    }}
                ]
            };
            $.table.init(options);
        }



        // 解绑车辆
        function unbindVehicle(relId) {
            $.modal.confirm("确定解绑该车辆吗？", function() {
                $.ajax({
                    url: bindingPrefix + "/house-vehicle/remove",
                    type: "post",
                    data: { relId: relId },
                    success: function(res) {
                        if (res.code == 0) {
                            $.modal.msgSuccess("解绑成功");
                            $.table.refresh("vehicle-table");
                        } else {
                            $.modal.alertError("解绑失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("解绑失败");
                    }
                });
            });
        }

        // ================= 车位相关 =================
        // 初始化车位列表
        function initParkingTable() {
            var options = {
                id: "parking-table",
                url: bindingPrefix + "/house-parking/list",
                queryParams: { houseId: houseId },
                modalName: "车位",
                striped: true,
                bordered: true,
                showToolbar: false,
                search: false,
                showSearch: false,
                showRefresh: false,
                showColumns: false,
                columns: [
                    { checkbox: true, width: 40 },
                    { field: 'parking_no', title: '车位号', align: 'center', width: 100 },
                    { field: 'parking_type', title: '车位类型', align: 'center', width: 100, formatter: function(value) { return $.table.selectDictLabel(parkingTypeDatas, value) || '-'; } },
                    { field: 'parking_status', title: '车位状态', align: 'center', width: 100, formatter: function(value) { return $.table.selectDictLabel(parkingStatusDatas, value) || '-'; } },
                    { field: 'parking_area', title: '面积(㎡)', align: 'center', width: 80, formatter: function(value) { return value ? value + '㎡' : '-'; } },
                    { field: 'plate_no', title: '绑定车牌号', align: 'center', width: 150, formatter: function(value) { return value || '-'; } },
                    { field: 'remark', title: '备注', align: 'center', width: 100 },
                    { title: '操作', align: 'center', width: 100, formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="unbindParking(\'' + row.rel_id + '\')"><i class="fa fa-unlink"></i> 解绑</a> ');
                        return actions.join('');
                    }}
                ]
            };
            $.table.init(options);
        }

        // 解绑车位
        function unbindParking(relId) {
            $.modal.confirm("确定解绑该车位吗？", function() {
                $.ajax({
                    url: bindingPrefix + "/house-parking/remove",
                    type: "post",
                    data: { relId: relId },
                    success: function(res) {
                        if (res.code == 0) {
                            $.modal.msgSuccess("解绑成功");
                            $.table.refresh("parking-table");
                        } else {
                            $.modal.alertError("解绑失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("解绑失败");
                    }
                });
            });
        }

        // 返回页面
        function pageBack() {
            $.modal.close();
        }

        // 修改房屋
        function editHouse() {
            var url = prefix + "/edit/" + houseId;
            $.modal.open("修改房屋", url);
        }

        // 房屋绑定管理
        function houseBindings() {
            var url = ctx + "oc/binding/house/manage/" + houseId;
            $.modal.popupRight("房屋绑定管理", url);
        }
    </script>
</body>
</html> 