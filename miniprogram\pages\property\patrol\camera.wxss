/* 拍照巡更页面样式 */
.patrol-camera-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  color: #999;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
}

/* 主要内容 */
.main-content {
  padding: 20rpx;
}

/* 任务信息卡片 */
.task-info-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.location-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.planned-time {
  font-size: 26rpx;
  color: #667eea;
  background: #f0f3ff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.location-address {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 位置信息卡片 */
.location-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.location-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.location-status.valid {
  background: #d4edda;
  color: #155724;
}

.location-status.invalid {
  background: #f8d7da;
  color: #721c24;
}

/* 位置加载状态 */
.location-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

.location-loading text {
  margin-left: 16rpx;
}

/* 位置错误状态 */
.location-error {
  text-align: center;
  padding: 40rpx 0;
}

.error-text {
  color: #f5222d;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  display: block;
}

.retry-btn {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

/* 位置信息 */
.location-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.location-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}

.location-item .label {
  color: #666;
  margin-right: 16rpx;
  min-width: 140rpx;
}

.location-item .value {
  color: #333;
  font-weight: 500;
}

.location-item .value.warning {
  color: #f5222d;
}

/* 位置空状态 */
.location-empty {
  text-align: center;
  padding: 40rpx 0;
}

.get-location-btn {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

/* 拍照区域 */
.photo-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.photo-count {
  font-size: 26rpx;
  color: #667eea;
  background: #f0f3ff;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
}

/* 照片网格 */
.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.photo-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.photo-image {
  width: 100%;
  height: 100%;
}

.photo-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-add {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #d9d9d9;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.add-text {
  font-size: 24rpx;
  margin-top: 8rpx;
}

/* 照片提示 */
.photo-tips {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

/* 备注区域 */
.remark-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.remark-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  margin-top: 16rpx;
  box-sizing: border-box;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 底部提交按钮 */
.submit-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn.enabled {
  background: #667eea;
  color: white;
}

.submit-btn.disabled {
  background: #f5f5f5;
  color: #ccc;
}
