package com.ehome.oc.task;


import com.ehome.oc.service.CCBJobService;
import com.ehome.oc.service.MarkicamSyncService;
import com.ehome.oc.service.PatrolTaskService;
import com.ehome.oc.task.ChargeBillTaskService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("ocTask")
public class OcTask {

    private static final Logger logger = LoggerFactory.getLogger(OcTask.class);

    @Autowired
    private MarkicamSyncService markicamService;

    @Autowired
    private PatrolTaskService patrolTaskService;

    @Autowired
    private ChargeBillTaskService chargeBillTaskService;

    /**
     * 定时同步所有小区的Markicam数据
     */
    public void synMarkicamData() {
        logger.info("开始定时同步所有小区的Markicam数据");

        try {
            // 获取所有启用的配置
            List<Record> configs = Db.find(
                "SELECT community_id FROM eh_markicam_config WHERE is_enabled = 1 AND is_deleted = 0"
            );

            if (configs == null || configs.isEmpty()) {
                logger.warn("未找到启用的Markicam配置，跳过同步");
                return;
            }

            logger.info("找到{}个启用的Markicam配置", configs.size());

            int successCount = 0;
            int failCount = 0;

            for (Record config : configs) {
                String communityId = config.getStr("community_id");
                try {
                    logger.info("开始同步小区 {} 的Markicam数据", communityId);
                    markicamService.syncMomentData(communityId);
                    successCount++;
                    logger.info("小区 {} Markicam数据同步成功", communityId);
                } catch (Exception e) {
                    failCount++;
                    logger.error("小区 {} Markicam数据同步失败: {}", communityId, e.getMessage(), e);
                }
            }

            logger.info("定时同步Markicam数据完成，总计: {}, 成功: {}, 失败: {}",
                configs.size(), successCount, failCount);

        } catch (Exception e) {
            logger.error("定时同步Markicam数据失败", e);
        }
    }

    public void synCardTransDetailList(){
        CCBJobService ccbJobService = new CCBJobService();
        ccbJobService.syncBalance();
        ccbJobService.synCardTransDetailList();
    }

    /**
     * 定时为所有小区生成今日巡更任务
     */
    public void generateAllCommunityPatrolTasks() {
        logger.info("开始定时为所有小区生成今日巡更任务");

        try {
            // 获取所有启用的小区
            List<Record> communities = Db.find("SELECT oc_id as community_id, oc_name as community_name FROM eh_community WHERE oc_state = 0");

            if (communities == null || communities.isEmpty()) {
                logger.warn("未找到启用的小区，跳过巡更任务生成");
                return;
            }

            logger.info("找到{}个启用的小区", communities.size());

            int successCount = 0;
            int failCount = 0;
            int totalTaskCount = 0;

            for (Record community : communities) {
                String communityId = community.getStr("community_id");
                String communityName = community.getStr("community_name");

                try {
                    logger.info("开始为小区 {} ({}) 生成巡更任务", communityName, communityId);

                    // 检查是否已生成任务
                    if (patrolTaskService.isTodayTasksGenerated(communityId)) {
                        logger.info("小区 {} ({}) 今日任务已生成，跳过", communityName, communityId);
                        continue;
                    }

                    int taskCount = patrolTaskService.generateTodayTasks(communityId);
                    successCount++;
                    totalTaskCount += taskCount;
                    logger.info("小区 {} ({}) 巡更任务生成成功，生成{}个任务", communityName, communityId, taskCount);

                } catch (Exception e) {
                    failCount++;
                    logger.error("小区 {} ({}) 巡更任务生成失败: {}", communityName, communityId, e.getMessage(), e);
                }
            }

            logger.info("定时生成巡更任务完成，总计小区: {}, 成功: {}, 失败: {}, 总任务数: {}", communities.size(), successCount, failCount, totalTaskCount);

        } catch (Exception e) {
            logger.error("定时生成巡更任务失败", e);
        }
    }

    /**
     * 批量生成账单定时任务
     * 每天凌晨调用，检查符合条件的收费绑定并生成账单
     */
    public void batchGenerateBills() {
        logger.info("开始执行批量生成账单定时任务");

        try {
            // 调用现有的账单生成服务
            chargeBillTaskService.autoGenerateBills();
            logger.info("批量生成账单定时任务执行完成");
        } catch (Exception e) {
            logger.error("批量生成账单定时任务执行失败", e);
        }
    }
}
