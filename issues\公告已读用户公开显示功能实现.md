# 公告已读用户公开显示功能实现

## 功能描述
为公告系统新增"是否公开已读用户"配置选项，管理员可在发布公告时选择是否在小程序端公开显示已读的房屋业主信息。

## 实现内容

### 1. 数据库修改
- **文件**: `ehome-web/src/main/resources/sql-templates/add_notice_show_read_users.sql`
- **内容**: 为`sys_notice`表添加`show_read_users`字段（TINYINT DEFAULT 0）
- **说明**: 0=不公开，1=公开

### 2. 后端实体类修改
- **文件**: `ehome-system/src/main/java/com/ehome/system/domain/SysNotice.java`
- **修改内容**:
  - 添加`showReadUsers`属性
  - 添加对应的getter/setter方法
  - 更新toString方法

### 3. 数据库映射修改
- **文件**: `ehome-system/src/main/resources/mapper/system/SysNoticeMapper.xml`
- **修改内容**:
  - 更新resultMap添加`show_read_users`字段映射
  - 更新selectNoticeVo SQL包含新字段
  - 更新insert和update语句支持新字段

### 4. 管理后台页面修改
- **文件**: 
  - `ehome-page/src/main/resources/templates/system/notice/add.html`
  - `ehome-page/src/main/resources/templates/system/notice/edit.html`
- **修改内容**: 在"开启评论"字段后添加"公开已读用户"配置选项
- **默认值**: 不公开（符合用户要求）

### 5. 小程序接口开发
- **文件**: `ehome-oc/src/main/java/com/ehome/oc/controller/wx/WxNoticeController.java`
- **新增接口**: `GET /api/wx/notice/{noticeId}/readUsers`
- **功能**: 
  - 检查公告是否开启已读用户公开功能
  - 返回已读房屋名称列表（去重）
  - 保护用户隐私，只返回房屋名称

### 6. 小程序前端修改
- **文件**: `miniprogram/pages/notice/detail.js`
- **修改内容**:
  - 添加已读用户相关数据字段
  - 新增`loadReadUsers`方法
  - 在`getNoticeDetail`中调用已读用户加载

- **文件**: `miniprogram/pages/notice/detail.wxml`
- **修改内容**: 在页面底部添加已读用户显示区域

- **文件**: `miniprogram/pages/notice/detail.wxss`
- **修改内容**: 添加已读用户区域的样式

## 功能特性

### 管理后台
- 管理员可在发布/编辑公告时选择是否公开已读用户
- 默认设置为"不公开"，保护用户隐私
- 提供友好的说明文字

### 小程序端
- 仅在公告开启公开功能时显示已读用户区域
- 显示已读房屋数量和房屋名称列表
- 采用卡片式设计，美观易读
- 自动去重，避免重复显示

### 隐私保护
- 只显示房屋名称，不显示业主个人信息
- 管理员可控制是否公开
- 接口层面验证权限

## 使用说明

### 管理员操作
1. 在发布公告时，选择"公开已读用户"选项
2. 发布后，小程序用户可在公告详情页面看到已读房屋列表

### 用户体验
1. 用户查看公告详情时，如果公告开启了已读用户公开功能
2. 页面底部会显示"已读房屋"区域
3. 显示已读房屋数量和具体房屋名称

## 最新更新（2025-01-19）

### 界面优化
- **标签卡设计**: 当评论和已读功能都存在时，显示标签卡切换界面
- **单功能显示**: 当只有评论或只有已读功能时，不显示标签卡，直接显示内容
- **时间显示**: 已读列表左侧显示房屋名称，右侧显示阅读时间

### 后端接口优化
- 修改`/api/wx/notice/{noticeId}/readUsers`接口
- 返回数据包含房屋名称和最新阅读时间
- 按房屋去重，取每个房屋的最新阅读时间
- 按阅读时间倒序排列

### 前端交互优化
- 实现标签卡切换功能
- 自动判断显示模式（标签卡 vs 单标题）
- 优化样式设计，提升用户体验

## 技术实现亮点
- 利用现有的阅读记录系统，无需额外数据表
- 接口层面做权限控制，确保数据安全
- 前端优雅降级，未开启功能时不显示相关区域
- 智能标签卡切换，根据功能可用性自动调整界面
- 样式设计美观，与现有界面风格一致
