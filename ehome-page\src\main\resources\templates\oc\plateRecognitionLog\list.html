<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('车牌识别记录')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>车牌号：</label>
                                <input type="text" name="plateNumber" placeholder="请输入车牌号"/>
                            </li>
                            <li>
                                <label>用户姓名：</label>
                                <input type="text" name="userName" placeholder="请输入用户姓名"/>
                            </li>
                            <li>
                                <label>用户手机号：</label>
                                <input type="text" name="userPhone" placeholder="请输入手机号"/>
                            </li>
                            <li>
                                <label>识别状态：</label>
                                <select name="recognitionStatus">
                                    <option value="">所有</option>
                                    <option value="1">成功</option>
                                    <option value="0">失败</option>
                                </select>
                            </li>
                            <li>
                                <label>找到车主：</label>
                                <select name="ownerFound">
                                    <option value="">所有</option>
                                    <option value="1">是</option>
                                    <option value="0">否</option>
                                </select>
                            </li>
                            <li>
                                <label>识别时间：</label>
                                <input type="text" class="time-input" name="startTime" placeholder="开始时间"/>
                            </li>
                            <li>
                                <label>至：</label>
                                <input type="text" class="time-input" name="endTime" placeholder="结束时间"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-info btn-rounded btn-sm" onclick="showStatistics()">
                    <i class="fa fa-bar-chart"></i> 统计信息
                </a>
                <a class="btn btn-danger btn-rounded btn-sm multiple disabled" onclick="$.operate.removeAll()">
                    <i class="fa fa-remove"></i> 删除
                </a>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/plateRecognitionLog";
        
        $(function() {
            var options = {
                url: prefix + "/list",
                removeUrl: prefix + "/remove",
                modalName: "车牌识别记录",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'log_id',
                    title: '记录ID',
                    visible: false
                },
                {
                    field: 'file_id',
                    title: '文件ID',
                    visible: false
                },
                {
                    field: 'user_name',
                    title: '用户姓名'
                },
                {
                    field: 'user_phone',
                    title: '用户手机号'
                },
                {
                    field: 'plate_number',
                    title: '车牌号',
                    formatter: function(value, row, index) {
                        if (value) {
                            return '<span class="label label-primary">' + value + '</span>';
                        }
                        return '-';
                    }
                },
                {
                    field: 'recognition_status',
                    title: '识别状态',
                    formatter: function(value, row, index) {
                        if (value == 1) {
                            return '<span class="label label-success">成功</span>';
                        } else {
                            return '<span class="label label-danger">失败</span>';
                        }
                    }
                },
                {
                    field: 'confidence',
                    title: '置信度',
                    formatter: function(value, row, index) {
                        if (value) {
                            return (value * 100).toFixed(1) + '%';
                        }
                        return '-';
                    }
                },
                {
                    field: 'plate_color',
                    title: '车牌颜色'
                },
                {
                    field: 'owner_found',
                    title: '找到车主',
                    formatter: function(value, row, index) {
                        if (value == 1) {
                            return '<span class="label label-success">是</span>';
                        } else {
                            return '<span class="label label-warning">否</span>';
                        }
                    }
                },
                {
                    field: 'owner_name',
                    title: '车主姓名'
                },
                {
                    field: 'owner_phone',
                    title: '车主电话'
                },
                {
                    field: 'recognition_time',
                    title: '识别耗时',
                    formatter: function(value, row, index) {
                        if (value) {
                            return value + 'ms';
                        }
                        return '-';
                    }
                },
                {
                    field: 'create_time',
                    title: '识别时间'
                },
                {
                    field: 'ip_address',
                    title: 'IP地址'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="showDetail(\'' + row.log_id + '\')"><i class="fa fa-eye"></i>详情</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 显示详情
        function showDetail(logId) {
            var url = prefix + "/detail/" + logId;
            $.modal.openTab("识别记录详情", url);
        }

        // 显示统计信息
        function showStatistics() {
            $.ajax({
                url: prefix + "/statistics",
                type: "post",
                dataType: "json",
                success: function(result) {
                    if (result.code == 0) {
                        var data = result.data;
                        var content = '<div class="row">';
                        content += '<div class="col-sm-6">';
                        content += '<h4>今日统计</h4>';
                        content += '<p>总识别次数：' + (data.today.total_count || 0) + '</p>';
                        content += '<p>成功次数：' + (data.today.success_count || 0) + '</p>';
                        content += '<p>找到车主：' + (data.today.owner_found_count || 0) + '</p>';
                        content += '<p>平均耗时：' + (data.today.avg_time ? Math.round(data.today.avg_time) + 'ms' : '-') + '</p>';
                        content += '</div>';
                        content += '<div class="col-sm-6">';
                        content += '<h4>本月统计</h4>';
                        content += '<p>总识别次数：' + (data.month.total_count || 0) + '</p>';
                        content += '<p>成功次数：' + (data.month.success_count || 0) + '</p>';
                        content += '<p>找到车主：' + (data.month.owner_found_count || 0) + '</p>';
                        content += '</div>';
                        content += '</div>';
                        
                        $.modal.alert(content, "统计信息");
                    } else {
                        $.modal.alertError(result.msg);
                    }
                }
            });
        }
    </script>
</body>
</html>
