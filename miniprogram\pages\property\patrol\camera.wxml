<view class="patrol-camera-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <van-loading type="spinner" size="24px" />
    <text class="loading-text">加载任务信息...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="main-content">
    <!-- 任务信息卡片 -->
    <view class="task-info-card">
      <view class="task-header">
        <view class="location-name">{{taskInfo.location_name}}</view>
        <view class="planned-time">计划时间：{{taskInfo.planned_time}}</view>
      </view>
      <view class="location-address" wx:if="{{taskInfo.location_address}}">
        {{taskInfo.location_address}}
      </view>
    </view>

    <!-- 位置信息卡片 -->
    <view class="location-card">
      <view class="card-header">
        <text class="card-title">位置信息</text>
        <view class="location-status {{isLocationValid ? 'valid' : 'invalid'}}">
          {{isLocationValid ? '位置正常' : '位置异常'}}
        </view>
      </view>
      
      <view wx:if="{{locationLoading}}" class="location-loading">
        <van-loading type="spinner" size="16px" />
        <text>正在获取位置...</text>
      </view>
      
      <view wx:elif="{{locationError}}" class="location-error">
        <text class="error-text">{{locationError}}</text>
        <button class="retry-btn" bindtap="retryLocation">重新获取</button>
      </view>
      
      <view wx:elif="{{currentLocation}}" class="location-info">
        <view class="location-item">
          <text class="label">当前坐标：</text>
          <text class="value">{{currentLocation.latitude}}, {{currentLocation.longitude}}</text>
        </view>
        <view class="location-item">
          <text class="label">定位精度：</text>
          <text class="value">{{currentLocation.accuracy}}米</text>
        </view>
        <view class="location-item" wx:if="{{distanceFromTarget > 0}}">
          <text class="label">距离目标：</text>
          <text class="value {{isLocationValid ? '' : 'warning'}}">{{distanceFromTarget}}米</text>
        </view>
      </view>
      
      <view wx:else class="location-empty">
        <button class="get-location-btn" bindtap="getCurrentLocation">获取当前位置</button>
      </view>
    </view>

    <!-- 拍照区域 -->
    <view class="photo-section">
      <view class="section-header">
        <text class="section-title">巡更照片</text>
        <text class="photo-count">{{photoList.length}}/{{maxPhotos}}</text>
      </view>
      
      <view class="photo-grid">
        <!-- 已上传的照片 -->
        <view wx:for="{{photoList}}" wx:key="fileId" class="photo-item">
          <image class="photo-image" src="{{item.url}}" mode="aspectFill" 
                 bindtap="previewPhoto" data-index="{{index}}" />
          <view class="photo-delete" bindtap="deletePhoto" data-index="{{index}}">
            <van-icon name="cross" size="12px" color="#fff" />
          </view>
        </view>
        
        <!-- 添加照片按钮 -->
        <view wx:if="{{photoList.length < maxPhotos}}" class="photo-add" bindtap="takePhoto">
          <van-icon name="plus" size="24px" color="#999" />
          <text class="add-text">添加照片</text>
        </view>
      </view>
      
      <view class="photo-tips">
        <text>• 请拍摄清晰的现场照片</text>
        <text>• 最多可上传{{maxPhotos}}张照片</text>
        <text>• 建议包含关键设施和环境</text>
      </view>
    </view>

    <!-- 备注区域 -->
    <view class="remark-section">
      <view class="section-title">巡更备注</view>
      <textarea class="remark-input" 
                placeholder="请输入巡更情况说明（选填）"
                value="{{remark}}"
                bindinput="onRemarkInput"
                maxlength="200"
                show-confirm-bar="{{false}}" />
      <view class="char-count">{{remark.length}}/200</view>
    </view>
  </view>

  <!-- 底部提交按钮 -->
  <view class="submit-footer">
    <button class="submit-btn {{photoList.length > 0 && currentLocation ? 'enabled' : 'disabled'}}"
            bindtap="submitPatrol"
            disabled="{{submitting || photoList.length === 0 || !currentLocation}}">
      <van-loading wx:if="{{submitting}}" type="spinner" size="16px" color="#fff" />
      <text wx:else>{{submitting ? '提交中...' : '提交巡更记录'}}</text>
    </button>
  </view>
</view>
