package com.ehome.oc.service;

import com.ehome.oc.domain.OwnerImport;

import java.util.List;

/**
 * 业主导入服务接口
 * 
 * <AUTHOR>
 */
public interface IOwnerImportService {
    
    /**
     * 导入业主数据
     *
     * @param ownerList 业主数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @param communityId 小区ID
     * @param pmsId 物业ID
     * @return 结果
     */
    String importOwner(List<OwnerImport> ownerList, Boolean isUpdateSupport, String operName, String communityId, String pmsId);
}
