<!-- 物业端报修详情页面 -->
<wxs module="utils">
  var formatTime = function(timeStr) {
    if (!timeStr) return ''
    var date = getDate(timeStr)
    var year = date.getFullYear()
    var month = (date.getMonth() + 1).toString()
    var day = date.getDate().toString()
    var hour = date.getHours().toString()
    var minute = date.getMinutes().toString()
    var second = date.getSeconds().toString()

    month = month.length === 1 ? '0' + month : month
    day = day.length === 1 ? '0' + day : day
    hour = hour.length === 1 ? '0' + hour : hour
    minute = minute.length === 1 ? '0' + minute : minute
    second = second.length === 1 ? '0' + second : second

    return year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second
  }

  module.exports.formatTime = formatTime
</wxs>

<view class="container">
  <!-- 搜索栏 -->
  <view class="search-container">
    <van-search
      value="{{ searchKeyword }}"
      placeholder="搜索ID、报修人、报修内容"
      bind:input="onSearchInput"
      bind:search="onSearch"
      bind:clear="onSearchClear"
    />
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading && repairList.length === 0}}">
    <van-loading type="spinner" size="24px">加载中...</van-loading>
  </view>

  <!-- 空状态 -->
  <view wx:elif="{{filteredList.length === 0 && !loading}}" class="empty-container">
    <van-empty description="暂无待处理报修" />
  </view>

  <!-- 报修列表 -->
  <view wx:else class="repair-list">
    <view class="repair-item" wx:for="{{filteredList}}" wx:key="id">
      <!-- 报修头部 -->
      <view class="repair-header">
        <view class="repair-id">
          <text class="id-label">报修ID:</text>
          <text class="id-value">{{item.id}}</text>
        </view>
        <van-tag 
          type="warning" 
          color="{{statusMap[item.status].color}}"
          text-color="#fff"
        >
          {{statusMap[item.status].text}}
        </van-tag>
      </view>

      <!-- 报修信息 -->
      <view class="repair-info">
        <view class="info-row">
          <text class="info-label">报修类型</text>
          <text class="info-value">{{item.type || '生活琐事'}}</text>
        </view>
        
        <view class="info-row">
          <text class="info-label">预约时间</text>
          <text class="info-value">{{utils.formatTime(item.create_time)}}</text>
        </view>
        
        <view class="info-row">
          <text class="info-label">位置</text>
          <text class="info-value">{{item.address || '北京市朝阳区将台西路10号'}}</text>
        </view>
        
        <view class="info-row">
          <text class="info-label">报修人</text>
          <text class="info-value">{{item.name || '周班'}}</text>
        </view>
      </view>

      <!-- 报修内容 -->
      <view class="repair-content">
        <text class="content-label">报修内容：</text>
        <text class="content-text">{{item.content || '小区路灯坏了'}}</text>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <van-button 
          size="small" 
          type="default"
          data-action="transfer"
          data-id="{{item.id}}"
          bind:click="onActionClick"
        >
          转单
        </van-button>
        
        <van-button 
          size="small" 
          type="default"
          data-action="pause"
          data-id="{{item.id}}"
          bind:click="onActionClick"
        >
          暂停
        </van-button>
        
        <van-button 
          size="small" 
          type="default"
          data-action="return"
          data-id="{{item.id}}"
          bind:click="onActionClick"
        >
          退单
        </van-button>
        
        <van-button 
          size="small" 
          type="primary"
          data-action="complete"
          data-id="{{item.id}}"
          bind:click="onActionClick"
        >
          办结
        </van-button>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{loading && filteredList.length > 0}}" class="loading-more">
      <van-loading type="spinner" size="16px">加载更多...</van-loading>
    </view>

    <!-- 没有更多数据 -->
    <view wx:if="{{!hasMore && filteredList.length > 0}}" class="no-more">
      <text>没有更多数据了</text>
    </view>
  </view>

  <!-- 操作确认弹窗 -->
  <van-dialog
    show="{{ showActionDialog }}"
    title="确认操作"
    message="确定要{{getActionText(currentAction)}}这个报修单吗？"
    show-cancel-button
    bind:confirm="onConfirmAction"
    bind:cancel="onCancelAction"
  />

  <!-- 反馈输入弹窗 -->
  <van-dialog
    show="{{ showFeedbackDialog }}"
    title="处理反馈"
    show-cancel-button
    confirm-button-loading="{{ processing }}"
    bind:confirm="onConfirmFeedback"
    bind:cancel="onCancelFeedback"
  >
    <view class="feedback-container">
      <van-field
        value="{{ feedback }}"
        type="textarea"
        placeholder="请输入处理反馈内容"
        autosize
        border="{{ false }}"
        bind:input="onFeedbackInput"
      />
    </view>
  </van-dialog>
</view>
