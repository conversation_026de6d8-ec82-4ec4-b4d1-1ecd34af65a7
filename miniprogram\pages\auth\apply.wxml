<view class="container">
  <!-- 温馨提示 -->
  <view class="tips">
    <text class="tips-text">温馨提示：请填写真实信息，我们会尽快审核通过，敬请期待。</text>
  </view>

  <!-- 表单区域 -->
  <view class="form-section">
    <van-cell-group>
      <van-field
        value="{{ selectedHouseText }}"
        label="房号"
        placeholder="请选择小区、楼栋、房屋"
        is-link
        readonly
        required
        bind:tap="showCascader"
        class="form-field"
      />

      <van-field
        value="{{ ownerName }}"
        label="姓名"
        placeholder="请输入姓名"
        required
        bind:change="onOwnerNameChange"
        class="form-field"
      />

      <van-field
        value="{{ idCard }}"
        label="证件号码"
        placeholder="请输入证件号码"
        required
        bind:change="onIdCardChange"
        class="form-field"
      />

      <van-field
        value="{{ mobile }}"
        label="手机号"
        placeholder="请输入手机号"
        type="number"
        maxlength="11"
        required
        bind:change="onMobileChange"
        class="form-field"
      />

      <van-field
        value="{{ genderText }}"
        label="性别"
        placeholder="请选择性别"
        is-link
        readonly
        required
        bind:tap="showGenderPicker"
        class="form-field"
      />

      <van-field
        value="{{ isLiveText }}"
        label="住户状态"
        placeholder="请选择住户状态"
        is-link
        readonly
        required
        bind:tap="showIsLivePicker"
        class="form-field"
      />

      <van-field
        wx:if="{{ isLive === '1' }}"
        value="{{ moveDate }}"
        label="入住日期"
        placeholder="请选择入住日期"
        is-link
        readonly
        required
        bind:tap="showDatePicker"
        class="form-field"
      />

      <van-field
        value="{{ relTypeText }}"
        label="认证身份"
        placeholder="请选择认证身份"
        is-link
        readonly
        required
        bind:tap="showRelTypePicker"
        class="form-field"
      />

      <van-field
        value="{{ remark }}"
        label="备注"
        placeholder="请输入备注信息（选填）"
        type="textarea"
        autosize
        bind:change="onRemarkChange"
        class="form-field"
      />
    </van-cell-group>
  </view>

  <!-- 证件上传区域 -->
  <view class="upload-section">
    <view class="upload-header">
      <text class="upload-icon">📄</text>
      <text class="upload-title">上传证件</text>
      <text class="upload-required">*</text>
    </view>
    <view class="upload-subtitle">请上传房产证、居住证或身份证（必须上传）</view>
    <view class="upload-content">
      <van-uploader
        file-list="{{ attachmentFiles }}"
        bind:after-read="onAttachmentUpload"
        bind:delete="onAttachmentDelete"
        max-count="1"
        accept="image"
        size-type="{{ ['compressed'] }}"
        max-size="{{ 10 * 1024 * 1024 }}"
        upload-text="上传证件"
        upload-icon="photograph"
      />
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <van-button
      type="primary"
      size="large"
      class="submit-btn {{canSubmit ? '' : 'disabled'}}"
      loading="{{ submitting }}"
      bind:click="submitApply"
    >
      {{ submitting ? '提交中...' : '确定' }}
    </van-button>
  </view>
</view>

<!-- 级联选择器弹窗 -->
<van-popup
  show="{{ showCascaderPopup }}"
  position="bottom"
  round
  custom-class="cascader-popup"
  bind:close="onClose"
>
  <van-cascader
    wx:if="{{ showCascaderPopup }}"
    value="{{ cascaderValue }}"
    title="选择房屋"
    options="{{ cascaderOptions }}"
    bind:close="onClose"
    bind:change="onCascaderChange"
    bind:finish="onCascaderFinish"
  />
</van-popup>

<!-- 性别选择器弹窗 -->
<van-action-sheet
  show="{{ showGenderPicker }}"
  title="选择性别"
  actions="{{ genderActionOptions }}"
  bind:close="onGenderPickerClose"
  bind:select="onGenderSelect"
/>

<!-- 住户状态选择器弹窗 -->
<van-action-sheet
  show="{{ showIsLivePicker }}"
  title="选择住户状态"
  actions="{{ isLiveActionOptions }}"
  bind:close="onIsLivePickerClose"
  bind:select="onIsLiveSelect"
/>

<!-- 认证身份选择器弹窗 -->
<van-action-sheet
  show="{{ showRelTypePicker }}"
  title="选择认证身份"
  actions="{{ relTypeActionOptions }}"
  bind:close="onRelTypePickerClose"
  bind:select="onRelTypeSelect"
/>

<!-- 入住日期选择器弹窗 -->
<van-popup
  show="{{ showDatePicker }}"
  position="bottom"
  round
  custom-class="picker-popup"
  bind:close="onDatePickerClose"
>
  <van-datetime-picker
    type="date"
    title="选择入住日期"
    value="{{ currentDate }}"
    bind:confirm="onDateConfirm"
    bind:cancel="onDatePickerClose"
  />
</van-popup>


