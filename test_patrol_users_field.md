# 巡更配置新增巡更人字段测试指南

## 功能概述
1. 在 `eh_patrol_config` 表中新增 `patrol_users` 字段，存储巡更人姓名（逗号分隔）
2. 在配置列表页面添加"查看巡更记录"按钮，可跳转到对应地点的记录页面
3. 隐藏地理位置相关字段（经纬度、允许范围）
4. 巡更人员选择改用 select2 组件

## 测试步骤

### 1. 数据库迁移
执行以下SQL脚本：
```sql
-- 执行 sql/patrol_config_add_users_field.sql
```

### 2. 功能测试

#### 2.1 测试配置保存功能
1. 访问：`/oc/patrol/config`
2. 点击"新增配置"或编辑现有配置
3. 测试计划时间快速选择功能：
   - 点击"快选"下拉按钮
   - 选择预设时间（09:00、12:00、14:00、18:00、22:00）
   - 确认时间正确填入输入框
4. 使用 select2 组件选择巡更人员（支持搜索）
5. 确认地理位置相关字段已隐藏
6. 保存后检查：
   - `eh_patrol_config_user` 表中是否正确插入记录
   - `eh_patrol_config` 表中 `patrol_users` 字段是否正确保存姓名

#### 2.2 测试配置列表显示
1. 访问：`/oc/patrol/config`
2. 检查"巡更人员"列是否显示姓名而非人数
3. 检查是否有"查看记录"按钮
4. 确认"允许范围"列已隐藏

#### 2.3 测试跳转记录页面
1. 在配置列表中点击"查看记录"按钮
2. 检查是否正确跳转到记录页面
3. 检查地点名称是否自动填入搜索框
4. 检查是否自动执行搜索

### 3. 验证SQL
```sql
-- 检查字段是否添加成功
DESCRIBE eh_patrol_config;

-- 检查数据是否正确同步
SELECT 
    config_id,
    location_name,
    patrol_users,
    (SELECT COUNT(*) FROM eh_patrol_config_user u WHERE u.config_id = c.config_id) as user_count,
    (SELECT GROUP_CONCAT(u.user_name) FROM eh_patrol_config_user u WHERE u.config_id = c.config_id) as actual_users
FROM eh_patrol_config c;
```

## 预期结果
1. 配置列表显示巡更人员姓名，隐藏允许范围列
2. 点击"查看记录"能正确跳转并预筛选
3. 数据保存时同时更新关联表和冗余字段
4. 向后兼容，不影响现有功能
5. 新增/编辑页面使用 select2 组件选择人员
6. 地理位置相关字段已隐藏但仍保留默认值
7. 计划时间支持快速选择常用时间点
