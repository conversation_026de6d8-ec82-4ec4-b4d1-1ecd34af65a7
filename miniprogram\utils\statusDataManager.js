/**
 * Status数据管理器
 * 统一管理 /api/wx/index/status 接口调用，避免重复请求
 */

import {getStateManager} from './stateManager.js'
import {STORAGE_KEYS} from '../constants/index.js'
import SecureLogger from './secureLogger.js'

// 缓存配置
const CACHE_CONFIG = {
  USER_INFO: 30 * 60 * 1000,      // 用户基本信息：30分钟
  AUTH_STATUS: 10 * 60 * 1000,    // 房屋认证状态：10分钟  
  COMMUNITY_INFO: 60 * 60 * 1000, // 社区配置信息：60分钟
  SUBSCRIBE_STATUS: 5 * 60 * 1000 // 订阅状态：5分钟
}

// 获取模式
export const FETCH_MODES = {
  CACHE_FIRST: 'cache_first',     // 缓存优先
  FORCE_REFRESH: 'force_refresh', // 强制刷新
  CACHE_ONLY: 'cache_only'        // 仅使用缓存
}

class StatusDataManager {
  constructor() {
    try {
      this.app = getApp()
    } catch (error) {
      console.warn('[StatusDataManager] getApp()失败，将在使用时重新获取')
      this.app = null
    }

    this.stateManager = getStateManager()
    this.cache = new Map()
    this.pendingRequest = null // 防止并发请求
    this.lastRequestTime = 0
    this.debounceDelay = 500 // 防抖延迟500ms

    SecureLogger.log('StatusDataManager', '初始化完成')
  }

  /**
   * 获取状态数据
   * @param {Object} options 选项
   * @param {string} options.mode 获取模式
   * @param {Object} options.params 请求参数
   * @param {boolean} options.silent 静默模式，不显示错误
   * @returns {Promise<Object>} 状态数据
   */
  async getStatusData(options = {}) {
    const { 
      mode = FETCH_MODES.CACHE_FIRST, 
      params = {}, 
      silent = false 
    } = options

    try {
      // 仅缓存模式
      if (mode === FETCH_MODES.CACHE_ONLY) {
        return this.getCachedData()
      }

      // 缓存优先模式
      if (mode === FETCH_MODES.CACHE_FIRST) {
        const cachedData = this.getCachedData()
        if (cachedData) {
          SecureLogger.log('StatusDataManager', '使用缓存数据')
          return {
            success: true,
            data: cachedData,
            fromCache: true
          }
        }
      }

      // 防抖处理
      const now = Date.now()
      if (now - this.lastRequestTime < this.debounceDelay) {
        if (this.pendingRequest) {
          SecureLogger.log('StatusDataManager', '使用防抖中的请求')
          return await this.pendingRequest
        }
      }

      // 如果有正在进行的请求，等待它完成
      if (this.pendingRequest) {
        SecureLogger.log('StatusDataManager', '等待进行中的请求')
        return await this.pendingRequest
      }

      // 发起新请求
      this.lastRequestTime = now
      this.pendingRequest = this.fetchFromServer(params, silent)
      
      try {
        const result = await this.pendingRequest
        return result
      } finally {
        this.pendingRequest = null
      }

    } catch (error) {
      SecureLogger.error('StatusDataManager', '获取状态数据失败', error)
      
      // 出错时尝试返回缓存数据
      const cachedData = this.getCachedData()
      if (cachedData) {
        return {
          success: true,
          data: cachedData,
          fromCache: true,
          error: error.message
        }
      }

      return {
        success: false,
        error: error.message || error
      }
    }
  }

  /**
   * 从服务器获取数据
   * @param {Object} params 请求参数
   * @param {boolean} silent 静默模式
   * @returns {Promise<Object>}
   */
  async fetchFromServer(params, silent) {
    SecureLogger.log('StatusDataManager', '从服务器获取数据', { params })

    // 确保app实例存在
    if (!this.app || !this.app.request) {
      this.app = getApp()
      if (!this.app || !this.app.request) {
        throw new Error('App实例未初始化或request方法不存在')
      }
    }

    // 添加超时保护
    const res = await Promise.race([
      this.app.request({
        url: '/api/wx/index/status',
        method: 'POST',
        data: params
      }),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('请求超时')), 15000)
      )
    ])

    if (res.code === 500) {
      return {
        success: false,
        needLogin: true,
        error: '需要重新登录'
      }
    }

    if (res.code === 0) {
      const statusData = {
        isHouseAuth: res.data.isHouseAuth,
        houseInfo: res.data.houseInfo,
        ownerInfo: res.data.ownerInfo,
        communityInfo: res.data.communityInfo,
        tokenUser: res.data.tokenUser,
        subscribeStatus: res.data.subscribeStatus,
        isPropertyUser: res.data.isPropertyUser,
        userType: res.data.userType
      }

      // 缓存数据
      this.setCachedData(statusData)

      // 更新状态管理器
      this.stateManager.setState(statusData)

      // 如果有新token，更新存储
      if (res.token) {
        wx.setStorageSync(STORAGE_KEYS.TOKEN, res.token)
      }

      SecureLogger.log('StatusDataManager', '数据获取成功并已缓存')
      
      return {
        success: true,
        data: statusData,
        token: res.token,
        fromCache: false
      }
    } else {
      throw new Error(res.msg || '获取状态数据失败')
    }
  }

  /**
   * 获取缓存数据
   * @returns {Object|null}
   */
  getCachedData() {
    const cached = this.cache.get('statusData')
    if (!cached) return null

    const now = Date.now()
    
    // 检查各部分数据是否过期
    const result = {}
    let hasValidData = false

    // 检查用户信息
    if (cached.userInfo && (now - cached.userInfo.timestamp < CACHE_CONFIG.USER_INFO)) {
      result.ownerInfo = cached.userInfo.data.ownerInfo
      result.tokenUser = cached.userInfo.data.tokenUser
      result.isPropertyUser = cached.userInfo.data.isPropertyUser
      result.userType = cached.userInfo.data.userType
      hasValidData = true
    }

    // 检查认证状态
    if (cached.authStatus && (now - cached.authStatus.timestamp < CACHE_CONFIG.AUTH_STATUS)) {
      result.isHouseAuth = cached.authStatus.data.isHouseAuth
      result.houseInfo = cached.authStatus.data.houseInfo
      hasValidData = true
    }

    // 检查社区信息
    if (cached.communityInfo && (now - cached.communityInfo.timestamp < CACHE_CONFIG.COMMUNITY_INFO)) {
      result.communityInfo = cached.communityInfo.data.communityInfo
      hasValidData = true
    }

    // 检查订阅状态
    if (cached.subscribeStatus && (now - cached.subscribeStatus.timestamp < CACHE_CONFIG.SUBSCRIBE_STATUS)) {
      result.subscribeStatus = cached.subscribeStatus.data.subscribeStatus
      hasValidData = true
    }

    return hasValidData ? result : null
  }

  /**
   * 设置缓存数据
   * @param {Object} data 状态数据
   */
  setCachedData(data) {
    const now = Date.now()
    
    const cacheData = {
      userInfo: {
        timestamp: now,
        data: {
          ownerInfo: data.ownerInfo,
          tokenUser: data.tokenUser,
          isPropertyUser: data.isPropertyUser,
          userType: data.userType
        }
      },
      authStatus: {
        timestamp: now,
        data: {
          isHouseAuth: data.isHouseAuth,
          houseInfo: data.houseInfo
        }
      },
      communityInfo: {
        timestamp: now,
        data: {
          communityInfo: data.communityInfo
        }
      },
      subscribeStatus: {
        timestamp: now,
        data: {
          subscribeStatus: data.subscribeStatus
        }
      }
    }

    this.cache.set('statusData', cacheData)
    SecureLogger.log('StatusDataManager', '数据已缓存')
  }

  /**
   * 清除缓存
   * @param {string} type 清除类型，不传则清除全部
   */
  clearCache(type) {
    if (type) {
      const cached = this.cache.get('statusData')
      if (cached && cached[type]) {
        delete cached[type]
        SecureLogger.log('StatusDataManager', `已清除${type}缓存`)
      }
    } else {
      this.cache.clear()
      SecureLogger.log('StatusDataManager', '已清除所有缓存')
    }
  }

  /**
   * 检查是否有进行中的请求
   * @returns {boolean}
   */
  hasPendingRequest() {
    return !!this.pendingRequest
  }

  /**
   * 处理登录返回的数据（避免重复调用status接口）
   * @param {Object} loginData 登录返回的数据
   */
  handleLoginData(loginData) {
    if (!loginData) return

    // 构建标准的状态数据结构
    const statusData = {
      isHouseAuth: loginData.isHouseAuth,
      houseInfo: loginData.houseInfo,
      ownerInfo: loginData.ownerInfo,
      communityInfo: loginData.communityInfo,
      tokenUser: loginData.tokenUser,
      subscribeStatus: loginData.subscribeStatus,
      isPropertyUser: loginData.isPropertyUser,
      userType: loginData.userType
    }

    // 缓存数据
    this.setCachedData(statusData)

    // 更新状态管理器
    this.stateManager.setState(statusData)

    SecureLogger.log('StatusDataManager', '登录数据已处理并缓存')
  }

  /**
   * 获取缓存统计信息
   * @returns {Object}
   */
  getCacheStats() {
    const cached = this.cache.get('statusData')
    if (!cached) return { total: 0, valid: 0 }

    const now = Date.now()
    let total = 0
    let valid = 0

    Object.keys(cached).forEach(key => {
      total++
      const item = cached[key]
      const maxAge = CACHE_CONFIG[key.toUpperCase()] || CACHE_CONFIG.AUTH_STATUS
      if (now - item.timestamp < maxAge) {
        valid++
      }
    })

    return { total, valid }
  }
}

// 创建单例
let statusDataManager = null

export function getStatusDataManager() {
  if (!statusDataManager) {
    statusDataManager = new StatusDataManager()
  }
  return statusDataManager
}

export default getStatusDataManager
