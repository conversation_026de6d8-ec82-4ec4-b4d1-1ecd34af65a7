/**
 * 首页常量定义
 * 统一管理首页相关的常量和配置
 */

/**
 * 默认主卡片数据
 */
export const DEFAULT_MAIN_CARDS = [
  {
    nav_id: 'default_repair',
    nav_name: '报事报修',
    icon_name: 'setting-o',
    remark: '一键维修',
    tap_name: 'goToRepair',
    nav_type: 'function',
    cardStyle: 'repair-card',
    isDefault: true
  },
  {
    nav_id: 'default_contact',
    nav_name: '联系物业',
    icon_name: 'service',
    remark: '一键搞定',
    tap_name: 'goToPropertyPhone',
    nav_type: 'function',
    cardStyle: 'contact-card',
    isDefault: true
  }
]

/**
 * 菜单图标颜色配置
 */
export const MENU_ICON_COLORS = [
  'linear-gradient(135deg, #1890ff, #69c0ff)',
  'linear-gradient(135deg, #52c41a, #95de64)',
  'linear-gradient(135deg, #faad14, #ffd666)',
  'linear-gradient(135deg, #ff4d4f, #ff7875)',
  'linear-gradient(135deg, #722ed1, #b37feb)',
  'linear-gradient(135deg, #13c2c2, #5cdbd3)',
  'linear-gradient(135deg, #eb2f96, #f759ab)',
  'linear-gradient(135deg, #fa8c16, #ffb366)'
]

/**
 * 超时配置（毫秒）
 */
export const TIMEOUT_CONFIG = {
  PAGE_INIT: 3000,           // 页面初始化超时
  AUTH_CHECK: 15000,         // 认证状态检查超时
  FALLBACK_PROTECTION: 3500  // 兜底保护超时
}

/**
 * 菜单布局配置
 */
export const MENU_LAYOUT_CONFIG = {
  DEFAULT_ITEMS_PER_ROW: 4,  // 默认每行显示个数
  MAX_ROWS_4_ITEMS: 2,       // 4个一排时最多显示行数
  MAX_ROWS_3_ITEMS: 3,       // 3个一排时最多显示行数
  MAX_ITEMS_4_PER_ROW: 8,    // 4个一排时最多显示总数
  MAX_ITEMS_3_PER_ROW: 9     // 3个一排时最多显示总数
}

/**
 * 页面状态常量
 */
export const PAGE_STATES = {
  LOADING: 'loading',
  CONTENT_VISIBLE: 'content_visible',
  FUNCTION_CONTAINER_VISIBLE: 'function_container_visible'
}

/**
 * 默认社区信息
 */
export const DEFAULT_COMMUNITY_INFO = {
  communityName: '睦邻共治',
  communityBanner: '/static/images/banner.png'
}

/**
 * 默认横幅配置
 */
export const DEFAULT_BANNER_CONFIG = {
  title: '智享社区，贴心到家，共同成长，共创未来！',
  subtitle: '物业服务就在掌心，共同成长，共创未来！'
}

/**
 * 通知类型默认配置
 */
export const DEFAULT_NOTICE_TYPES = [
  { dictValue: '', dictLabel: '全部', dictSort: 0 }
]

/**
 * 分享配置常量
 */
export const SHARE_CONFIG = {
  DEFAULT_TITLE: '智慧社区服务',
  DEFAULT_DESC: '便民服务，触手可及',
  DEFAULT_IMAGE: '/static/images/share-logo.png'
}

/**
 * 桌面引导配置
 */
export const DESKTOP_GUIDE_CONFIG = {
  STORAGE_KEY: 'desktop_guide_shown',
  SHOW_DELAY: 3000  // 延迟3秒显示
}

/**
 * 订阅消息配置
 */
export const SUBSCRIBE_CONFIG = {
  CHECK_DELAY: 2000,  // 延迟2秒检查订阅
  SUCCESS_DURATION: 2000  // 成功提示持续时间
}

/**
 * 错误消息常量
 */
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  LOGIN_REQUIRED: '请先登录',
  AUTH_REQUIRED: '请先完成房屋认证',
  MENU_CONFIG_ERROR: '菜单配置错误',
  MINIPROGRAM_CONFIG_ERROR: '小程序配置格式错误，请联系管理员',
  MINIPROGRAM_ID_INVALID: '小程序ID无效',
  FUNCTION_NOT_AVAILABLE: '功能暂未开放',
  GET_MENU_DETAIL_FAILED: '获取菜单详情失败',
  REFRESH_FAILED: '刷新失败，请重试'
}

/**
 * 成功消息常量
 */
export const SUCCESS_MESSAGES = {
  REFRESH_SUCCESS: '刷新成功',
  SUBSCRIBE_SUCCESS: '订阅成功',
  DESKTOP_GUIDE_CONFIRMED: '已设置不再提示'
}

/**
 * Toast配置
 */
export const TOAST_CONFIG = {
  SUCCESS_DURATION: 1500,
  ERROR_DURATION: 2000,
  NORMAL_DURATION: 1500
}

/**
 * 页面路径常量
 */
export const PAGE_PATHS = {
  LOGIN: '/pages/login/login',
  HOUSE_LIST: '/pages/house/index',
  REPAIR: '/pages/bx/bx',
  COMPLAINT: '/pages/complaint/complaint',
  SERVICE_TEL: '/pages/serviceTel/index',
  VISITOR: '/pages/house/index?action=invite',
  OC_INFO: '/pages/ocinfo/ocinfo',
  NAV_INDEX: '/pages/nav/index',
  NOTICE_DETAIL: '/pages/notice/detail',
  NOTICE_LIST: '/pages/notice/list',
  MENU_CONTENT: '/pages/menu/content',
  WEBVIEW: '/pages/webview/index',
  VEHICLE_RECOGNIZE: '/pages/vehicle/recognize'
}

/**
 * API路径常量
 */
export const API_PATHS = {
  INDEX_STATUS: '/api/wx/index/status',
  INDEX_NOTICES: '/api/wx/index/notices',
  INDEX_MENUS: '/api/wx/index/getMenus',
  NOTICE_TYPES: '/api/wx/data/getNoticeTypes',
  MENU_CONTENT: '/api/wx/data/menuContent',
  SHARE_RECORD: '/api/wx/share/record',
  SHARE_VISIT: '/api/wx/share/visit'
}

/**
 * 存储键常量
 */
export const STORAGE_KEYS = {
  TOKEN: 'token',
  WX_USER_INFO: 'wxUserInfo',
  DESKTOP_GUIDE_SHOWN: 'desktop_guide_shown'
}

/**
 * 图标名称常量
 */
export const ICON_NAMES = {
  DEFAULT_MENU: 'apps-o',
  VOLUME: 'volume-o',
  EXCHANGE: 'exchange',
  CROSS: 'cross',
  ARROW: 'arrow',
  SMILE: 'smile-o',
  SETTING: 'setting-o',
  SERVICE: 'service'
}

/**
 * CSS类名常量
 */
export const CSS_CLASSES = {
  REPAIR_CARD: 'repair-card',
  CONTACT_CARD: 'contact-card',
  NO_MAIN_CARDS: 'no-main-cards'
}

/**
 * 微信小程序环境版本
 */
export const MINIPROGRAM_ENV_VERSION = {
  DEVELOP: 'develop',
  TRIAL: 'trial',
  RELEASE: 'release'
}

/**
 * 正则表达式常量
 */
export const REGEX_PATTERNS = {
  WECHAT_ARTICLE: /^https:\/\/mp\.weixin\.qq\.com\//,
  MINIPROGRAM_ID: /^wx[a-zA-Z0-9]{16}$/
}

/**
 * 数字常量
 */
export const NUMBERS = {
  MIN_MINIPROGRAM_ID_LENGTH: 10,
  EXPECTED_MINIPROGRAM_ID_LENGTH: 18,
  DEFAULT_PAGE_SIZE: 5,
  DEFAULT_PAGE_NUMBER: 1
}

export default {
  DEFAULT_MAIN_CARDS,
  MENU_ICON_COLORS,
  TIMEOUT_CONFIG,
  MENU_LAYOUT_CONFIG,
  PAGE_STATES,
  DEFAULT_COMMUNITY_INFO,
  DEFAULT_BANNER_CONFIG,
  DEFAULT_NOTICE_TYPES,
  SHARE_CONFIG,
  DESKTOP_GUIDE_CONFIG,
  SUBSCRIBE_CONFIG,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  TOAST_CONFIG,
  PAGE_PATHS,
  API_PATHS,
  STORAGE_KEYS,
  ICON_NAMES,
  CSS_CLASSES,
  MINIPROGRAM_ENV_VERSION,
  REGEX_PATTERNS,
  NUMBERS
}
