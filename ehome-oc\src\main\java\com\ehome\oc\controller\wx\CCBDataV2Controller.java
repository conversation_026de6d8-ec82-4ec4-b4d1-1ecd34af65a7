package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.List;

/**
 * 建行数据查询 V2 - 优化版本，支持懒加载
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/api/wx/ccbv2")
public class CCBDataV2Controller extends BaseWxController {

    /**
     * 获取年份列表和账户基本信息
     */
    @GetMapping("/years")
    public AjaxResult getYears() {
        try {
            String lastYear = String.valueOf(java.time.LocalDate.now().getYear() - 1);
            
            // 获取所有可用年份
            EasySQL sql = new EasySQL();
            sql.append("SELECT DISTINCT SUBSTRING(tran_month,1,4) AS year");
            sql.append("FROM eh_tran_record WHERE 1=1");
            sql.append(getCurrentUser().getCommunityId(), "AND community_id = ?",false);
            sql.append("AND status = 'published'");
            sql.append(lastYear, "AND SUBSTRING(tran_month,1,4) >= ?");
            sql.append("ORDER BY year DESC");
            
            List<Record> yearRecords = Db.find(sql.getSQL(), sql.getParams());
            
            // 构建年份列表
            JSONArray years = new JSONArray();
            for (Record record : yearRecords) {
                years.add(record.getStr("year"));
            }
            
            // 获取账户信息
            JSONObject result = new JSONObject();
            result.put("years", years);
            result.put("updateTime", DateUtils.dateTimeNow());
            result.put("dataSource", "建设银行");
            result.put("accountNumber", "--");
            result.put("balance", "--");
            
            Record bankInfo = Db.findFirst("select * from eh_pms_bank_account where community_id = ?", getCurrentUser().getCommunityId());
            if (bankInfo != null) {
                result.put("balance", formatAmount(bankInfo.getBigDecimal("balance")));
                result.put("updateTime", bankInfo.getStr("balance_last_time"));
                result.put("accountNumber", maskBankCardNumber(bankInfo.getStr("acc_no")));
            }
            
            return AjaxResult.success("成功", result);
        } catch (Exception e) {
            logger.error("获取年份列表失败: " + e.getMessage(), e);
            return AjaxResult.error("获取年份列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取指定年份的月度汇总数据
     */
    @GetMapping("/yearDetail")
    public AjaxResult getYearDetail(@RequestParam String year) {
        try {
            if (StringUtils.isEmpty(year)) {
                return AjaxResult.error("年份参数不能为空");
            }
            
            EasySQL sql = new EasySQL();
            sql.append("SELECT tran_month,");
            sql.append("SUM(CASE WHEN direction = 'in' THEN amt ELSE 0 END) AS income,");
            sql.append("SUM(CASE WHEN direction = 'out' THEN amt ELSE 0 END) AS expense");
            sql.append("FROM eh_tran_record WHERE 1=1");
            sql.append(getCurrentUser().getCommunityId(), "AND community_id = ?",false);
            sql.append("AND status = 'published'");
            sql.append(year, "AND SUBSTRING(tran_month,1,4) = ?");
            sql.append("GROUP BY tran_month");
            sql.append("ORDER BY tran_month DESC");
            
            List<Record> monthRecords = Db.find(sql.getSQL(), sql.getParams());
            
            // 构建月份数据
            JSONArray months = new JSONArray();
            BigDecimal yearIncome = BigDecimal.ZERO;
            BigDecimal yearExpense = BigDecimal.ZERO;
            
            for (Record record : monthRecords) {
                JSONObject month = new JSONObject();
                String tranMonth = record.getStr("tran_month");
                BigDecimal monthIncome = record.getBigDecimal("income");
                BigDecimal monthExpense = record.getBigDecimal("expense");
                
                month.put("tran_month", tranMonth);
                month.put("year", year);
                month.put("monthStr", convertToChineseMonth(tranMonth));
                month.put("income", formatAmount(monthIncome));
                month.put("expend", formatAmount(monthExpense));
                
                // 累计年度收支
                yearIncome = yearIncome.add(monthIncome == null ? BigDecimal.ZERO : monthIncome);
                yearExpense = yearExpense.add(monthExpense == null ? BigDecimal.ZERO : monthExpense);
                
                months.add(month);
            }
            
            JSONObject result = new JSONObject();
            result.put("currentYear", year);
            result.put("yearIncome", formatAmount(yearIncome));
            result.put("yearExpense", formatAmount(yearExpense));
            result.put("months", months);
            
            return AjaxResult.success("成功", result);
        } catch (Exception e) {
            logger.error("获取年度详情失败: " + e.getMessage(), e);
            return AjaxResult.error("获取年度详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取指定月份的交易详情
     */
    @GetMapping("/monthDetail")
    public AjaxResult getMonthDetail(@RequestParam String month) {
        try {
            if (StringUtils.isEmpty(month)) {
                return AjaxResult.error("月份参数不能为空");
            }
            
            // 处理月份格式 (YYYY-MM -> YYYYMM)
            String tranMonth = month.replace("-", "");
            if (tranMonth.length() != 6) {
                return AjaxResult.error("月份格式错误，应为 YYYY-MM");
            }
            
            EasySQL sql = new EasySQL();
            sql.append("SELECT * FROM eh_tran_record WHERE 1=1");
            sql.append("AND status = 'published'");
            sql.append(getCurrentUser().getCommunityId(), "AND community_id = ?",false);
            sql.append(tranMonth, "AND tran_month = ?");
            sql.append("ORDER BY tran_datetime DESC");
            
            List<Record> records = Db.find(sql.getSQL(), sql.getParams());
            
            // 构建交易详情列表
            JSONArray list = new JSONArray();
            BigDecimal monthIncome = BigDecimal.ZERO;
            BigDecimal monthExpense = BigDecimal.ZERO;
            BigDecimal balance = null;
            
            for (Record record : records) {
                JSONObject row = new JSONObject();
                row.put("id", record.get("trck_no"));
                row.put("title", record.get("message"));
                row.put("date", record.get("tran_date"));
                
                String direction = record.getStr("direction");
                row.put("type", direction);
                
                BigDecimal amt = record.getBigDecimal("amt");
                row.put("amount", formatAmount(amt));
                
                if (balance == null) {
                    balance = record.getBigDecimal("amt_all");
                }
                
                if ("in".equals(direction)) {
                    monthIncome = monthIncome.add(amt == null ? BigDecimal.ZERO : amt);
                } else if ("out".equals(direction)) {
                    monthExpense = monthExpense.add(amt == null ? BigDecimal.ZERO : amt);
                }
                
                list.add(row);
            }
            
            JSONObject result = new JSONObject();
            result.put("list", list);
            result.put("monthIncome", formatAmount(monthIncome));
            result.put("monthExpense", formatAmount(monthExpense));
            result.put("balance", balance == null ? formatAmount(BigDecimal.ZERO) : formatAmount(balance));
            
            return AjaxResult.success("成功", result);
        } catch (Exception e) {
            logger.error("获取月份详情失败: " + e.getMessage(), e);
            return AjaxResult.error("获取月份详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 转换月份格式为中文
     */
    private String convertToChineseMonth(String yearMonth) {
        if (yearMonth == null || yearMonth.length() != 6) {
            throw new IllegalArgumentException("格式必须是yyyyMM，例如202501");
        }
        String monthStr = yearMonth.substring(4, 6); // 提取 MM
        int month = Integer.parseInt(monthStr);      // 去掉前导0
        return month + "月";
    }
    
    /**
     * 金额格式化：四舍五入保留两位小数，千分位分隔符
     */
    private static String formatAmount(BigDecimal amount) {
        if (amount == null) return "0.00";
        BigDecimal scaled = amount.setScale(2, RoundingMode.HALF_UP);
        DecimalFormat df = new DecimalFormat("#,##0.00");
        return df.format(scaled);
    }

    /**
     * 银行卡号加密显示
     * 保留前4位和后4位，中间用*号替换
     */
    private static String maskBankCardNumber(String cardNumber) {
        if (cardNumber == null || cardNumber.trim().isEmpty()) {
            return "--";
        }

        String cleanCardNumber = cardNumber.trim();
        int length = cleanCardNumber.length();

        // 如果卡号长度小于8位，直接返回原卡号（避免过度加密）
        if (length <= 8) {
            return cleanCardNumber;
        }

        // 保留前4位和后4位，中间用*号替换
        String prefix = cleanCardNumber.substring(0, 4);
        String suffix = cleanCardNumber.substring(length - 4);
        int maskLength = length - 8;

        StringBuilder masked = new StringBuilder();
        masked.append(prefix);
        for (int i = 0; i < maskLength; i++) {
            masked.append("*");
        }
        masked.append(suffix);

        return masked.toString();
    }
}