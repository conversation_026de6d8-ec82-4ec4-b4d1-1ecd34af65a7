/**
 * 页面工具类 - 提供公共的页面功能（重构版，按照微信官方最佳实践）
 */

/**
 * 通用TabBar初始化函数
 * @param {string} userType 用户类型 '1'=业主, '2'=物业
 */
export function initTabBar(userType) {
  if (typeof getCurrentPages === 'function') {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]

    if (currentPage && typeof currentPage.getTabBar === 'function' && currentPage.getTabBar()) {
      const tabBar = currentPage.getTabBar()

      // 切换到对应用户类型的菜单
      if (typeof tabBar.toggleMenu === 'function') {
        tabBar.toggleMenu(userType)
      }

      // 初始化选中状态
      if (typeof tabBar.init === 'function') {
        tabBar.init()
      }
    }
  }
}

/**
 * 自动检测用户类型并初始化TabBar
 * 根据当前登录用户的类型自动选择合适的TabBar配置
 */
export function autoInitTabBar() {
  const userType = getCurrentUserType()
  initTabBar(userType)
}

/**
 * 物业页面通用的onShow处理
 * @param {Object} pageInstance 页面实例
 * @param {Function} loadDataFn 加载数据的函数
 */
export function handlePropertyPageShow(pageInstance, loadDataFn) {
  // 初始化物业TabBar
  initTabBar('2')

  // 加载页面数据
  if (typeof loadDataFn === 'function') {
    loadDataFn.call(pageInstance)
  }
}

/**
 * 业主页面通用的onShow处理
 * @param {Object} pageInstance 页面实例
 * @param {Function} loadDataFn 加载数据的函数
 */
export function handleOwnerPageShow(pageInstance, loadDataFn) {
  // 初始化业主TabBar
  initTabBar('1')

  // 加载页面数据
  if (typeof loadDataFn === 'function') {
    loadDataFn.call(pageInstance)
  }
}

/**
 * 获取当前用户类型
 * @returns {string} 用户类型 '1'=业主, '2'=物业
 */
export function getCurrentUserType() {
  try {
    const app = getApp()
    if (app && app.globalData && app.globalData.stateManager) {
      const state = app.globalData.stateManager.getState()
      return state.userType || '1'
    }
  } catch (error) {
    console.error('获取用户类型失败:', error)
  }
  return '1' // 默认业主
}

/**
 * 检查是否为物业用户
 * @returns {boolean}
 */
export function isPropertyUser() {
  return getCurrentUserType() === '2'
}

/**
 * 检查是否为业主用户
 * @returns {boolean}
 */
export function isOwnerUser() {
  return getCurrentUserType() === '1'
}
