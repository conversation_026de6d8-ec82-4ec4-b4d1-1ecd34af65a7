<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('巡更配置管理')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                地点名称：<input type="text" name="location_name" placeholder="请输入地点名称" onkeypress="if(event.keyCode==13) $.table.search()"/>
                            </li>
                            <li>
                                项目名称：<input type="text" name="project_name" placeholder="请输入项目名称" onkeypress="if(event.keyCode==13) $.table.search()"/>
                            </li>
                            <li class="select-time">
                                <label>状态：</label>
                                <select name="is_active">
                                    <option value="">所有</option>
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="oc:patrol:add">
                    <i class="fa fa-plus"></i> 新增配置
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="oc:patrol:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="oc:patrol:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-info" onclick="generateTodayTasks()" shiro:hasPermission="oc:patrol:generate">
                    <i class="fa fa-calendar"></i> 生成今日任务
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="oc:patrol:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/patrol";
        var editFlag = true;
        var removeFlag = true;

        $(function() {
            var options = {
                url: prefix + "/config/list",
                createUrl: prefix + "/config/add",
                updateUrl: prefix + "/config/edit/{id}",
                removeUrl: prefix + "/config/delete",
                exportUrl: prefix + "/config/export",
                uniqueId: "config_id",
                modalName: "巡更配置",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'config_id',
                    title: '配置ID',
                    visible: false
                },
                {
                    field: 'location_name',
                    title: '地点名称',
                    sortable: true
                },
                {
                    field: 'project_name',
                    title: '项目名称',
                    sortable: true
                },
                {
                    field: 'location_address',
                    title: '地点地址'
                },
                {
                    field: 'planned_time',
                    title: '计划时间',
                    sortable: true
                },
                {
                    field: 'patrol_users',
                    title: '巡更人员',
                    formatter: function(value, row, index) {
                        if (value && value.trim() !== '') {
                            return value;
                        } else {
                            return row.user_count + '人';
                        }
                    }
                },

                {
                    field: 'is_active',
                    title: '状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == 1) {
                            return '<span class="badge badge-success">启用</span>';
                        } else {
                            return '<span class="badge badge-danger">禁用</span>';
                        }
                    }
                },
                {
                    field: 'remark',
                    title: '备注',
                    formatter: function(value, row, index) {
                        if (value && value.trim() !== '') {
                            // 如果备注太长，只显示前30个字符
                            return value.length > 30 ? value.substring(0, 30) + '...' : value;
                        } else {
                            return '-';
                        }
                    }
                },
                {
                    field: 'create_time',
                    title: '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewPatrolRecords(\'' + row.location_name + '\')"><i class="fa fa-list"></i>查看记录</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.config_id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.config_id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 生成今日任务
        function generateTodayTasks() {
            $.modal.confirm("确认要生成今日巡更任务吗？", function() {
                $.operate.post(prefix + "/generateTodayTasks", {}, function(result) {
                    $.modal.msgSuccess(result.msg);
                    $.table.refresh();
                });
            });
        }

        // 查看巡更记录
        function viewPatrolRecords(locationName) {
            var url = prefix + "/record?location_name=" + encodeURIComponent(locationName);
            $.modal.openTab("巡更记录 - " + locationName, url);
        }
    </script>
</body>
</html>
