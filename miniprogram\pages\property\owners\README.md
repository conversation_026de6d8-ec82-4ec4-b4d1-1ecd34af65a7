# 业主管理功能

## 功能概述
为物业管理系统新增的业主管理功能，提供楼栋-房屋-业主三级查看体验。

## 功能特性
- ✅ 左侧楼栋列表展示
- ✅ 右侧房屋列表展示  
- ✅ 房屋详情和业主信息查看
- ✅ 楼栋搜索功能
- ✅ 响应式布局适配
- ✅ 权限控制（仅物业用户）

## 页面结构
```
/pages/property/owners/
├── index.js      # 页面逻辑
├── index.wxml    # 页面结构
├── index.wxss    # 页面样式
├── index.json    # 页面配置
└── README.md     # 功能说明
```

## API接口
### 后端控制器：WxOwnerController

1. **GET /api/wx/owner/buildings**
   - 获取楼栋列表
   - 返回：楼栋ID、名称、房屋数量

2. **GET /api/wx/owner/houses/{buildingId}**
   - 获取指定楼栋的房屋列表
   - 返回：房屋信息、业主数量、业主姓名

3. **GET /api/wx/owner/houseDetail/{houseId}**
   - 获取房屋详情和业主信息
   - 返回：房屋完整信息、绑定业主列表

4. **GET /api/wx/owner/searchBuildings?keyword=xxx**
   - 搜索楼栋
   - 返回：匹配的楼栋列表

## 使用说明
1. 物业用户登录后，在TabBar中点击"业主"菜单
2. 左侧选择楼栋，右侧显示该楼栋的房屋列表
3. 点击房屋可查看详细信息和绑定的业主
4. 使用顶部搜索框可搜索楼栋

## 权限控制
- 仅物业用户（userType='2'）可访问
- 数据按社区隔离，只能查看当前社区数据
- 非物业用户访问时自动跳转到首页

## 技术实现
- **前端**：使用Vant组件库，van-sidebar + van-list布局
- **后端**：Spring Boot + JFinal ActiveRecord
- **数据库**：MySQL，关联查询楼栋、房屋、业主表
- **权限**：基于用户类型和社区ID进行数据隔离

## 测试要点
1. 楼栋列表加载是否正常
2. 房屋列表切换是否正确
3. 搜索功能是否有效
4. 房屋详情显示是否完整
5. 权限控制是否生效
6. 不同设备适配是否正常
