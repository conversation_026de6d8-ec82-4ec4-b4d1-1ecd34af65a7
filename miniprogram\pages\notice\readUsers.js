const app = getApp()
import {formatTime} from '../../utils/common.js'

Page({
  data: {
    noticeId: '',
    title: '',
    readUsersList: [],
    loading: true
  },

  onLoad(options) {
    const { noticeId, title } = options
    this.setData({
      noticeId,
      title: decodeURIComponent(title || '公告')
    })
    
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '已读房屋'
    })
    
    this.loadAllReadUsers()
  },

  // 加载全部已读用户
  async loadAllReadUsers() {
    try {
      this.setData({ loading: true })
      
      const res = await app.request({
        url: `/api/wx/notice/${this.data.noticeId}/readUsers`,
        method: 'GET'
      })

      if (res.code === 0) {
        const { readUsers } = res.data
        
        // 处理时间格式
        const processedReadUsers = readUsers.map(user => ({
          ...user,
          readTimeFormatted: formatTime(user.readTime, 'MM-DD HH:mm')
        }))
        
        this.setData({
          readUsersList: processedReadUsers,
          loading: false
        })
      } else {
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        })
        this.setData({ loading: false })
      }
    } catch (error) {
      console.error('加载已读用户失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      this.setData({ loading: false })
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadAllReadUsers().then(() => {
      wx.stopPullDownRefresh()
    })
  }
})
