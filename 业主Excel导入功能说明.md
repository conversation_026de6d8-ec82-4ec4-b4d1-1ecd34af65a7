# 业主Excel导入功能实现说明

## 功能概述
为业主管理模块添加了Excel批量导入功能，支持通过Excel文件批量导入业主信息。

## 实现内容

### 1. 新增文件
- `ehome-oc/src/main/java/com/ehome/oc/domain/OwnerImport.java` - 业主导入实体类

### 2. 修改文件
- `ehome-page/src/main/resources/templates/oc/user/list-owner.html` - 前端页面
- `ehome-oc/src/main/java/com/ehome/oc/controller/assets/OcOwnerController.java` - 后端控制器

## 功能特性

### Excel模板字段
| 字段名称 | 是否必填 | 说明 |
|---------|---------|------|
| 房号 | 条件必填 | 房间号，如需绑定房屋则必填 |
| 楼栋 | 条件必填 | 楼栋名称，如需绑定房屋则必填 |
| 单元 | 条件必填 | 单元名称，如需绑定房屋则必填 |
| 所在楼层 | 否 | 楼层信息 |
| 业主姓名 | 是 | 业主的真实姓名 |
| 手机号 | 是 | 11位手机号，同一小区内不能重复 |
| 身份证号码 | 否 | 18位身份证号码 |
| 性别 | 否 | 男/女，系统自动转换为M/F |
| 家庭住址 | 否 | 业主的家庭住址 |
| 备注 | 否 | 其他备注信息 |

### 数据验证规则
1. **业主姓名**：必填，不能为空
2. **手机号**：必填，必须是11位数字且以1开头
3. **房屋信息**：如果填写房屋信息，房号、楼栋、单元必须同时填写，且房屋必须已存在
4. **手机号重复检查**：同一小区内手机号不能重复
5. **身份证格式**：如果填写，必须是18位
6. **性别转换**：男→M，女→F

### 导入选项
- **是否更新已存在数据**：勾选后，如果手机号已存在，会更新该业主的其他信息

## 使用方法

### 1. 下载模板
1. 进入业主管理页面
2. 点击"批量导入"按钮
3. 在弹出的对话框中点击"下载模板"
4. 下载Excel模板文件

### 2. 填写数据
1. 在Excel模板中填写业主信息
2. 确保必填字段（业主姓名、手机号码）不为空
3. 手机号格式正确且不重复

### 3. 导入数据
1. 点击"批量导入"按钮
2. 选择填写好的Excel文件
3. 选择是否更新已存在的数据
4. 点击确定开始导入
5. 查看导入结果

## 示例数据

| 房号 | 楼栋 | 单元 | 所在楼层 | 业主姓名 | 手机号 | 身份证号码 | 性别 | 家庭住址 | 备注 |
|------|------|------|---------|---------|--------|-----------|------|---------|------|
| 101 | 1栋 | 1单元 | 1 | 张三 | 13800138000 | 110101199001011234 | 男 | 北京市朝阳区 | 业主 |
| 201 | 2栋 | 1单元 | 2 | 李四 | 13900139000 | 110101199002022345 | 女 | 北京市海淀区 | 业主 |
|  |  |  |  | 王五 | 13700137000 |  | 男 |  | 临时业主（无房屋绑定） |

## 错误处理
- 数据格式错误：跳过该行，记录错误信息
- 手机号重复：根据更新选项决定是否更新或跳过
- 必填字段为空：跳过该行，记录错误信息
- 导入完成后显示详细的成功和失败统计

## 技术实现
- 使用ExcelUtil工具类处理Excel文件
- 支持.xls和.xlsx格式
- 数据验证使用正则表达式
- 数据库操作使用JFinal的Db工具
- 完善的错误处理和日志记录
