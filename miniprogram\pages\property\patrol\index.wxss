/* 巡更任务列表页面样式 */
.patrol-container {
  padding: 20rpx;
  background-color: #f7f8fa;
  min-height: 100vh;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  color: #999;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
}

/* 统计卡片区域 */
.stats-cards {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stats-card {
  flex: 1;
  padding: 30rpx 24rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  position: relative;
  overflow: hidden;
}

.stats-card.orange {
  background: linear-gradient(135deg, #ff7849 0%, #ff9a56 100%);
}

.stats-card.blue {
  background: linear-gradient(135deg, #4285f4 0%, #5a9cff 100%);
}

.stats-content {
  flex: 1;
}

.stats-title {
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 8rpx;
}

.stats-value {
  font-size: 36rpx;
  font-weight: 600;
}

.stats-action {
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 区域标题 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

/* 今日任务区域 */
.today-section {
  margin-bottom: 40rpx;
}

.today-task-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-progress {
  flex: 1;
}

.progress-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  margin-bottom: 12rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1989fa 0%, #4dabf7 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-ratio {
  font-size: 24rpx;
  color: #666;
}

.start-patrol-btn {
  padding: 16rpx 32rpx;
  background: #1989fa;
  color: white;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin-left: 20rpx;
}

/* 往日任务区域 */
.history-section {
  margin-bottom: 40rpx;
}

.history-item {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
}

.history-date {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.date-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-left: 8rpx;
}

.history-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

.stat-value {
  font-size: 26rpx;
  font-weight: 500;
}

.stat-value.completed {
  color: #52c41a;
}

.stat-value.pending {
  color: #fa8c16;
}

.history-action {
  padding: 8rpx 16rpx;
  background: #f7f8fa;
  color: #1989fa;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 20rpx;
}
