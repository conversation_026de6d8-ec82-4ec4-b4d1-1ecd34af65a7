# 车牌识别功能快速部署指南

## 快速部署步骤

### 1. 配置百度AI服务（5分钟）

1. 访问 [百度AI开放平台](https://ai.baidu.com/)
2. 注册登录 → 控制台 → 创建应用 → 选择"车牌识别"
3. 获取密钥信息：APP_ID、API_KEY、SECRET_KEY

### 2. 更新配置文件（2分钟）

编辑 `ehome-web/src/main/resources/application.yml`：

```yaml
# 百度AI配置
baidu:
  ai:
    enabled: true
    app-id: 你的APP_ID
    api-key: 你的API_KEY  
    secret-key: 你的SECRET_KEY
```

### 3. 执行数据库脚本（2分钟）

```bash
# 执行优化脚本
mysql -u username -p database_name < sql/vehicle_recognition_optimization.sql

# 执行识别记录表脚本
mysql -u username -p database_name < sql/plate_recognition_log.sql

# 或者手动执行关键SQL
ALTER TABLE eh_vehicle ADD INDEX idx_plate_no (plate_no);
ALTER TABLE eh_vehicle ADD INDEX idx_community_plate (community_id, plate_no);
```

### 4. 添加菜单配置（3分钟）

**方法一：通过后台管理界面**
- 菜单管理 → 新增菜单
- 菜单名称：`车牌识别挪车`
- 菜单类型：`page`
- 点击方法：`goToVehicleRecognize`
- 图标：`scan`

**方法二：直接执行SQL**
```sql
INSERT INTO eh_wx_nav (nav_id, parent_id, nav_name, nav_type, tap_name, icon_name, sort_order, status, create_time, update_time, remark, community_id) 
VALUES ('nav_vehicle_recognize', '0', '车牌识别挪车', 'page', 'goToVehicleRecognize', 'scan', 100, '0', NOW(), NOW(), '车牌识别挪车功能', '你的社区ID');
```

### 5. 重启应用（1分钟）

```bash
# 重启应用使配置生效
systemctl restart ehome
# 或者
./restart.sh
```

## 功能测试

### 测试步骤

1. **登录小程序** → 进入导航页面
2. **点击"车牌识别挪车"** → 进入识别页面
3. **拍照或选择图片** → 自动上传到统一文件接口
4. **等待识别完成** → 系统自动调用百度AI识别
5. **查看识别结果** → 确认车牌号和车主信息
6. **测试拨号功能** → 点击电话号码

### 技术流程

1. **统一上传**: 使用 `feedbackManager` 统一上传组件处理图片
2. **文件管理**: 自动压缩、格式验证、大小检查
3. **获取文件ID**: 上传成功后返回 `fileId` 并保存到 `eh_file_info` 表
4. **车牌识别**: 调用 `/api/wx/vehicle/recognizePlate` 传递 `fileId`
5. **读取文件**: 后端根据 `fileId` 从数据库获取文件路径
6. **AI识别**: 调用百度AI服务识别车牌
7. **查询车主**: 根据车牌号查询车主信息
8. **保存记录**: 将识别过程和结果保存到 `eh_plate_recognition_log` 表

### 测试数据准备

确保数据库中有测试车辆数据：

```sql
-- 插入测试车辆数据
INSERT INTO eh_vehicle (vehicle_id, community_id, plate_no, owner_real_name, owner_phone, house_name, parking_space, check_status) 
VALUES 
('test_vehicle_001', '你的社区ID', '京A12345', '张三', '13800138000', '1号楼101室', 'A-001', 1),
('test_vehicle_002', '你的社区ID', '京B67890', '李四', '13900139000', '2号楼201室', 'B-002', 1);
```

## 故障排除

### 常见问题

❌ **"百度AI服务未启用"**
- 检查 `application.yml` 配置
- 确认 `enabled: true`

❌ **"未找到车主信息"**  
- 检查数据库中是否有对应车牌数据
- 确认 `check_status = 1`（已审核）

❌ **"页面跳转失败"**
- 检查 `app.json` 中是否添加了页面路径
- 确认页面文件是否存在

❌ **"识别准确率低"**
- 确保图片清晰、光线充足
- 车牌完整可见、角度适中

### 调试方法

1. **查看后端日志**：
```bash
tail -f logs/ehome.log | grep -i "plate\|vehicle"
```

2. **检查API调用**：
```bash
# 测试车牌识别接口
curl -X POST -F "file=@test_image.jpg" \
  -H "Authorization: Bearer your_token" \
  http://your_domain/api/wx/vehicle/recognizePlate
```

3. **验证数据库**：
```sql
-- 检查车辆数据
SELECT * FROM eh_vehicle WHERE plate_no = '京A12345';

-- 检查菜单配置  
SELECT * FROM eh_wx_nav WHERE tap_name = 'goToVehicleRecognize';
```

## 成本控制

### 百度AI调用优化

- **免费额度**：每月1000次
- **监控用量**：在百度AI控制台查看调用统计
- **缓存策略**：相同图片短时间内不重复识别

### 建议设置

```yaml
# 可选：添加调用限制配置
baidu:
  ai:
    enabled: true
    app-id: your_app_id
    api-key: your_api_key
    secret-key: your_secret_key
    # 每日调用限制（可选）
    daily-limit: 100
    # 缓存时间（秒，可选）
    cache-duration: 300
```

## 后台管理功能

### 查看识别记录

1. **访问路径**: 系统管理 → 车牌识别记录
2. **功能特点**:
   - 查看所有识别记录
   - 按条件筛选（车牌号、用户、时间等）
   - 查看详细信息
   - 统计数据展示

### 菜单配置

在后台菜单管理中添加：
- **菜单名称**: 车牌识别记录
- **请求地址**: /oc/plateRecognitionLog/mgr
- **父级菜单**: 资产管理

## 完成检查清单

- [ ] 百度AI密钥配置完成
- [ ] 数据库索引添加完成
- [ ] 识别记录表创建完成
- [ ] 菜单配置添加完成
- [ ] 应用重启完成
- [ ] 功能测试通过
- [ ] 测试数据准备完成
- [ ] 后台管理页面可访问

完成以上步骤后，车牌识别挪车功能即可正常使用！
