# combina_name字段更新说明

## 需求背景
根据用户要求，需要更新房屋表（eh_house_info）的combina_name字段，格式为：**楼栋-单元-楼层**

## 字段分析

### 相关字段
- `building_name` - 楼栋名称（如：1栋）
- `unit_name` - 单元名称（如：1单元）
- `floor` - 楼层（如：1）
- `combina_name` - 房屋全称（目标字段）

### 期望格式
```
1栋-1单元-1
2栋-2单元-3
A栋-A单元-5
```

## SQL实现

### 推荐方案（处理空值）
```sql
UPDATE eh_house_info 
SET combina_name = CONCAT(
    IFNULL(building_name, ''), 
    '-', 
    IFNULL(unit_name, ''), 
    '-', 
    IFNULL(floor, '')
),
update_time = NOW(),
update_by = 'system'
WHERE building_name IS NOT NULL;
```

### 保守方案（只更新空值）
```sql
UPDATE eh_house_info 
SET combina_name = CONCAT(
    IFNULL(building_name, ''), 
    '-', 
    IFNULL(unit_name, ''), 
    '-', 
    IFNULL(floor, '')
),
update_time = NOW(),
update_by = 'system'
WHERE (combina_name IS NULL OR combina_name = '')
  AND building_name IS NOT NULL;
```

## 执行步骤

### 1. 备份数据（推荐）
```sql
-- 创建备份表
CREATE TABLE eh_house_info_backup AS 
SELECT * FROM eh_house_info;
```

### 2. 查看当前状态
```sql
SELECT 
    house_id,
    building_name,
    unit_name,
    floor,
    combina_name,
    CONCAT(
        IFNULL(building_name, ''), 
        '-', 
        IFNULL(unit_name, ''), 
        '-', 
        IFNULL(floor, '')
    ) AS expected_combina_name
FROM eh_house_info 
WHERE building_name IS NOT NULL
LIMIT 10;
```

### 3. 执行更新
```sql
UPDATE eh_house_info 
SET combina_name = CONCAT(
    IFNULL(building_name, ''), 
    '-', 
    IFNULL(unit_name, ''), 
    '-', 
    IFNULL(floor, '')
),
update_time = NOW(),
update_by = 'system'
WHERE building_name IS NOT NULL;
```

### 4. 验证结果
```sql
SELECT 
    COUNT(*) as total_updated,
    COUNT(CASE WHEN combina_name LIKE '%-%-%' THEN 1 END) as full_format_count
FROM eh_house_info 
WHERE building_name IS NOT NULL;
```

## 处理场景

### 场景1：完整信息
- building_name: "1栋"
- unit_name: "1单元"
- floor: "1"
- **结果**: "1栋-1单元-1"

### 场景2：缺少楼层
- building_name: "1栋"
- unit_name: "1单元"
- floor: NULL
- **结果**: "1栋-1单元-"

### 场景3：缺少单元
- building_name: "1栋"
- unit_name: NULL
- floor: "1"
- **结果**: "1栋--1"

### 场景4：仅楼栋
- building_name: "1栋"
- unit_name: NULL
- floor: NULL
- **结果**: "1栋--"

## 注意事项

### 1. 数据安全
- 建议先备份数据
- 可以先在测试环境执行
- 使用事务确保数据一致性

### 2. 性能考虑
- 大表更新可能耗时较长
- 建议在业务低峰期执行
- 可以分批更新减少锁定时间

### 3. 数据质量
- 检查building_name字段的数据质量
- 确认NULL值的处理是否符合预期
- 验证更新后的格式是否正确

## 回滚方案

如果需要回滚，可以使用备份表：
```sql
UPDATE eh_house_info h1
JOIN eh_house_info_backup h2 ON h1.house_id = h2.house_id
SET h1.combina_name = h2.combina_name,
    h1.update_time = h2.update_time,
    h1.update_by = h2.update_by;
```

## 总结
该SQL更新方案能够安全有效地更新combina_name字段，格式为"楼栋-单元-楼层"，同时处理了各种空值情况，确保数据的完整性和一致性。
