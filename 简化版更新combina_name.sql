-- 简化版：更新房屋表的combina_name字段
-- 格式：楼栋-单元-楼层

-- 推荐执行的SQL（处理各种情况）
UPDATE eh_house_info 
SET combina_name = CONCAT(
    IFNULL(building_name, ''), 
    '-', 
    IFNULL(unit_name, ''), 
    '-', 
    IFNULL(floor, '')
),
update_time = NOW(),
update_by = 'system'
WHERE building_name IS NOT NULL;

-- 如果只想更新空的combina_name字段
UPDATE eh_house_info 
SET combina_name = CONCAT(
    IFNULL(building_name, ''), 
    '-', 
    IFNULL(unit_name, ''), 
    '-', 
    IFNULL(floor, '')
),
update_time = NOW(),
update_by = 'system'
WHERE (combina_name IS NULL OR combina_name = '')
  AND building_name IS NOT NULL;
