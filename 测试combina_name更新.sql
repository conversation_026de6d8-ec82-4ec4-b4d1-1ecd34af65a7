-- 测试combina_name更新SQL

-- 1. 先查看当前数据状态
SELECT 
    house_id,
    building_name,
    unit_name,
    floor,
    combina_name,
    CONCAT(
        IFNULL(building_name, ''), 
        '-', 
        IFNULL(unit_name, ''), 
        '-', 
        IFNULL(floor, '')
    ) AS expected_combina_name
FROM eh_house_info 
WHERE building_name IS NOT NULL
LIMIT 5;

-- 2. 执行更新
UPDATE eh_house_info 
SET combina_name = CONCAT(
    IFNULL(building_name, ''), 
    '-', 
    IFNULL(unit_name, ''), 
    '-', 
    IFNULL(floor, '')
),
update_time = NOW(),
update_by = 'system'
WHERE building_name IS NOT NULL;

-- 3. 验证更新结果
SELECT 
    house_id,
    building_name,
    unit_name,
    floor,
    combina_name,
    update_time
FROM eh_house_info 
WHERE building_name IS NOT NULL
LIMIT 5;

-- 4. 统计更新情况
SELECT 
    COUNT(*) as total_updated,
    COUNT(CASE WHEN combina_name LIKE '%-%-%' THEN 1 END) as full_format_count,
    COUNT(CASE WHEN combina_name LIKE '%-%' AND combina_name NOT LIKE '%-%-%' THEN 1 END) as partial_format_count
FROM eh_house_info 
WHERE building_name IS NOT NULL;
