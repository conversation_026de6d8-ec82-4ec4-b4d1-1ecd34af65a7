package com.ehome.admin.controller.file;

import com.ehome.admin.domain.FileUploadResult;
import com.ehome.common.config.RuoYiConfig;
import com.ehome.common.config.ServerConfig;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.service.OssService;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.ServletUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.file.FileUploadUtils;
import com.ehome.common.utils.file.FileUtils;
import com.ehome.common.utils.uuid.Seq;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件处理控制器
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping({"/common","/attachment"})
public class FileController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(FileController.class);

    @Autowired
    private ServerConfig serverConfig;
    
    @Autowired
    private OssService ossService;

    private static final String FILE_DELIMETER = ",";
    
    /**
     * 处理单个文件上传的公共方法
     * 
     * @param file 上传的文件
     * @param source 文件来源
     * @param isPublic 是否上传到公共Bucket
     * @return 文件上传结果
     * @throws Exception
     */
    private FileUploadResult processFileUpload(MultipartFile file, String source,String businessId,boolean isPublic,String folderId,String communityId) throws Exception {
        // 上传文件路径
        String filePath = RuoYiConfig.getUploadPath();
        if (!StringUtils.isEmpty(source)) {
            filePath = filePath + File.separator + source;
        }
        
        // 先生成fileId，确保文件名和数据库记录使用同一个ID
        String fileId = Seq.getId(Seq.uploadSeqType);
        String accessUrl = serverConfig.getUrl() + "/fv/download/";

        // 使用指定的fileId生成文件名并上传
        String extractedFileName = FileUploadUtils.extractFilename(file, fileId);
        File absoluteFile = FileUploadUtils.getAbsoluteFile(filePath, extractedFileName);
        String absolutePath = absoluteFile.getAbsolutePath();

        // 手动保存文件，而不是使用FileUploadUtils.upload()避免重复生成fileId
        file.transferTo(absoluteFile.toPath());
        String fileName = FileUploadUtils.getPathFileName(filePath, extractedFileName);

        // 默认存储类型为local，总是先上传到本地
        String storageType = "local";
        String ossUrl = null;
        String ossKey = null;

        // 尝试上传到OSS
        if (ossService.isAvailable()) {
            try {
                OssService.UploadResult ossResult = ossService.uploadFile(file,fileId,source, isPublic);
                if (ossResult.isSuccess()) {
                    ossUrl = ossResult.getUrl();
                    ossKey = ossResult.getObjectKey();
                    storageType = "both"; // 本地和OSS都有
                    log.info("文件同步上传到OSS成功: {} -> {} ({})", file.getOriginalFilename(), ossKey, isPublic ? "公共Bucket" : "私有Bucket");
                } else {
                    log.warn("文件上传到OSS失败: {}", ossResult.getErrorMessage());
                }
            } catch (Exception ossException) {
                log.error("OSS上传异常: {}", ossException.getMessage(), ossException);
            }
        }
        
        // 创建上传结果
        FileUploadResult result = new FileUploadResult(
            fileName, accessUrl, absolutePath, ossUrl, ossKey, storageType,
            file.getOriginalFilename(), file.getSize(),
            FileUploadUtils.getExtension(file), file.getContentType()
        );
        result.setBusinessId(businessId);
        result.setBusinessType(source);

        // 设置文件夹和社区参数
        String folderIdParam = getRequest().getParameter("folderId");
        String communityIdParam = getRequest().getParameter("communityId");
        result.setFolderId(folderIdParam);
        result.setCommunityId(communityIdParam);
        result.setFileId(fileId);

        // 保存文件信息到数据库
        this.saveFileInfoToDatabase(result);

        // 方便PC和小程序直接访问
        result.setAccessUrl(result.getAccessUrl()+result.getFileId());

        return result;
    }
    
    /**
     * 保存文件信息到数据库
     *
     * @param result 文件上传结果
     */
    private String saveFileInfoToDatabase(FileUploadResult result) {
        String fileId = null;
        try {
            String folderId = result.getFolderId();
            String communityId = result.getCommunityId();

            // 获取文件夹编码
            String folderCode = "";
            if (StringUtils.isNotEmpty(folderId)) {
                Record folder = Db.findFirst("select folder_code from eh_file_folder where folder_id = ? and status = '0'", folderId);
                if (folder != null) {
                    folderCode = folder.getStr("folder_code");
                }
            }

            fileId = result.getFileId();
            result.setFileId(fileId);
            Record fileRecord = new Record();
            fileRecord.set("file_id",fileId);
            fileRecord.set("original_name", result.getOriginalFilename());
            fileRecord.set("file_name", FileUtils.getName(result.getFileName()));
            fileRecord.set("file_path", result.getFileName());
            fileRecord.set("absolute_path", result.getAbsolutePath());
            fileRecord.set("access_url", result.getAccessUrl()+fileId);
            fileRecord.set("oss_url", result.getOssUrl());
            fileRecord.set("oss_key", result.getOssKey());
            fileRecord.set("storage_type", result.getStorageType());
            fileRecord.set("file_size", result.getFileSize());
            fileRecord.set("file_size_str", FileUtils.formatFileSize(result.getFileSize()));
            fileRecord.set("file_type", result.getFileType());
            fileRecord.set("mime_type", result.getMimeType());
            fileRecord.set("upload_user", getSysUser() != null ? getSysUser().getLoginName() : "anonymous");
            fileRecord.set("folder_id", StringUtils.isNotEmpty(folderId) ? folderId : "");
            fileRecord.set("folder_code", folderCode);
            fileRecord.set("community_id", StringUtils.isNotEmpty(communityId) ? communityId : (getSysUser() != null ? getSysUser().getCommunityId() : ""));
            fileRecord.set("download_count", 0);
            fileRecord.set("last_download_time", "");
            fileRecord.set("create_time", DateUtils.getTime());
            fileRecord.set("update_time", DateUtils.getTime());
            fileRecord.set("status", "0");
            fileRecord.set("business_type", result.getBusinessType());
            fileRecord.set("business_id", result.getBusinessId());

            Db.save("eh_file_info", "file_id", fileRecord);
        } catch (Exception dbException) {
            logger.error("保存文件信息到数据库失败: " + dbException.getMessage(), dbException);
        }
        return fileId;
    }


    /**
     * 通用上传请求（单个）
     * PC端使用
     */
    @PostMapping("/upload")
    @ResponseBody
    public AjaxResult uploadFile(MultipartFile file) throws Exception
    {
        try
        {
            String source = getRequest().getParameter("source");
            String folderId = getRequest().getParameter("folderId");
            String communityId = getRequest().getParameter("communityId");
            String businessId = getRequest().getParameter("businessId");
            if("logo".equals(source)){
                businessId = getSysUser().getCommunityId();
            }
            String bucketType = getRequest().getParameter("bucketType");
            boolean isPublic = "public".equalsIgnoreCase(bucketType);
            
            // 使用公共方法处理文件上传
            FileUploadResult result = processFileUpload(file, source,businessId,isPublic,folderId,communityId);

            AjaxResult ajax = AjaxResult.success();
            ajax.putAll(result.getJson());
            ajax.put("isUpload",1);
            return ajax;
        }
        catch (Exception e)
        {
            logger.error(e.getMessage(), e);
            return AjaxResult.error(e.getMessage(),e);
        }
    }

    /**
     * 通用上传请求（多个）
     */
    @PostMapping("/uploads")
    @ResponseBody
    public AjaxResult uploadFiles(List<MultipartFile> files) throws Exception
    {
        try
        {
            String source = getRequest().getParameter("source");
            String folderId = getRequest().getParameter("folderId");
            String communityId = getRequest().getParameter("communityId");
            String businessId = getRequest().getParameter("businessId");
            String bucketType = getRequest().getParameter("bucketType");
            boolean isPublic = "public".equalsIgnoreCase(bucketType);

            List<String> urls = new ArrayList<String>();
            List<String> ids = new ArrayList<String>();
            List<String> fileNames = new ArrayList<String>();
            List<String> newFileNames = new ArrayList<String>();
            List<String> originalFilenames = new ArrayList<String>();

            for (MultipartFile file : files)
            {
                // 使用公共方法处理文件上传
                FileUploadResult result = processFileUpload(file, source, businessId,isPublic,folderId,communityId);
                ids.add(result.getFileId());
                urls.add(result.getFinalUrl());
                fileNames.add(result.getFileName());
                newFileNames.add(FileUtils.getName(result.getFileName()));
                originalFilenames.add(result.getOriginalFilename());
            }
            
            AjaxResult ajax = AjaxResult.success();
            ajax.put("code",0);
            ajax.put("fileIds", StringUtils.join(ids, FILE_DELIMETER));
            ajax.put("urls", StringUtils.join(urls, FILE_DELIMETER));
            ajax.put("fileNames", StringUtils.join(fileNames, FILE_DELIMETER));
            ajax.put("newFileNames", StringUtils.join(newFileNames, FILE_DELIMETER));
            ajax.put("originalFilenames", StringUtils.join(originalFilenames, FILE_DELIMETER));
            return ajax;
        }
        catch (Exception e)
        {
            logger.error(e.getMessage(), e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 根据OSS Key生成预签名访问URL
     */
    @PostMapping("/oss/generateUrl")
    @ResponseBody
    public AjaxResult generateOssUrl(String ossKey, Long expiration) {
        try {
            if (StringUtils.isEmpty(ossKey)) {
                return AjaxResult.error("OSS Key不能为空");
            }

            if (!ossService.isAvailable()) {
                return AjaxResult.error("OSS服务不可用");
            }

            String url = ossService.generatePresignedUrl(null,ossKey, expiration);
            if (StringUtils.isEmpty(url)) {
                return AjaxResult.error("生成预签名URL失败");
            }

            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("ossKey", ossKey);
            ajax.put("expiration", expiration != null ? expiration : ossService.getUrlExpiration());
            return ajax;

        } catch (Exception e) {
            logger.error("生成OSS预签名URL失败", e);
            return AjaxResult.error("生成预签名URL失败: " + e.getMessage());
        }
    }


    /**
     * 文件下载接口 - 通过文件ID下载文件
     */
    @GetMapping("/download/{fileId}")
    public void downloadFileById(@PathVariable String fileId, HttpServletResponse response) {
        try {
            if (StringUtils.isEmpty(fileId)) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "文件ID不能为空");
                return;
            }

            // 查询文件信息
            Record fileInfo = Db.findFirst(
                "SELECT file_id, original_name, file_path, access_url, oss_url, oss_key, storage_type, mime_type,absolute_path " +
                "FROM eh_file_info WHERE file_id = ? AND status = '0'", fileId);

            if (fileInfo == null) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件不存在或已删除");
                return;
            }

            String storageType = fileInfo.getStr("storage_type");
            String ossKey = fileInfo.getStr("oss_key");
            String filePath = fileInfo.getStr("file_path");
            String absolutePath = fileInfo.getStr("absolute_path");
            String originalName = fileInfo.getStr("original_name");
            String mimeType = fileInfo.getStr("mime_type");
            String ossUrl = fileInfo.getStr("oss_url");

            // 设置响应头
            response.setContentType(StringUtils.isNotEmpty(mimeType) ? mimeType : MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, originalName);

            // 根据存储类型处理下载
            if (("both".equals(storageType) || "oss".equals(storageType)) &&
                StringUtils.isNotEmpty(ossKey) && ossService.isAvailable()) {
                // 重定向到OSS预签名URL
                try {
                    String newOssUrl = ossService.generatePresignedUrl(ossUrl,ossKey, 3600L); // 1小时有效期
                    if (StringUtils.isNotEmpty(newOssUrl)) {
                        response.sendRedirect(newOssUrl);
                        // 更新下载统计
                        this.updateDownloadStats(fileId, "sys_user");
                        return;
                    }
                } catch (Exception ossException) {
                    log.warn("OSS下载重定向失败，降级到本地文件: {} - {}", fileId, ossException.getMessage());
                }
            }

            // 本地文件下载 - 兼容不同环境的路径
            if (StringUtils.isNotEmpty(absolutePath)) {
                try {
                    String fullPath = absolutePath;

                    // 检查文件是否存在
                    File file = new File(fullPath);
                    if (file.exists() && file.isFile()) {
                        FileUtils.writeBytes(fullPath, response.getOutputStream());
                        // 更新下载统计
                        updateDownloadStats(fileId, "sys_user");
                    } else {
                        // 文件不存在，尝试通过access_url重定向
                        String fileUrl = fileInfo.getStr("access_url");
                        if (StringUtils.isNotEmpty(fileUrl)) {
                            String encodeRedirectUrl = encodeRedirectUrl(fileUrl);
                            log.warn("本地文件不存在，重定向到access_url: {} -> {},{}", fullPath, fileUrl,encodeRedirectUrl);
                            response.sendRedirect(encodeRedirectUrl);
                            return;
                        } else {
                            response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件不存在: " + fullPath);
                        }
                    }
                } catch (Exception e) {
                    log.error("本地文件访问失败: {}", absolutePath, e);
                    // 降级到access_url重定向
                    String fileUrl = fileInfo.getStr("access_url");
                    if (StringUtils.isNotEmpty(fileUrl)) {
                        String encodeRedirectUrl = encodeRedirectUrl(fileUrl);
                        log.warn("本地文件访问异常，重定向到access_url: {},{}", fileUrl,encodeRedirectUrl);
                        response.sendRedirect(encodeRedirectUrl);
                    } else {
                        response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "文件访问失败");
                    }
                }
            } else {
                // 没有file_path，尝试使用access_url
                String fileUrl = fileInfo.getStr("access_url");
                if (StringUtils.isNotEmpty(fileUrl)) {
                    log.info("使用access_url进行重定向: {}", fileUrl);
                    response.sendRedirect(encodeRedirectUrl(fileUrl));
                } else {
                    response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件路径和URL都不存在");
                }
            }

        } catch (Exception e) {
            log.error("文件下载失败: {}", fileId, e);
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "文件下载失败");
            } catch (Exception ignored) {
                logger.error("文件下载失败: {}", fileId, e);
            }
        }
    }


    /**
     * 确保URL是完整的HTTP地址
     */
    private String ensureFullUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        if (url.startsWith("http://") || url.startsWith("https://")) {
            return url;
        }
        return serverConfig.getUrl() + url;
    }

    /**
     * 更新文件下载统计
     *
     * @param fileId 文件ID
     * @param userType 用户类型
     */
    private void updateDownloadStats(String fileId, String userType) {
        try {
            String currentTime = DateUtils.getTime();
            // 记录下载日志
            Record logRecord = new Record();
            logRecord.set("file_id", fileId);
            logRecord.set("user_id", getSysUser() != null ? getSysUser().getUserId() : "");
            logRecord.set("user_name", getSysUser() != null ? getSysUser().getLoginName() : "anonymous");
            logRecord.set("user_type", userType);
            logRecord.set("download_ip", getRequest().getRemoteAddr());
            logRecord.set("user_agent", getRequest().getHeader("User-Agent"));
            logRecord.set("download_time", currentTime);
            logRecord.set("status", "0");
            logRecord.set("error_msg", "");
            logRecord.set("pms_id", getSysUser() != null ? getSysUser().getPmsId() : "");
            logRecord.set("community_id", getSysUser() != null ? getSysUser().getCommunityId() : "");
            logRecord.set("owner_id", "");
            logRecord.set("house_id", "");
            logRecord.set("house_name", "");

           if(Db.save("eh_file_download_log","log_id",logRecord)){
               // 更新文件下载次数和最后下载时间
               Db.update("UPDATE eh_file_info SET download_count = download_count + 1, last_download_time = ? WHERE file_id = ?", currentTime, fileId);
           }
        } catch (Exception e) {
            logger.error("更新文件下载统计失败: {}", e.getMessage(), e);
        }
    }


    /**
     * 对重定向URL进行编码处理，避免中文字符导致的HTTP头错误
     */
    private String encodeRedirectUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        try {
            // 若存在非ASCII字符
            if (!StandardCharsets.US_ASCII.newEncoder().canEncode(url)) {
                int schemeIdx = url.indexOf("://");
                if (schemeIdx > 0) {
                    int pathIdx = url.indexOf('/', schemeIdx + 3);
                    if (pathIdx > 0) {
                        String base = url.substring(0, pathIdx);
                        String encodedPath = Arrays.stream(url.substring(pathIdx).split("/"))
                                .filter(part -> !part.isEmpty())
                                .map(ServletUtils::urlEncode)
                                .collect(Collectors.joining("/", "/", ""));
                        return base + encodedPath;
                    }
                }
                // 非完整URL或不含路径部分
                return ServletUtils.urlEncode(url);
            }
            return url;
        } catch (Exception e) {
            logger.warn("URL编码失败，使用原始URL: {} - {}", url, e.getMessage());
            return url;
        }
    }


    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete 是否删除
     */
    @GetMapping("/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            if (!FileUtils.checkAllowDownload(fileName))
            {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = RuoYiConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(filePath);
            }
        }
        catch (Exception e)
        {
            logger.error("下载文件失败", e);
        }
    }
}
